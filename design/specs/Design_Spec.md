# 静息(Serenity) iOS应用 - 设计规范文档

## 1. 设计概述

### 1.1 设计理念
静息(Serenity)应用的设计理念围绕"平静、简洁、专注"展开，旨在为用户创造一个远离喧嚣、回归内心的数字空间。设计语言强调：

- **极简主义**：去除不必要的视觉干扰，让用户专注于呼吸和冥想
- **自然感**：运用自然元素和有机形状，营造舒缓的视觉体验
- **一致性**：统一的设计语言确保用户在不同功能间的流畅体验
- **可访问性**：考虑不同用户群体的使用需求，确保界面易用性

### 1.2 目标平台
- **主要平台**：iOS (iPhone)
- **设计标准**：遵循 Apple Human Interface Guidelines
- **屏幕适配**：iPhone 12/13/14 系列标准尺寸 (375×812pt)
- **系统版本**：iOS 15.0+

## 2. 颜色系统

### 2.1 主色调

#### 深蓝色 (Primary Blue)
- **色值**：#1A2151
- **用途**：主背景色、导航栏、卡片背景
- **寓意**：深邃、宁静、专业

#### 青绿色 (Primary Teal)
- **色值**：#2EC4B6
- **用途**：强调色、按钮、进度指示、图标高亮
- **寓意**：清新、活力、成长

#### 深紫色 (Primary Purple)
- **色值**：#4D4C7D
- **用途**：渐变色、装饰元素、次要强调
- **寓意**：神秘、智慧、冥想

### 2.2 中性色

#### 深灰色 (Dark Gray)
- **色值**：#2E2E3A
- **用途**：次要背景、卡片分割

#### 中灰色 (Medium Gray)
- **色值**：#6B7280
- **用途**：次要文本、图标

#### 浅灰色 (Light Gray)
- **色值**：#F3F4F6
- **用途**：浅色模式背景（未来扩展）

#### 纯白色
- **色值**：#FFFFFF
- **用途**：主要文本、图标、按钮文字

### 2.3 功能色

#### 成功色 (Success)
- **色值**：#10B981 (绿色)
- **用途**：完成状态、成功提示

#### 警告色 (Warning)
- **色值**：#F59E0B (黄色)
- **用途**：提醒、注意事项

#### 错误色 (Error)
- **色值**：#EF4444 (红色)
- **用途**：错误提示、删除操作

#### 信息色 (Info)
- **色值**：#3B82F6 (蓝色)
- **用途**：信息提示、链接

## 3. 字体系统

### 3.1 字体族
- **主字体**：SF Pro Display / SF Pro Text (iOS系统字体)
- **备用字体**：-apple-system, BlinkMacSystemFont, system-ui, sans-serif

### 3.2 字体层级

#### 标题层级
- **H1 大标题**：32pt, 粗体 (Bold), 用于页面主标题
- **H2 中标题**：24pt, 粗体 (Bold), 用于区块标题
- **H3 小标题**：20pt, 中等 (Medium), 用于卡片标题
- **H4 子标题**：18pt, 中等 (Medium), 用于列表项标题

#### 正文层级
- **正文大**：16pt, 常规 (Regular), 用于主要内容
- **正文中**：14pt, 常规 (Regular), 用于次要内容
- **正文小**：12pt, 常规 (Regular), 用于辅助信息

#### 特殊用途
- **按钮文字**：16pt, 中等 (Medium)
- **导航标签**：10pt, 中等 (Medium)
- **时间数字**：48pt, 粗体 (Bold), 用于练习计时
- **统计数字**：24pt, 粗体 (Bold), 用于数据展示

### 3.3 行高规范
- **标题行高**：字号 × 1.2
- **正文行高**：字号 × 1.5
- **按钮行高**：字号 × 1.3

## 4. 布局系统

### 4.1 网格系统
- **容器宽度**：375pt (iPhone标准宽度)
- **内容边距**：24pt (左右各24pt)
- **卡片间距**：16pt (垂直间距)
- **元素间距**：8pt, 12pt, 16pt, 24pt, 32pt (8pt基础单位)

### 4.2 组件尺寸

#### 按钮规范
- **主要按钮**：高度48pt, 圆角24pt, 最小宽度120pt
- **次要按钮**：高度40pt, 圆角20pt
- **图标按钮**：40×40pt, 圆角20pt
- **浮动按钮**：56×56pt, 圆角28pt

#### 卡片规范
- **标准卡片**：圆角24pt, 内边距16pt
- **大卡片**：圆角32pt, 内边距24pt
- **列表项**：高度64pt, 圆角16pt, 内边距16pt

#### 输入框规范
- **文本输入框**：高度48pt, 圆角16pt, 内边距16pt
- **搜索框**：高度40pt, 圆角20pt, 内边距12pt

### 4.3 安全区域
- **顶部安全区域**：44pt (状态栏)
- **底部安全区域**：34pt (Home指示器)
- **导航栏高度**：44pt
- **标签栏高度**：83pt (包含安全区域)

## 5. 图标系统

### 5.1 图标库
- **主要图标库**：FontAwesome 6.4.0
- **图标风格**：线性图标为主，填充图标为辅
- **图标尺寸**：16pt, 20pt, 24pt, 32pt

### 5.2 核心图标
- **首页**：fas fa-home
- **呼吸**：fas fa-wind
- **冥想**：fas fa-spa
- **睡眠**：fas fa-moon
- **个人**：fas fa-user
- **设置**：fas fa-cog
- **播放**：fas fa-play
- **暂停**：fas fa-pause
- **时钟**：fas fa-clock

### 5.3 图标使用规范
- **导航图标**：24pt, 激活状态使用主色调
- **功能图标**：20pt, 使用中性色或主色调
- **装饰图标**：16pt, 使用低透明度

## 6. 动效系统

### 6.1 过渡动画
- **页面切换**：0.3s ease-in-out
- **模态弹出**：0.25s ease-out
- **按钮反馈**：0.15s ease-in-out
- **加载状态**：1.5s linear infinite

### 6.2 呼吸动画
- **呼吸周期**：8秒 (4秒吸气 + 4秒呼气)
- **缩放范围**：1.0 - 1.3
- **透明度变化**：0.6 - 1.0
- **缓动函数**：ease-in-out

### 6.3 微交互
- **按钮点击**：轻微缩放 (0.95)
- **卡片悬停**：轻微上浮 (2pt阴影)
- **成就解锁**：发光效果 + 缩放

## 7. 组件规范

### 7.1 导航组件

#### 顶部导航栏
- **高度**：44pt + 安全区域
- **背景**：透明或半透明
- **标题**：居中，20pt粗体
- **按钮**：40×40pt圆形按钮

#### 底部标签栏
- **高度**：49pt + 安全区域
- **背景**：半透明毛玻璃效果
- **图标**：24pt，激活状态使用主色调
- **文字**：10pt，激活状态使用主色调

### 7.2 内容组件

#### 练习卡片
- **最小高度**：120pt
- **圆角**：24pt
- **背景**：半透明白色 (rgba(255,255,255,0.1))
- **内边距**：16pt
- **阴影**：轻微阴影增强层次

#### 统计卡片
- **高度**：80pt
- **圆角**：16pt
- **数字**：24pt粗体，主色调
- **标签**：12pt，中性色

### 7.3 交互组件

#### 呼吸引导圆圈
- **最大尺寸**：320pt
- **最小尺寸**：240pt
- **边框**：2pt，半透明白色
- **背景**：渐变色，半透明
- **动画**：平滑缩放，8秒周期

#### 进度指示器
- **环形进度条**：3pt线宽，主色调
- **线性进度条**：4pt高度，圆角2pt
- **百分比文字**：16pt中等字重

## 8. 响应式设计

### 8.1 屏幕适配
- **iPhone SE (375×667)**：紧凑布局，减少间距
- **iPhone 12/13/14 (375×812)**：标准布局
- **iPhone 12/13/14 Pro Max (414×896)**：扩展布局，增加内容密度

### 8.2 横屏适配
- **呼吸练习页面**：支持横屏，圆圈居中显示
- **其他页面**：锁定竖屏方向

## 9. 可访问性

### 9.1 颜色对比度
- **主要文本**：对比度 ≥ 4.5:1
- **大文本**：对比度 ≥ 3:1
- **图标**：对比度 ≥ 3:1

### 9.2 字体大小
- **支持动态字体**：跟随系统字体大小设置
- **最小字体**：12pt (确保可读性)
- **最大缩放**：支持200%缩放

### 9.3 交互反馈
- **触摸目标**：最小44×44pt
- **视觉反馈**：按钮状态变化
- **音频反馈**：可选的系统音效
- **触觉反馈**：关键操作的震动反馈

## 10. 图片资源

### 10.1 图片来源
- **主要来源**：Unsplash (https://unsplash.com)
- **备选来源**：Pexels (https://pexels.com)
- **图片类型**：自然风景、冥想场景、人物肖像

### 10.2 图片规格
- **卡片缩略图**：200×200pt, 圆角16pt
- **背景图片**：375×200pt, 适配屏幕宽度
- **用户头像**：100×100pt, 圆形裁剪
- **图片格式**：WebP优先，JPEG备选

### 10.3 图片优化
- **压缩质量**：80-90%
- **响应式图片**：提供2x和3x分辨率
- **懒加载**：非关键图片延迟加载

## 11. 品牌元素

### 11.1 Logo设计
- **主Logo**："静息" + "Serenity"
- **图标Logo**：叶子图标 (fas fa-leaf)
- **颜色**：白色或主色调
- **最小尺寸**：24×24pt

### 11.2 品牌色彩
- **主品牌色**：青绿色 (#2EC4B6)
- **辅助色**：深蓝色 (#1A2151)
- **强调色**：深紫色 (#4D4C7D)

### 11.3 品牌语调
- **温和友善**：使用温暖、鼓励的语言
- **专业可信**：提供科学依据和专业指导
- **简洁明了**：避免复杂术语，使用通俗易懂的表达

## 12. 开发交付

### 12.1 设计文件
- **原型文件**：HTML/CSS实现的高保真原型
- **设计规范**：本文档 (Design_Spec.md)
- **资源文件**：图标、图片等静态资源

### 12.2 技术规范
- **CSS框架**：Tailwind CSS
- **图标库**：FontAwesome 6.4.0
- **字体**：系统默认字体 (SF Pro)
- **浏览器支持**：现代浏览器 (用于原型预览)

### 12.3 交付清单
- ✅ 启动页面 (launch.html)
- ✅ 登录页面 (login.html)
- ✅ 首页 (home.html)
- ✅ 呼吸练习页面 (breathing.html)
- ✅ 呼吸练习进行中 (breathing-session.html)
- ✅ 冥想页面 (meditation.html)
- ✅ 睡眠页面 (sleep.html)
- ✅ 个人中心 (profile.html)
- ✅ 设置页面 (settings.html)
- ✅ 主入口页面 (index.html)
- ✅ 设计规范文档 (Design_Spec.md)

---

**文档版本**：1.0  
**创建日期**：2023年11月  
**最后更新**：2023年11月  
**创建者**：UI/UX设计团队  

*本设计规范文档为静息(Serenity)应用的官方设计指南，所有界面设计和开发实现都应严格遵循本规范。*