<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静息(Serenity) - 高保真原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-blue': '#1A2151',
                        'primary-teal': '#2EC4B6',
                        'primary-purple': '#4D4C7D',
                        'dark-gray': '#2E2E3A',
                        'medium-gray': '#6B7280',
                        'light-gray': '#F3F4F6'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
        }
        .prototype-frame {
            width: 375px;
            height: 812px;
            border: 8px solid #1d1d1f;
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: #000;
            position: relative;
        }
        .prototype-frame::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 30px;
            background: #1d1d1f;
            border-radius: 0 0 20px 20px;
            z-index: 10;
        }
        .prototype-frame iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }
        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-primary-blue via-primary-purple to-primary-teal min-h-screen">
    <!-- 页面头部 -->
    <div class="container mx-auto px-6 py-8">
        <div class="text-center mb-12 fade-in">
            <div class="flex items-center justify-center mb-6">
                <div class="w-16 h-16 rounded-full bg-primary-teal/30 flex items-center justify-center mr-4 floating-animation">
                    <i class="fas fa-leaf text-3xl text-primary-teal"></i>
                </div>
                <div>
                    <h1 class="text-4xl font-bold text-white mb-2">静息 Serenity</h1>
                    <p class="text-white/80 text-lg">高保真 iOS 应用原型展示</p>
                </div>
            </div>
            <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 max-w-4xl mx-auto">
                <p class="text-white/90 text-base leading-relaxed mb-4">
                    本原型基于产品需求文档(PRD)设计，严格遵循 Apple Human Interface Guidelines，
                    展示了静息冥想应用的完整用户界面和交互流程。
                </p>
                <div class="flex items-center justify-center space-x-6 text-sm text-white/70">
                    <div class="flex items-center">
                        <i class="fas fa-mobile-alt mr-2 text-primary-teal"></i>
                        <span>iOS 平台</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-palette mr-2 text-primary-teal"></i>
                        <span>Tailwind CSS</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-icons mr-2 text-primary-teal"></i>
                        <span>FontAwesome</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-images mr-2 text-primary-teal"></i>
                        <span>Unsplash</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 原型界面展示区域 -->
        <div class="space-y-16">
            <!-- 启动页面 -->
            <div class="fade-in" style="animation-delay: 0.2s;">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-white mb-2">启动页面</h2>
                    <p class="text-white/70">应用启动时的欢迎界面，展示品牌标识和核心理念</p>
                </div>
                <div class="flex justify-center">
                    <div class="prototype-frame">
                        <iframe src="launch.html" title="启动页面"></iframe>
                    </div>
                </div>
            </div>

            <!-- 登录页面 -->
            <div class="fade-in" style="animation-delay: 0.4s;">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-white mb-2">登录页面</h2>
                    <p class="text-white/70">用户登录界面，支持邮箱登录和第三方登录方式</p>
                </div>
                <div class="flex justify-center">
                    <div class="prototype-frame">
                        <iframe src="login.html" title="登录页面"></iframe>
                    </div>
                </div>
            </div>

            <!-- 首页 -->
            <div class="fade-in" style="animation-delay: 0.6s;">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-white mb-2">首页</h2>
                    <p class="text-white/70">应用主页，展示今日推荐、快速开始和个人统计</p>
                </div>
                <div class="flex justify-center">
                    <div class="prototype-frame">
                        <iframe src="home.html" title="首页"></iframe>
                    </div>
                </div>
            </div>

            <!-- 呼吸练习页面 -->
            <div class="fade-in" style="animation-delay: 0.8s;">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-white mb-2">呼吸练习</h2>
                    <p class="text-white/70">呼吸练习功能页面，提供多种呼吸法和引导练习</p>
                </div>
                <div class="flex justify-center">
                    <div class="prototype-frame">
                        <iframe src="breathing.html" title="呼吸练习页面"></iframe>
                    </div>
                </div>
            </div>

            <!-- 呼吸练习进行中 -->
            <div class="fade-in" style="animation-delay: 1.0s;">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-white mb-2">呼吸练习进行中</h2>
                    <p class="text-white/70">呼吸练习实时界面，包含动画引导和进度显示</p>
                </div>
                <div class="flex justify-center">
                    <div class="prototype-frame">
                        <iframe src="breathing-session.html" title="呼吸练习进行中"></iframe>
                    </div>
                </div>
            </div>

            <!-- 冥想页面 -->
            <div class="fade-in" style="animation-delay: 1.2s;">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-white mb-2">冥想</h2>
                    <p class="text-white/70">冥想功能页面，提供引导冥想、正念练习和冥想音乐</p>
                </div>
                <div class="flex justify-center">
                    <div class="prototype-frame">
                        <iframe src="meditation.html" title="冥想页面"></iframe>
                    </div>
                </div>
            </div>

            <!-- 睡眠页面 -->
            <div class="fade-in" style="animation-delay: 1.4s;">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-white mb-2">睡眠</h2>
                    <p class="text-white/70">睡眠辅助页面，包含睡前放松、睡眠故事和自然声音</p>
                </div>
                <div class="flex justify-center">
                    <div class="prototype-frame">
                        <iframe src="sleep.html" title="睡眠页面"></iframe>
                    </div>
                </div>
            </div>

            <!-- 个人中心 -->
            <div class="fade-in" style="animation-delay: 1.6s;">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-white mb-2">个人中心</h2>
                    <p class="text-white/70">用户个人信息、练习统计和成就展示页面</p>
                </div>
                <div class="flex justify-center">
                    <div class="prototype-frame">
                        <iframe src="profile.html" title="个人中心"></iframe>
                    </div>
                </div>
            </div>

            <!-- 设置页面 -->
            <div class="fade-in" style="animation-delay: 1.8s;">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-white mb-2">设置</h2>
                    <p class="text-white/70">应用设置页面，包含账户管理、偏好设置和支持选项</p>
                </div>
                <div class="flex justify-center">
                    <div class="prototype-frame">
                        <iframe src="settings.html" title="设置页面"></iframe>
                    </div>
                </div>
            </div>
        </div>

        <!-- 页面底部信息 -->
        <div class="mt-20 text-center fade-in" style="animation-delay: 2.0s;">
            <div class="bg-white/10 backdrop-blur-md rounded-2xl p-8 max-w-4xl mx-auto">
                <h3 class="text-xl font-bold text-white mb-4">技术规格</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 text-sm">
                    <div class="text-center">
                        <div class="w-12 h-12 rounded-full bg-primary-teal/30 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-code text-primary-teal text-xl"></i>
                        </div>
                        <div class="font-semibold text-white mb-1">前端技术</div>
                        <div class="text-white/70">HTML5 + Tailwind CSS</div>
                    </div>
                    <div class="text-center">
                        <div class="w-12 h-12 rounded-full bg-primary-purple/30 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-mobile-alt text-primary-purple text-xl"></i>
                        </div>
                        <div class="font-semibold text-white mb-1">目标平台</div>
                        <div class="text-white/70">iOS (iPhone)</div>
                    </div>
                    <div class="text-center">
                        <div class="w-12 h-12 rounded-full bg-yellow-500/30 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-paint-brush text-yellow-500 text-xl"></i>
                        </div>
                        <div class="font-semibold text-white mb-1">设计规范</div>
                        <div class="text-white/70">Apple HIG</div>
                    </div>
                    <div class="text-center">
                        <div class="w-12 h-12 rounded-full bg-green-500/30 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-check-circle text-green-500 text-xl"></i>
                        </div>
                        <div class="font-semibold text-white mb-1">完成度</div>
                        <div class="text-white/70">高保真原型</div>
                    </div>
                </div>
                
                <div class="mt-8 pt-6 border-t border-white/20">
                    <p class="text-white/60 text-sm">
                        本原型展示了静息(Serenity)冥想应用的完整用户界面设计，
                        所有界面均基于真实的产品需求文档制作，可直接用于开发参考。
                    </p>
                    <div class="mt-4 flex items-center justify-center space-x-6 text-xs text-white/50">
                        <span>© 2023 静息团队</span>
                        <span>•</span>
                        <span>设计版本 1.0</span>
                        <span>•</span>
                        <span>高保真原型</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 背景装饰元素 -->
    <div class="fixed top-20 left-10 w-32 h-32 bg-primary-teal/10 rounded-full blur-xl pointer-events-none"></div>
    <div class="fixed bottom-20 right-10 w-40 h-40 bg-primary-purple/10 rounded-full blur-xl pointer-events-none"></div>
    <div class="fixed top-1/2 right-20 w-24 h-24 bg-white/5 rounded-full blur-lg pointer-events-none"></div>
    <div class="fixed bottom-1/3 left-20 w-28 h-28 bg-primary-teal/5 rounded-full blur-lg pointer-events-none"></div>

    <script>
        // 添加滚动动画效果
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 观察所有需要动画的元素
        document.querySelectorAll('.fade-in').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.8s ease-out, transform 0.8s ease-out';
            observer.observe(el);
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>