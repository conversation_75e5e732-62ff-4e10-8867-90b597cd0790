<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静息 - 呼吸练习中</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-blue': '#1A2151',
                        'primary-teal': '#2EC4B6',
                        'primary-purple': '#4D4C7D',
                        'dark-gray': '#2E2E3A',
                        'medium-gray': '#6B7280',
                        'light-gray': '#F3F4F6'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #1A2151 0%, #4D4C7D 50%, #2EC4B6 100%);
        }
        .breathing-circle {
            animation: breathingCycle 8s ease-in-out infinite;
        }
        @keyframes breathingCycle {
            0%, 100% { 
                transform: scale(1);
                opacity: 0.6;
            }
            25% { 
                transform: scale(1.3);
                opacity: 1;
            }
            50% { 
                transform: scale(1.3);
                opacity: 1;
            }
            75% { 
                transform: scale(1);
                opacity: 0.6;
            }
        }
        .phase-text {
            animation: phaseChange 8s ease-in-out infinite;
        }
        @keyframes phaseChange {
            0%, 100% { opacity: 1; }
            25%, 75% { opacity: 0.7; }
            50% { opacity: 1; }
        }
        .progress-ring {
            transform: rotate(-90deg);
        }
        .progress-circle {
            stroke-dasharray: 628;
            stroke-dashoffset: 628;
            animation: progressAnimation 8s linear infinite;
        }
        @keyframes progressAnimation {
            0% { stroke-dashoffset: 628; }
            100% { stroke-dashoffset: 0; }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen text-white overflow-hidden">
    <!-- iOS状态栏 -->
    <div class="flex items-center justify-between px-6 pt-3 pb-2 text-white text-sm font-medium">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 顶部控制栏 -->
    <div class="flex items-center justify-between px-6 py-4">
        <button class="w-10 h-10 rounded-full bg-white/20 backdrop-blur-md flex items-center justify-center">
            <i class="fas fa-times text-lg"></i>
        </button>
        <div class="text-center">
            <h1 class="text-lg font-semibold">4-7-8 呼吸法</h1>
            <p class="text-sm text-white/70">剩余 4:32</p>
        </div>
        <button class="w-10 h-10 rounded-full bg-white/20 backdrop-blur-md flex items-center justify-center">
            <i class="fas fa-volume-up text-lg"></i>
        </button>
    </div>

    <!-- 主要呼吸引导区域 -->
    <div class="flex-1 flex flex-col items-center justify-center px-8">
        <!-- 呼吸圆圈动画 -->
        <div class="relative mb-12">
            <!-- 进度环 -->
            <svg class="progress-ring w-80 h-80 absolute inset-0" viewBox="0 0 200 200">
                <circle
                    cx="100"
                    cy="100"
                    r="90"
                    fill="none"
                    stroke="rgba(255,255,255,0.2)"
                    stroke-width="2"
                />
                <circle
                    class="progress-circle"
                    cx="100"
                    cy="100"
                    r="90"
                    fill="none"
                    stroke="#2EC4B6"
                    stroke-width="3"
                    stroke-linecap="round"
                />
            </svg>
            
            <!-- 主呼吸圆圈 -->
            <div class="breathing-circle w-80 h-80 rounded-full bg-gradient-to-br from-primary-teal/40 to-primary-purple/40 backdrop-blur-md border border-white/30 flex items-center justify-center relative">
                <!-- 内部装饰圆圈 -->
                <div class="w-48 h-48 rounded-full bg-gradient-to-br from-white/20 to-transparent backdrop-blur-sm flex items-center justify-center">
                    <div class="w-24 h-24 rounded-full bg-white/30 backdrop-blur-md flex items-center justify-center">
                        <i class="fas fa-wind text-3xl text-white"></i>
                    </div>
                </div>
                
                <!-- 装饰性粒子 -->
                <div class="absolute top-8 right-12 w-3 h-3 bg-primary-teal rounded-full opacity-60 animate-pulse"></div>
                <div class="absolute bottom-12 left-8 w-2 h-2 bg-white rounded-full opacity-40 animate-pulse" style="animation-delay: 1s;"></div>
                <div class="absolute top-16 left-16 w-1.5 h-1.5 bg-primary-teal rounded-full opacity-50 animate-pulse" style="animation-delay: 2s;"></div>
                <div class="absolute bottom-8 right-8 w-2.5 h-2.5 bg-white rounded-full opacity-30 animate-pulse" style="animation-delay: 3s;"></div>
            </div>
        </div>

        <!-- 呼吸阶段指示 -->
        <div class="text-center mb-8">
            <div class="phase-text text-4xl font-light mb-2">吸气</div>
            <div class="text-lg text-white/70">跟随圆圈慢慢扩大</div>
            <div class="text-6xl font-bold mt-4 text-primary-teal">4</div>
            <div class="text-sm text-white/60 mt-1">秒</div>
        </div>

        <!-- 呼吸节奏指示器 -->
        <div class="flex items-center space-x-8 mb-8">
            <div class="text-center">
                <div class="w-12 h-12 rounded-full bg-primary-teal/80 flex items-center justify-center mb-2">
                    <span class="text-lg font-bold">4</span>
                </div>
                <span class="text-xs text-white/70">吸气</span>
            </div>
            <div class="text-center">
                <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center mb-2">
                    <span class="text-lg font-bold">7</span>
                </div>
                <span class="text-xs text-white/70">屏息</span>
            </div>
            <div class="text-center">
                <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center mb-2">
                    <span class="text-lg font-bold">8</span>
                </div>
                <span class="text-xs text-white/70">呼气</span>
            </div>
        </div>
    </div>

    <!-- 底部控制区域 -->
    <div class="px-8 pb-8">
        <!-- 练习统计 -->
        <div class="flex items-center justify-center space-x-8 mb-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-primary-teal">3</div>
                <div class="text-xs text-white/70">完成轮次</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-primary-teal">2:28</div>
                <div class="text-xs text-white/70">已练习</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-primary-teal">12</div>
                <div class="text-xs text-white/70">目标轮次</div>
            </div>
        </div>

        <!-- 控制按钮 -->
        <div class="flex items-center justify-center space-x-6">
            <button class="w-14 h-14 rounded-full bg-white/20 backdrop-blur-md flex items-center justify-center">
                <i class="fas fa-backward text-lg"></i>
            </button>
            <button class="w-16 h-16 rounded-full bg-white/30 backdrop-blur-md flex items-center justify-center">
                <i class="fas fa-pause text-xl"></i>
            </button>
            <button class="w-14 h-14 rounded-full bg-white/20 backdrop-blur-md flex items-center justify-center">
                <i class="fas fa-forward text-lg"></i>
            </button>
        </div>

        <!-- 底部提示 -->
        <div class="text-center mt-6">
            <p class="text-sm text-white/60">轻触屏幕可暂停练习</p>
        </div>
    </div>

    <!-- 背景装饰元素 -->
    <div class="absolute top-32 left-4 w-32 h-32 bg-gradient-to-br from-primary-teal/10 to-transparent rounded-full blur-xl"></div>
    <div class="absolute bottom-40 right-4 w-40 h-40 bg-gradient-to-br from-primary-purple/10 to-transparent rounded-full blur-xl"></div>
    <div class="absolute top-1/2 right-8 w-24 h-24 bg-gradient-to-br from-white/5 to-transparent rounded-full blur-lg"></div>
</body>
</html>