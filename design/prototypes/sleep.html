<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静息 - 睡眠</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-blue': '#1A2151',
                        'primary-teal': '#2EC4B6',
                        'primary-purple': '#4D4C7D',
                        'dark-gray': '#2E2E3A',
                        'medium-gray': '#6B7280',
                        'light-gray': '#F3F4F6'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
        }
        .night-gradient {
            background: linear-gradient(135deg, #1A2151 0%, #2E2E3A 50%, #4D4C7D 100%);
        }
        .moon-glow {
            animation: moonGlow 4s ease-in-out infinite;
        }
        @keyframes moonGlow {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }
        .star-twinkle {
            animation: twinkle 3s ease-in-out infinite;
        }
        @keyframes twinkle {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }
    </style>
</head>
<body class="night-gradient min-h-screen text-white">
    <!-- iOS状态栏 -->
    <div class="flex items-center justify-between px-6 pt-3 pb-2 text-white text-sm font-medium">
        <div class="flex items-center space-x-1">
            <span>22:30</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-2 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="flex items-center justify-between px-6 py-4">
        <button class="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center">
            <i class="fas fa-arrow-left"></i>
        </button>
        <h1 class="text-xl font-bold">睡眠</h1>
        <button class="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center">
            <i class="fas fa-moon"></i>
        </button>
    </div>

    <!-- 主要内容 -->
    <div class="flex-1 px-6">
        <!-- 睡眠时间设置 -->
        <div class="mb-8">
            <div class="bg-white/10 rounded-3xl p-6 relative overflow-hidden">
                <div class="relative z-10">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h2 class="text-xl font-bold mb-1">晚安时光</h2>
                            <p class="text-white/70 text-sm">为您准备舒缓的睡前体验</p>
                        </div>
                        <div class="moon-glow w-16 h-16 rounded-full bg-gradient-to-br from-yellow-200 to-yellow-400 flex items-center justify-center">
                            <i class="fas fa-moon text-2xl text-primary-blue"></i>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary-teal">7.5</div>
                            <div class="text-xs text-white/70">建议睡眠时长</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary-teal">23:00</div>
                            <div class="text-xs text-white/70">建议就寝时间</div>
                        </div>
                    </div>
                </div>
                <!-- 装饰性星星 -->
                <div class="absolute top-4 right-20 w-2 h-2 bg-yellow-300 rounded-full star-twinkle"></div>
                <div class="absolute bottom-8 right-12 w-1.5 h-1.5 bg-yellow-200 rounded-full star-twinkle" style="animation-delay: 1s;"></div>
                <div class="absolute top-12 right-8 w-1 h-1 bg-yellow-400 rounded-full star-twinkle" style="animation-delay: 2s;"></div>
            </div>
        </div>

        <!-- 睡前放松 -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4">睡前放松</h2>
            <div class="space-y-3">
                <!-- 渐进式肌肉放松 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center space-x-4">
                    <div class="w-16 h-16 rounded-xl overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=200&h=200&fit=crop&crop=center" 
                             alt="肌肉放松" 
                             class="w-full h-full object-cover">
                    </div>
                    <div class="flex-1">
                        <h3 class="font-semibold">渐进式肌肉放松</h3>
                        <p class="text-white/70 text-sm">系统性放松全身肌肉群</p>
                        <div class="flex items-center mt-1 space-x-3">
                            <div class="flex items-center">
                                <i class="fas fa-clock text-xs text-primary-teal mr-1"></i>
                                <span class="text-xs text-white/70">25分钟</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-star text-xs text-yellow-400 mr-1"></i>
                                <span class="text-xs text-white/70">4.9</span>
                            </div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 深度呼吸放松 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center space-x-4">
                    <div class="w-16 h-16 rounded-xl overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=200&h=200&fit=crop&crop=center" 
                             alt="深度呼吸" 
                             class="w-full h-full object-cover">
                    </div>
                    <div class="flex-1">
                        <h3 class="font-semibold">深度呼吸放松</h3>
                        <p class="text-white/70 text-sm">通过缓慢呼吸进入睡眠状态</p>
                        <div class="flex items-center mt-1 space-x-3">
                            <div class="flex items-center">
                                <i class="fas fa-clock text-xs text-primary-teal mr-1"></i>
                                <span class="text-xs text-white/70">15分钟</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-star text-xs text-yellow-400 mr-1"></i>
                                <span class="text-xs text-white/70">4.8</span>
                            </div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 身体扫描冥想 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center space-x-4">
                    <div class="w-16 h-16 rounded-xl overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop&crop=center" 
                             alt="身体扫描" 
                             class="w-full h-full object-cover">
                    </div>
                    <div class="flex-1">
                        <h3 class="font-semibold">身体扫描冥想</h3>
                        <p class="text-white/70 text-sm">从头到脚的全身放松引导</p>
                        <div class="flex items-center mt-1 space-x-3">
                            <div class="flex items-center">
                                <i class="fas fa-clock text-xs text-primary-teal mr-1"></i>
                                <span class="text-xs text-white/70">30分钟</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-star text-xs text-yellow-400 mr-1"></i>
                                <span class="text-xs text-white/70">4.7</span>
                            </div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>
            </div>
        </div>

        <!-- 睡眠故事 -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4">睡眠故事</h2>
            <div class="grid grid-cols-1 gap-4">
                <!-- 森林夜晚 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center space-x-4">
                    <div class="w-20 h-16 rounded-xl overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=300&h=200&fit=crop&crop=center" 
                             alt="森林夜晚" 
                             class="w-full h-full object-cover">
                    </div>
                    <div class="flex-1">
                        <h3 class="font-semibold">森林夜晚的秘密</h3>
                        <p class="text-white/70 text-sm">在月光下的森林中，聆听大自然的夜曲</p>
                        <div class="flex items-center mt-1">
                            <i class="fas fa-clock text-xs text-primary-teal mr-1"></i>
                            <span class="text-xs text-white/70">45分钟</span>
                        </div>
                    </div>
                    <button class="w-10 h-10 rounded-full bg-primary-teal/30 flex items-center justify-center">
                        <i class="fas fa-play text-sm"></i>
                    </button>
                </div>

                <!-- 海边小屋 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center space-x-4">
                    <div class="w-20 h-16 rounded-xl overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1505142468610-359e7d316be0?w=300&h=200&fit=crop&crop=center" 
                             alt="海边小屋" 
                             class="w-full h-full object-cover">
                    </div>
                    <div class="flex-1">
                        <h3 class="font-semibold">海边小屋的夜晚</h3>
                        <p class="text-white/70 text-sm">伴随着海浪声入眠的温馨故事</p>
                        <div class="flex items-center mt-1">
                            <i class="fas fa-clock text-xs text-primary-teal mr-1"></i>
                            <span class="text-xs text-white/70">35分钟</span>
                        </div>
                    </div>
                    <button class="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                        <i class="fas fa-play text-sm"></i>
                    </button>
                </div>

                <!-- 山间小径 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center space-x-4">
                    <div class="w-20 h-16 rounded-xl overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop&crop=center" 
                             alt="山间小径" 
                             class="w-full h-full object-cover">
                    </div>
                    <div class="flex-1">
                        <h3 class="font-semibold">山间小径漫步</h3>
                        <p class="text-white/70 text-sm">在宁静的山谷中寻找内心的平静</p>
                        <div class="flex items-center mt-1">
                            <i class="fas fa-clock text-xs text-primary-teal mr-1"></i>
                            <span class="text-xs text-white/70">40分钟</span>
                        </div>
                    </div>
                    <button class="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                        <i class="fas fa-play text-sm"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 自然声音 -->
        <div class="mb-20">
            <h2 class="text-lg font-semibold mb-4">自然声音</h2>
            <div class="grid grid-cols-2 gap-4">
                <!-- 雨声 -->
                <div class="bg-white/10 rounded-2xl p-4 text-center">
                    <div class="w-12 h-12 rounded-xl bg-primary-teal/30 flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-cloud-rain text-xl text-primary-teal"></i>
                    </div>
                    <h3 class="font-semibold text-sm mb-1">雨声</h3>
                    <p class="text-xs text-white/70 mb-2">温柔的雨滴声</p>
                    <button class="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center mx-auto">
                        <i class="fas fa-play text-xs"></i>
                    </button>
                </div>

                <!-- 海浪声 -->
                <div class="bg-white/10 rounded-2xl p-4 text-center">
                    <div class="w-12 h-12 rounded-xl bg-primary-teal/30 flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-water text-xl text-primary-teal"></i>
                    </div>
                    <h3 class="font-semibold text-sm mb-1">海浪声</h3>
                    <p class="text-xs text-white/70 mb-2">舒缓的海浪拍岸</p>
                    <button class="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center mx-auto">
                        <i class="fas fa-play text-xs"></i>
                    </button>
                </div>

                <!-- 鸟鸣声 -->
                <div class="bg-white/10 rounded-2xl p-4 text-center">
                    <div class="w-12 h-12 rounded-xl bg-primary-teal/30 flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-dove text-xl text-primary-teal"></i>
                    </div>
                    <h3 class="font-semibold text-sm mb-1">鸟鸣声</h3>
                    <p class="text-xs text-white/70 mb-2">清晨的鸟儿歌唱</p>
                    <button class="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center mx-auto">
                        <i class="fas fa-play text-xs"></i>
                    </button>
                </div>

                <!-- 风声 -->
                <div class="bg-white/10 rounded-2xl p-4 text-center">
                    <div class="w-12 h-12 rounded-xl bg-primary-teal/30 flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-wind text-xl text-primary-teal"></i>
                    </div>
                    <h3 class="font-semibold text-sm mb-1">风声</h3>
                    <p class="text-xs text-white/70 mb-2">轻柔的微风声</p>
                    <button class="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center mx-auto">
                        <i class="fas fa-play text-xs"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white/10 backdrop-blur-md">
        <div class="flex items-center justify-around py-3">
            <div class="flex flex-col items-center space-y-1">
                <i class="fas fa-home text-white/60 text-lg"></i>
                <span class="text-xs text-white/60">首页</span>
            </div>
            <div class="flex flex-col items-center space-y-1">
                <i class="fas fa-wind text-white/60 text-lg"></i>
                <span class="text-xs text-white/60">呼吸</span>
            </div>
            <div class="flex flex-col items-center space-y-1">
                <i class="fas fa-spa text-white/60 text-lg"></i>
                <span class="text-xs text-white/60">冥想</span>
            </div>
            <div class="flex flex-col items-center space-y-1">
                <i class="fas fa-moon text-primary-teal text-lg"></i>
                <span class="text-xs text-primary-teal font-medium">睡眠</span>
            </div>
            <div class="flex flex-col items-center space-y-1">
                <i class="fas fa-user text-white/60 text-lg"></i>
                <span class="text-xs text-white/60">我的</span>
            </div>
        </div>
    </div>
</body>
</html>