<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静息 - 设置</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-blue': '#1A2151',
                        'primary-teal': '#2EC4B6',
                        'primary-purple': '#4D4C7D',
                        'dark-gray': '#2E2E3A',
                        'medium-gray': '#6B7280',
                        'light-gray': '#F3F4F6'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
        }
        .toggle-switch {
            width: 44px;
            height: 24px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            position: relative;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .toggle-switch.active {
            background: #2EC4B6;
        }
        .toggle-switch::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: white;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
        }
        .toggle-switch.active::after {
            transform: translateX(20px);
        }
    </style>
</head>
<body class="bg-primary-blue min-h-screen text-white">
    <!-- iOS状态栏 -->
    <div class="flex items-center justify-between px-6 pt-3 pb-2 text-white text-sm font-medium">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="flex items-center justify-between px-6 py-4">
        <button class="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center">
            <i class="fas fa-arrow-left"></i>
        </button>
        <h1 class="text-xl font-bold">设置</h1>
        <div class="w-10 h-10"></div>
    </div>

    <!-- 主要内容 -->
    <div class="flex-1 px-6">
        <!-- 账户设置 -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4 text-white/90">账户</h2>
            <div class="bg-white/10 rounded-2xl overflow-hidden">
                <!-- 个人资料 -->
                <div class="p-4 border-b border-white/10 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 rounded-full overflow-hidden">
                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face" 
                                 alt="用户头像" 
                                 class="w-full h-full object-cover">
                        </div>
                        <div>
                            <div class="font-medium">个人资料</div>
                            <div class="text-sm text-white/70">李小雅</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 订阅管理 -->
                <div class="p-4 border-b border-white/10 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-primary-teal/30 flex items-center justify-center">
                            <i class="fas fa-crown text-primary-teal"></i>
                        </div>
                        <div>
                            <div class="font-medium">订阅管理</div>
                            <div class="text-sm text-white/70">高级会员 · 还有 23 天</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 数据同步 -->
                <div class="p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-blue-500/30 flex items-center justify-center">
                            <i class="fas fa-sync text-blue-500"></i>
                        </div>
                        <div>
                            <div class="font-medium">数据同步</div>
                            <div class="text-sm text-white/70">iCloud 同步已开启</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>
            </div>
        </div>

        <!-- 应用设置 -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4 text-white/90">应用设置</h2>
            <div class="bg-white/10 rounded-2xl overflow-hidden">
                <!-- 通知设置 -->
                <div class="p-4 border-b border-white/10 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-yellow-500/30 flex items-center justify-center">
                            <i class="fas fa-bell text-yellow-500"></i>
                        </div>
                        <div>
                            <div class="font-medium">通知</div>
                            <div class="text-sm text-white/70">练习提醒和成就通知</div>
                        </div>
                    </div>
                    <div class="toggle-switch active"></div>
                </div>

                <!-- 音量设置 -->
                <div class="p-4 border-b border-white/10">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 rounded-xl bg-green-500/30 flex items-center justify-center">
                                <i class="fas fa-volume-up text-green-500"></i>
                            </div>
                            <div>
                                <div class="font-medium">音量</div>
                                <div class="text-sm text-white/70">调整音频音量</div>
                            </div>
                        </div>
                    </div>
                    <div class="ml-13">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-volume-down text-white/50 text-sm"></i>
                            <div class="flex-1 bg-white/20 rounded-full h-2 relative">
                                <div class="bg-primary-teal h-2 rounded-full" style="width: 70%"></div>
                                <div class="absolute top-0 bg-white w-4 h-4 rounded-full border-2 border-primary-teal" style="left: calc(70% - 8px); top: -4px;"></div>
                            </div>
                            <i class="fas fa-volume-up text-white/50 text-sm"></i>
                        </div>
                    </div>
                </div>

                <!-- 主题设置 -->
                <div class="p-4 border-b border-white/10 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-purple-500/30 flex items-center justify-center">
                            <i class="fas fa-palette text-purple-500"></i>
                        </div>
                        <div>
                            <div class="font-medium">主题</div>
                            <div class="text-sm text-white/70">深色主题</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 语言设置 -->
                <div class="p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-red-500/30 flex items-center justify-center">
                            <i class="fas fa-globe text-red-500"></i>
                        </div>
                        <div>
                            <div class="font-medium">语言</div>
                            <div class="text-sm text-white/70">简体中文</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>
            </div>
        </div>

        <!-- 练习偏好 -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4 text-white/90">练习偏好</h2>
            <div class="bg-white/10 rounded-2xl overflow-hidden">
                <!-- 默认练习时长 -->
                <div class="p-4 border-b border-white/10 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-primary-teal/30 flex items-center justify-center">
                            <i class="fas fa-clock text-primary-teal"></i>
                        </div>
                        <div>
                            <div class="font-medium">默认练习时长</div>
                            <div class="text-sm text-white/70">10 分钟</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 背景音乐 -->
                <div class="p-4 border-b border-white/10 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-indigo-500/30 flex items-center justify-center">
                            <i class="fas fa-music text-indigo-500"></i>
                        </div>
                        <div>
                            <div class="font-medium">背景音乐</div>
                            <div class="text-sm text-white/70">森林自然声</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 振动反馈 -->
                <div class="p-4 border-b border-white/10 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-orange-500/30 flex items-center justify-center">
                            <i class="fas fa-mobile-alt text-orange-500"></i>
                        </div>
                        <div>
                            <div class="font-medium">振动反馈</div>
                            <div class="text-sm text-white/70">练习节拍提示</div>
                        </div>
                    </div>
                    <div class="toggle-switch active"></div>
                </div>

                <!-- 自动锁屏 -->
                <div class="p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-gray-500/30 flex items-center justify-center">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <div>
                            <div class="font-medium">防止自动锁屏</div>
                            <div class="text-sm text-white/70">练习时保持屏幕常亮</div>
                        </div>
                    </div>
                    <div class="toggle-switch active"></div>
                </div>
            </div>
        </div>

        <!-- 支持 -->
        <div class="mb-20">
            <h2 class="text-lg font-semibold mb-4 text-white/90">支持</h2>
            <div class="bg-white/10 rounded-2xl overflow-hidden">
                <!-- 帮助中心 -->
                <div class="p-4 border-b border-white/10 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-blue-500/30 flex items-center justify-center">
                            <i class="fas fa-question-circle text-blue-500"></i>
                        </div>
                        <span class="font-medium">帮助中心</span>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 联系我们 -->
                <div class="p-4 border-b border-white/10 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-green-500/30 flex items-center justify-center">
                            <i class="fas fa-envelope text-green-500"></i>
                        </div>
                        <span class="font-medium">联系我们</span>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 服务条款 -->
                <div class="p-4 border-b border-white/10 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-yellow-500/30 flex items-center justify-center">
                            <i class="fas fa-file-contract text-yellow-500"></i>
                        </div>
                        <span class="font-medium">服务条款</span>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 隐私政策 -->
                <div class="p-4 border-b border-white/10 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-purple-500/30 flex items-center justify-center">
                            <i class="fas fa-shield-alt text-purple-500"></i>
                        </div>
                        <span class="font-medium">隐私政策</span>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 关于我们 -->
                <div class="p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-primary-teal/30 flex items-center justify-center">
                            <i class="fas fa-info-circle text-primary-teal"></i>
                        </div>
                        <div>
                            <div class="font-medium">关于静息</div>
                            <div class="text-sm text-white/70">版本 1.2.0</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>
            </div>
        </div>

        <!-- 底部操作 -->
        <div class="mb-8">
            <button class="w-full bg-red-500/20 text-red-400 py-4 rounded-2xl font-medium border border-red-500/30">
                退出登录
            </button>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white/10 backdrop-blur-md">
        <div class="flex items-center justify-around py-3">
            <div class="flex flex-col items-center space-y-1">
                <i class="fas fa-home text-white/60 text-lg"></i>
                <span class="text-xs text-white/60">首页</span>
            </div>
            <div class="flex flex-col items-center space-y-1">
                <i class="fas fa-wind text-white/60 text-lg"></i>
                <span class="text-xs text-white/60">呼吸</span>
            </div>
            <div class="flex flex-col items-center space-y-1">
                <i class="fas fa-spa text-white/60 text-lg"></i>
                <span class="text-xs text-white/60">冥想</span>
            </div>
            <div class="flex flex-col items-center space-y-1">
                <i class="fas fa-moon text-white/60 text-lg"></i>
                <span class="text-xs text-white/60">睡眠</span>
            </div>
            <div class="flex flex-col items-center space-y-1">
                <i class="fas fa-user text-white/60 text-lg"></i>
                <span class="text-xs text-white/60">我的</span>
            </div>
        </div>
    </div>

    <script>
        // 简单的开关切换功能
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', function() {
                this.classList.toggle('active');
            });
        });
    </script>
</body>
</html>