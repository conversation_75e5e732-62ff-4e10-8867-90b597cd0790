<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静息 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-blue': '#1A2151',
                        'primary-teal': '#2EC4B6',
                        'primary-purple': '#4D4C7D',
                        'dark-gray': '#2E2E3A',
                        'medium-gray': '#6B7280',
                        'light-gray': '#F3F4F6'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
        }
        .gradient-card {
            background: linear-gradient(135deg, #2EC4B6 0%, #4D4C7D 100%);
        }
        .breathing-pulse {
            animation: pulse 2s ease-in-out infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
    </style>
</head>
<body class="bg-primary-blue min-h-screen text-white">
    <!-- iOS状态栏 -->
    <div class="flex items-center justify-between px-6 pt-3 pb-2 text-white text-sm font-medium">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="flex flex-col min-h-screen">
        <!-- 顶部用户信息 -->
        <div class="px-6 py-4">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h1 class="text-2xl font-bold">早上好</h1>
                    <p class="text-white/70 text-sm">今天是开始新练习的好日子</p>
                </div>
                <div class="w-12 h-12 rounded-full overflow-hidden">
                    <!-- 用户头像 - Unsplash人像 -->
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face" 
                         alt="用户头像" 
                         class="w-full h-full object-cover">
                </div>
            </div>
        </div>

        <!-- 快速开始卡片 -->
        <div class="px-6 mb-6">
            <div class="gradient-card rounded-3xl p-6 relative overflow-hidden">
                <div class="relative z-10">
                    <h2 class="text-xl font-bold mb-2">开始今日练习</h2>
                    <p class="text-white/80 text-sm mb-4">5分钟呼吸练习，让心灵回归平静</p>
                    <button class="bg-white text-primary-blue px-6 py-3 rounded-2xl font-semibold breathing-pulse">
                        <i class="fas fa-play mr-2"></i>
                        开始练习
                    </button>
                </div>
                <!-- 装饰性背景元素 -->
                <div class="absolute top-4 right-4 w-16 h-16 bg-white/10 rounded-full"></div>
                <div class="absolute bottom-4 right-8 w-8 h-8 bg-white/20 rounded-full"></div>
            </div>
        </div>

        <!-- 今日统计 -->
        <div class="px-6 mb-6">
            <h3 class="text-lg font-semibold mb-4">今日进展</h3>
            <div class="grid grid-cols-3 gap-4">
                <div class="bg-white/10 rounded-2xl p-4 text-center">
                    <div class="text-2xl font-bold text-primary-teal">3</div>
                    <div class="text-xs text-white/70">连续天数</div>
                </div>
                <div class="bg-white/10 rounded-2xl p-4 text-center">
                    <div class="text-2xl font-bold text-primary-teal">15</div>
                    <div class="text-xs text-white/70">分钟</div>
                </div>
                <div class="bg-white/10 rounded-2xl p-4 text-center">
                    <div class="text-2xl font-bold text-primary-teal">2</div>
                    <div class="text-xs text-white/70">完成练习</div>
                </div>
            </div>
        </div>

        <!-- 推荐练习 -->
        <div class="px-6 mb-6">
            <h3 class="text-lg font-semibold mb-4">为你推荐</h3>
            <div class="space-y-3">
                <!-- 推荐练习1 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center space-x-4">
                    <div class="w-16 h-16 rounded-xl overflow-hidden">
                        <!-- 森林图片 - Unsplash -->
                        <img src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=200&h=200&fit=crop&crop=center" 
                             alt="森林呼吸" 
                             class="w-full h-full object-cover">
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold">森林呼吸</h4>
                        <p class="text-white/70 text-sm">在大自然的声音中放松身心</p>
                        <div class="flex items-center mt-1">
                            <i class="fas fa-clock text-xs text-primary-teal mr-1"></i>
                            <span class="text-xs text-white/70">10分钟</span>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 推荐练习2 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center space-x-4">
                    <div class="w-16 h-16 rounded-xl overflow-hidden">
                        <!-- 海洋图片 - Unsplash -->
                        <img src="https://images.unsplash.com/photo-1505142468610-359e7d316be0?w=200&h=200&fit=crop&crop=center" 
                             alt="海洋冥想" 
                             class="w-full h-full object-cover">
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold">海洋冥想</h4>
                        <p class="text-white/70 text-sm">跟随海浪的节奏深度放松</p>
                        <div class="flex items-center mt-1">
                            <i class="fas fa-clock text-xs text-primary-teal mr-1"></i>
                            <span class="text-xs text-white/70">15分钟</span>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 推荐练习3 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center space-x-4">
                    <div class="w-16 h-16 rounded-xl overflow-hidden">
                        <!-- 山景图片 - Unsplash -->
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop&crop=center" 
                             alt="山景专注" 
                             class="w-full h-full object-cover">
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold">山景专注</h4>
                        <p class="text-white/70 text-sm">提升专注力的呼吸练习</p>
                        <div class="flex items-center mt-1">
                            <i class="fas fa-clock text-xs text-primary-teal mr-1"></i>
                            <span class="text-xs text-white/70">8分钟</span>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="mt-auto bg-white/10 backdrop-blur-md">
            <div class="flex items-center justify-around py-3">
                <div class="flex flex-col items-center space-y-1">
                    <i class="fas fa-home text-primary-teal text-lg"></i>
                    <span class="text-xs text-primary-teal font-medium">首页</span>
                </div>
                <div class="flex flex-col items-center space-y-1">
                    <i class="fas fa-wind text-white/60 text-lg"></i>
                    <span class="text-xs text-white/60">呼吸</span>
                </div>
                <div class="flex flex-col items-center space-y-1">
                    <i class="fas fa-spa text-white/60 text-lg"></i>
                    <span class="text-xs text-white/60">冥想</span>
                </div>
                <div class="flex flex-col items-center space-y-1">
                    <i class="fas fa-moon text-white/60 text-lg"></i>
                    <span class="text-xs text-white/60">睡眠</span>
                </div>
                <div class="flex flex-col items-center space-y-1">
                    <i class="fas fa-user text-white/60 text-lg"></i>
                    <span class="text-xs text-white/60">我的</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>