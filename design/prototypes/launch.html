<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静息 - 启动页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-blue': '#1A2151',
                        'primary-teal': '#2EC4B6',
                        'primary-purple': '#4D4C7D',
                        'dark-gray': '#2E2E3A',
                        'medium-gray': '#6B7280',
                        'light-gray': '#F3F4F6'
                    },
                    fontFamily: {
                        'sf-pro': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Display', 'SF Pro Text', 'system-ui', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
        }
        .breathing-animation {
            animation: breathe 4s ease-in-out infinite;
        }
        @keyframes breathe {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.1); opacity: 1; }
        }
        .gradient-bg {
            background: linear-gradient(135deg, #1A2151 0%, #4D4C7D 50%, #2EC4B6 100%);
        }
        .fade-in {
            animation: fadeIn 2s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex flex-col items-center justify-center text-white overflow-hidden">
    <!-- iOS状态栏 -->
    <div class="absolute top-0 left-0 right-0 h-11 flex items-center justify-between px-6 text-white text-sm font-medium">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex flex-col items-center justify-center flex-1 px-8 text-center">
        <!-- 应用图标和呼吸动画 -->
        <div class="relative mb-12">
            <!-- 背景呼吸圆圈 -->
            <div class="breathing-animation w-48 h-48 rounded-full bg-gradient-to-br from-primary-teal/30 to-primary-purple/30 backdrop-blur-sm border border-white/20 flex items-center justify-center">
                <!-- 内部图标 -->
                <div class="w-24 h-24 rounded-full bg-white/20 backdrop-blur-md flex items-center justify-center">
                    <i class="fas fa-leaf text-4xl text-white"></i>
                </div>
            </div>
            
            <!-- 装饰性粒子效果 -->
            <div class="absolute -top-4 -right-4 w-3 h-3 bg-primary-teal rounded-full opacity-60 animate-pulse"></div>
            <div class="absolute -bottom-6 -left-6 w-2 h-2 bg-white rounded-full opacity-40 animate-pulse" style="animation-delay: 1s;"></div>
            <div class="absolute top-8 -left-8 w-1.5 h-1.5 bg-primary-teal rounded-full opacity-50 animate-pulse" style="animation-delay: 2s;"></div>
        </div>

        <!-- 应用名称和标语 -->
        <div class="fade-in">
            <h1 class="text-5xl font-bold mb-4 tracking-tight">静息</h1>
            <h2 class="text-2xl font-light mb-6 opacity-90">Serenity</h2>
            <p class="text-lg opacity-75 leading-relaxed max-w-xs">
                呼吸入静，心归平和
            </p>
            <p class="text-base opacity-60 mt-2">
                Breathe In, Peace Out
            </p>
        </div>

        <!-- 加载指示器 -->
        <div class="mt-16 flex flex-col items-center">
            <div class="flex space-x-2 mb-4">
                <div class="w-2 h-2 bg-white rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-white rounded-full animate-bounce" style="animation-delay: 0.1s;"></div>
                <div class="w-2 h-2 bg-white rounded-full animate-bounce" style="animation-delay: 0.2s;"></div>
            </div>
            <p class="text-sm opacity-50">正在加载...</p>
        </div>
    </div>

    <!-- 底部版权信息 -->
    <div class="pb-8 text-center">
        <p class="text-xs opacity-40">© 2023 静息团队</p>
    </div>

    <!-- 背景装饰元素 -->
    <div class="absolute top-20 left-8 w-32 h-32 bg-gradient-to-br from-primary-teal/10 to-transparent rounded-full blur-xl"></div>
    <div class="absolute bottom-32 right-8 w-40 h-40 bg-gradient-to-br from-primary-purple/10 to-transparent rounded-full blur-xl"></div>
    <div class="absolute top-1/2 left-4 w-24 h-24 bg-gradient-to-br from-white/5 to-transparent rounded-full blur-lg"></div>
</body>
</html>