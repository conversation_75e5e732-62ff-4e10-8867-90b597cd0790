<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静息 - 登录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-blue': '#1A2151',
                        'primary-teal': '#2EC4B6',
                        'primary-purple': '#4D4C7D',
                        'dark-gray': '#2E2E3A',
                        'medium-gray': '#6B7280',
                        'light-gray': '#F3F4F6'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #1A2151 0%, #4D4C7D 100%);
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen text-white">
    <!-- iOS状态栏 -->
    <div class="flex items-center justify-between px-6 pt-3 pb-2 text-white text-sm font-medium">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="flex flex-col min-h-screen px-8 pt-16">
        <!-- 顶部海洋图片 -->
        <div class="w-32 h-32 mx-auto mb-8 rounded-3xl overflow-hidden shadow-2xl">
            <!-- 使用Unsplash的海洋图片 -->
            <img src="https://images.unsplash.com/photo-*************-359e7d316be0?w=400&h=400&fit=crop&crop=center" 
                 alt="平静的海洋" 
                 class="w-full h-full object-cover">
        </div>

        <!-- 应用标题 -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold mb-2">Serenity</h1>
            <p class="text-lg opacity-80">Log into your account</p>
        </div>

        <!-- 登录表单 -->
        <div class="flex-1 max-w-sm mx-auto w-full">
            <!-- 邮箱输入 -->
            <div class="mb-4">
                <label class="block text-sm font-medium mb-2 opacity-80">Email</label>
                <input type="email" 
                       placeholder="输入您的邮箱"
                       class="w-full px-4 py-4 rounded-2xl glass-effect text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-primary-teal">
            </div>

            <!-- 密码输入 -->
            <div class="mb-6">
                <label class="block text-sm font-medium mb-2 opacity-80">Password</label>
                <input type="password" 
                       placeholder="输入您的密码"
                       class="w-full px-4 py-4 rounded-2xl glass-effect text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-primary-teal">
            </div>

            <!-- 继续按钮 -->
            <button class="w-full bg-primary-teal text-white py-4 rounded-2xl font-semibold text-lg mb-4 shadow-lg hover:bg-primary-teal/90 transition-colors">
                Continue with Email
            </button>

            <!-- 或者分割线 -->
            <div class="flex items-center my-6">
                <div class="flex-1 h-px bg-white/20"></div>
                <span class="px-4 text-sm opacity-60">Or continue with</span>
                <div class="flex-1 h-px bg-white/20"></div>
            </div>

            <!-- 第三方登录选项 -->
            <div class="space-y-3">
                <!-- Apple ID登录 -->
                <button class="w-full glass-effect py-4 rounded-2xl font-medium flex items-center justify-center space-x-3 hover:bg-white/20 transition-colors">
                    <i class="fab fa-apple text-xl"></i>
                    <span>Continue with Apple ID</span>
                </button>

                <!-- Google登录 -->
                <button class="w-full glass-effect py-4 rounded-2xl font-medium flex items-center justify-center space-x-3 hover:bg-white/20 transition-colors">
                    <i class="fab fa-google text-xl"></i>
                    <span>Continue with Google</span>
                </button>

                <!-- TikTok登录 -->
                <button class="w-full glass-effect py-4 rounded-2xl font-medium flex items-center justify-center space-x-3 hover:bg-white/20 transition-colors">
                    <i class="fab fa-tiktok text-xl"></i>
                    <span>Continue with TikTok</span>
                </button>

                <!-- Facebook登录 -->
                <button class="w-full glass-effect py-4 rounded-2xl font-medium flex items-center justify-center space-x-3 hover:bg-white/20 transition-colors">
                    <i class="fab fa-facebook text-xl"></i>
                    <span>Continue with Facebook</span>
                </button>
            </div>
        </div>

        <!-- 底部条款 -->
        <div class="text-center py-8">
            <p class="text-xs opacity-60 leading-relaxed">
                By continuing, you agree to our Terms of Service<br>
                and Privacy Policy
            </p>
        </div>
    </div>

    <!-- 背景装饰 -->
    <div class="absolute top-32 right-8 w-24 h-24 bg-primary-teal/20 rounded-full blur-xl"></div>
    <div class="absolute bottom-40 left-8 w-32 h-32 bg-primary-purple/20 rounded-full blur-xl"></div>
</body>
</html>