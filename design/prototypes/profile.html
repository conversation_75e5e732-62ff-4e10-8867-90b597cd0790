<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静息 - 个人中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-blue': '#1A2151',
                        'primary-teal': '#2EC4B6',
                        'primary-purple': '#4D4C7D',
                        'dark-gray': '#2E2E3A',
                        'medium-gray': '#6B7280',
                        'light-gray': '#F3F4F6'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
        }
        .achievement-glow {
            animation: glow 2s ease-in-out infinite alternate;
        }
        @keyframes glow {
            from { box-shadow: 0 0 10px rgba(46, 196, 182, 0.3); }
            to { box-shadow: 0 0 20px rgba(46, 196, 182, 0.6); }
        }
    </style>
</head>
<body class="bg-primary-blue min-h-screen text-white">
    <!-- iOS状态栏 -->
    <div class="flex items-center justify-between px-6 pt-3 pb-2 text-white text-sm font-medium">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="flex items-center justify-between px-6 py-4">
        <button class="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center">
            <i class="fas fa-arrow-left"></i>
        </button>
        <h1 class="text-xl font-bold">个人中心</h1>
        <button class="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center">
            <i class="fas fa-cog"></i>
        </button>
    </div>

    <!-- 主要内容 -->
    <div class="flex-1 px-6">
        <!-- 用户信息卡片 -->
        <div class="mb-8">
            <div class="bg-gradient-to-br from-primary-teal/30 to-primary-purple/30 rounded-3xl p-6 relative overflow-hidden">
                <div class="relative z-10">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-20 h-20 rounded-full overflow-hidden border-3 border-white/30">
                            <!-- 用户头像 - Unsplash人像 -->
                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200&h=200&fit=crop&crop=face" 
                                 alt="用户头像" 
                                 class="w-full h-full object-cover">
                        </div>
                        <div class="flex-1">
                            <h2 class="text-2xl font-bold mb-1">李小雅</h2>
                            <p class="text-white/80 text-sm mb-2">静息会员 · 已加入 45 天</p>
                            <div class="flex items-center space-x-4">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-primary-teal">15</div>
                                    <div class="text-xs text-white/70">连续天数</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-lg font-bold text-primary-teal">127</div>
                                    <div class="text-xs text-white/70">总练习次数</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-lg font-bold text-primary-teal">42h</div>
                                    <div class="text-xs text-white/70">累计时长</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 装饰性背景元素 -->
                <div class="absolute top-4 right-4 w-16 h-16 bg-white/10 rounded-full"></div>
                <div class="absolute bottom-4 right-8 w-8 h-8 bg-white/20 rounded-full"></div>
            </div>
        </div>

        <!-- 本周统计 -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4">本周统计</h2>
            <div class="bg-white/10 rounded-2xl p-4">
                <div class="grid grid-cols-3 gap-4 mb-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-primary-teal">7</div>
                        <div class="text-xs text-white/70">练习天数</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-primary-teal">18</div>
                        <div class="text-xs text-white/70">完成次数</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-primary-teal">4.2h</div>
                        <div class="text-xs text-white/70">练习时长</div>
                    </div>
                </div>
                <!-- 进度条 -->
                <div class="mb-2">
                    <div class="flex items-center justify-between text-sm mb-1">
                        <span class="text-white/70">周目标完成度</span>
                        <span class="text-primary-teal font-semibold">85%</span>
                    </div>
                    <div class="w-full bg-white/20 rounded-full h-2">
                        <div class="bg-primary-teal h-2 rounded-full" style="width: 85%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 成就徽章 -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4">成就徽章</h2>
            <div class="grid grid-cols-4 gap-4">
                <!-- 连续练习徽章 -->
                <div class="text-center">
                    <div class="w-16 h-16 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center mx-auto mb-2 achievement-glow">
                        <i class="fas fa-fire text-2xl text-white"></i>
                    </div>
                    <p class="text-xs text-white/70">连续15天</p>
                </div>
                
                <!-- 时长徽章 -->
                <div class="text-center">
                    <div class="w-16 h-16 rounded-full bg-gradient-to-br from-primary-teal to-primary-purple flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-clock text-2xl text-white"></i>
                    </div>
                    <p class="text-xs text-white/70">40小时达人</p>
                </div>
                
                <!-- 早起徽章 -->
                <div class="text-center">
                    <div class="w-16 h-16 rounded-full bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-sun text-2xl text-white"></i>
                    </div>
                    <p class="text-xs text-white/70">早起鸟儿</p>
                </div>
                
                <!-- 专注徽章 -->
                <div class="text-center">
                    <div class="w-16 h-16 rounded-full bg-white/20 flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-target text-2xl text-white/50"></i>
                    </div>
                    <p class="text-xs text-white/50">专注大师</p>
                </div>
            </div>
        </div>

        <!-- 最近练习 -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4">最近练习</h2>
            <div class="space-y-3">
                <!-- 练习记录1 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center space-x-4">
                    <div class="w-12 h-12 rounded-xl overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=200&h=200&fit=crop&crop=center" 
                             alt="4-7-8呼吸" 
                             class="w-full h-full object-cover">
                    </div>
                    <div class="flex-1">
                        <h3 class="font-semibold text-sm">4-7-8 呼吸法</h3>
                        <p class="text-white/70 text-xs">今天 21:30 · 完成 8分钟</p>
                    </div>
                    <div class="text-right">
                        <div class="text-primary-teal text-sm font-semibold">+5</div>
                        <div class="text-white/70 text-xs">专注值</div>
                    </div>
                </div>

                <!-- 练习记录2 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center space-x-4">
                    <div class="w-12 h-12 rounded-xl overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=200&h=200&fit=crop&crop=center" 
                             alt="正念冥想" 
                             class="w-full h-full object-cover">
                    </div>
                    <div class="flex-1">
                        <h3 class="font-semibold text-sm">正念呼吸冥想</h3>
                        <p class="text-white/70 text-xs">今天 07:15 · 完成 15分钟</p>
                    </div>
                    <div class="text-right">
                        <div class="text-primary-teal text-sm font-semibold">+8</div>
                        <div class="text-white/70 text-xs">平静值</div>
                    </div>
                </div>

                <!-- 练习记录3 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center space-x-4">
                    <div class="w-12 h-12 rounded-xl overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=200&h=200&fit=crop&crop=center" 
                             alt="睡前放松" 
                             class="w-full h-full object-cover">
                    </div>
                    <div class="flex-1">
                        <h3 class="font-semibold text-sm">睡前放松练习</h3>
                        <p class="text-white/70 text-xs">昨天 22:45 · 完成 20分钟</p>
                    </div>
                    <div class="text-right">
                        <div class="text-primary-teal text-sm font-semibold">+10</div>
                        <div class="text-white/70 text-xs">睡眠值</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能菜单 -->
        <div class="mb-20">
            <h2 class="text-lg font-semibold mb-4">更多功能</h2>
            <div class="space-y-2">
                <!-- 练习记录 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-primary-teal/30 flex items-center justify-center">
                            <i class="fas fa-chart-line text-primary-teal"></i>
                        </div>
                        <span class="font-medium">练习记录</span>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 目标设置 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-primary-purple/30 flex items-center justify-center">
                            <i class="fas fa-target text-primary-purple"></i>
                        </div>
                        <span class="font-medium">目标设置</span>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 提醒设置 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-yellow-500/30 flex items-center justify-center">
                            <i class="fas fa-bell text-yellow-500"></i>
                        </div>
                        <span class="font-medium">提醒设置</span>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 分享成果 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-green-500/30 flex items-center justify-center">
                            <i class="fas fa-share text-green-500"></i>
                        </div>
                        <span class="font-medium">分享成果</span>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>

                <!-- 帮助与反馈 -->
                <div class="bg-white/10 rounded-2xl p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-xl bg-blue-500/30 flex items-center justify-center">
                            <i class="fas fa-question-circle text-blue-500"></i>
                        </div>
                        <span class="font-medium">帮助与反馈</span>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white/10 backdrop-blur-md">
        <div class="flex items-center justify-around py-3">
            <div class="flex flex-col items-center space-y-1">
                <i class="fas fa-home text-white/60 text-lg"></i>
                <span class="text-xs text-white/60">首页</span>
            </div>
            <div class="flex flex-col items-center space-y-1">
                <i class="fas fa-wind text-white/60 text-lg"></i>
                <span class="text-xs text-white/60">呼吸</span>
            </div>
            <div class="flex flex-col items-center space-y-1">
                <i class="fas fa-spa text-white/60 text-lg"></i>
                <span class="text-xs text-white/60">冥想</span>
            </div>
            <div class="flex flex-col items-center space-y-1">
                <i class="fas fa-moon text-white/60 text-lg"></i>
                <span class="text-xs text-white/60">睡眠</span>
            </div>
            <div class="flex flex-col items-center space-y-1">
                <i class="fas fa-user text-primary-teal text-lg"></i>
                <span class="text-xs text-primary-teal font-medium">我的</span>
            </div>
        </div>
    </div>
</body>
</html>