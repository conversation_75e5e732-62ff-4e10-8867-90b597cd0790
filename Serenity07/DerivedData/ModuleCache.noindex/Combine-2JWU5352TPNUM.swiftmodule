---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.2/Combine.swiftmodule/x86_64-apple-ios-simulator.swiftmodule'
dependencies:
  - mtime:           1701956121000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.2/Combine.swiftmodule/x86_64-apple-ios-simulator.swiftmodule'
    size:            440920
  - mtime:           1699792606000000000
    path:            'usr/lib/swift/Swift.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            2073090
    sdk_relative:    true
  - mtime:           1699774113000000000
    path:            'usr/include/Darwin.apinotes'
    size:            1133
    sdk_relative:    true
  - mtime:           1699801313000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            31006
    sdk_relative:    true
  - mtime:           1699792637000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            147510
    sdk_relative:    true
  - mtime:           1699801406000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            21930
    sdk_relative:    true
  - mtime:           1699801856000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            169361
    sdk_relative:    true
version:         1
...
