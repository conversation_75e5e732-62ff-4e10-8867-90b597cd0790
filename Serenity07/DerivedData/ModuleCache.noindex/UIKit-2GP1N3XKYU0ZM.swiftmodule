---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.2/UIKit.swiftmodule/x86_64-apple-ios-simulator.swiftmodule'
dependencies:
  - mtime:           1701956175000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/17.2/UIKit.swiftmodule/x86_64-apple-ios-simulator.swiftmodule'
    size:            821988
  - mtime:           1699792606000000000
    path:            'usr/lib/swift/Swift.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            2073090
    sdk_relative:    true
  - mtime:           1699774113000000000
    path:            'usr/include/Darwin.apinotes'
    size:            1133
    sdk_relative:    true
  - mtime:           1698525730000000000
    path:            'usr/include/objc/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1699799314000000000
    path:            'usr/include/dispatch/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1699777717000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes'
    size:            51513
    sdk_relative:    true
  - mtime:           1699801477000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1699775315000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1699801313000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            31006
    sdk_relative:    true
  - mtime:           1699792637000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            147510
    sdk_relative:    true
  - mtime:           1699801406000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            21930
    sdk_relative:    true
  - mtime:           1699801856000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            169361
    sdk_relative:    true
  - mtime:           1699803526000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            4670
    sdk_relative:    true
  - mtime:           1699802890000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            56057
    sdk_relative:    true
  - mtime:           1699803021000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            22315
    sdk_relative:    true
  - mtime:           1699801448000000000
    path:            'usr/lib/swift/Observation.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            3554
    sdk_relative:    true
  - mtime:           1699803898000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            886160
    sdk_relative:    true
  - mtime:           1699803099000000000
    path:            'usr/lib/swift/CoreGraphics.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            42672
    sdk_relative:    true
  - mtime:           1699803612000000000
    path:            'System/Library/Frameworks/Accessibility.framework/Modules/Accessibility.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            15427
    sdk_relative:    true
  - mtime:           1699803518000000000
    path:            'usr/lib/swift/DataDetection.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            601
    sdk_relative:    true
  - mtime:           1699803572000000000
    path:            'System/Library/Frameworks/DeveloperToolsSupport.framework/Modules/DeveloperToolsSupport.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            7993
    sdk_relative:    true
  - mtime:           1699804025000000000
    path:            'System/Library/Frameworks/OpenGLES.framework/Headers/OpenGLES.apinotes'
    size:            1192
    sdk_relative:    true
  - mtime:           1699577275000000000
    path:            'System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes'
    size:            74227
    sdk_relative:    true
  - mtime:           1699775956000000000
    path:            'System/Library/Frameworks/QuartzCore.framework/Headers/QuartzCore.apinotes'
    size:            7428
    sdk_relative:    true
  - mtime:           1699803686000000000
    path:            'usr/lib/swift/Metal.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            25034
    sdk_relative:    true
  - mtime:           1699803778000000000
    path:            'usr/lib/swift/QuartzCore.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            1649
    sdk_relative:    true
  - mtime:           1699804173000000000
    path:            'System/Library/Frameworks/Symbols.framework/Modules/Symbols.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            11925
    sdk_relative:    true
  - mtime:           1698526851000000000
    path:            'System/Library/Frameworks/CoreImage.framework/Headers/CoreImage.apinotes'
    size:            36716
    sdk_relative:    true
  - mtime:           1699804074000000000
    path:            'System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes'
    size:            1662
    sdk_relative:    true
  - mtime:           1699804093000000000
    path:            'System/Library/Frameworks/UserNotifications.framework/Headers/UserNotifications.apinotes'
    size:            326
    sdk_relative:    true
  - mtime:           1699781099000000000
    path:            'System/Library/Frameworks/UIKit.framework/Headers/UIKit.apinotes'
    size:            161455
    sdk_relative:    true
  - mtime:           1699803506000000000
    path:            'usr/lib/swift/FileProvider.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            1437
    sdk_relative:    true
  - mtime:           1699803834000000000
    path:            'usr/lib/swift/CoreImage.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            603
    sdk_relative:    true
  - mtime:           1699803045000000000
    path:            'usr/lib/swift/os.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            118486
    sdk_relative:    true
  - mtime:           1699808684000000000
    path:            'System/Library/Frameworks/UIKit.framework/Modules/UIKit.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            192041
    sdk_relative:    true
version:         1
...
