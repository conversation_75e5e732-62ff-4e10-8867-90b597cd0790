{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "ef46bda7d101f37ec6f1a87b8fff3b0d006805fb299f583ef38569fd1ef6e65d"}], "containerPath": "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07.xcodeproj", "continueBuildingAfterErrors": false, "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "x86_64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator17.2", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["x86_64"], "targetArchitecture": "x86_64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products", "derivedDataPath": "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Debug", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPhone15,4", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "17.2", "ASSETCATALOG_FILTER_FOR_THINNING_DEVICE_CONFIGURATION": "iPhone15,4", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "COLOR_DIAGNOSTICS": "YES", "diagnostic_message_length": "116", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "NO", "TARGET_DEVICE_IDENTIFIER": "98CB0150-CC0A-4FB3-9FB6-CE2D0B82DC36", "TARGET_DEVICE_MODEL": "iPhone15,4", "TARGET_DEVICE_OS_VERSION": "17.2", "TARGET_DEVICE_PLATFORM_NAME": "iphonesimulator"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}