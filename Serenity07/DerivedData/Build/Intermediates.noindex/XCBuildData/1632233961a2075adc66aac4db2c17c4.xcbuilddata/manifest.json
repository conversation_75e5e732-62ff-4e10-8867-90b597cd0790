{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products": {"is-mutated": true}, "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app": {"is-mutated": true}, "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Serenity07": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Serenity07 normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/_CodeSignature", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/SDKStatCaches.noindex/iphonesimulator17.2-21C52-4f2951bfe8f3cd53c99228b0131e163e.sdkstatcache", "<Linked Binary /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Serenity07>", "<target-Serenity07-****************************************************************--begin-scanning>", "<target-Serenity07-****************************************************************--end>", "<target-Serenity07-****************************************************************--linker-inputs-ready>", "<target-Serenity07-****************************************************************--modules-ready>"], "outputs": ["<all>"]}, "<target-Serenity07-****************************************************************-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/ssu/root.ssu.yaml", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Serenity07", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/_CodeSignature", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Assets.car", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Info.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/PkgInfo", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent.der", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent.der", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07 Swift Compilation Finished", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-OutputFileMap.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.SwiftFileList", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_const_extract_protocols.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-all-target-headers.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-generated-files.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-own-target-headers.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-project-headers.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/all-product-headers.yaml", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/empty-Serenity07.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Serenity07", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_dependency_info.dat", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_lto.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07 Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftmodule", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftsourceinfo", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.abi.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-Swift.h", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftdoc", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Serenity07-Swift.h", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.LinkFileList"], "roots": ["/tmp/Serenity07.dst", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products"], "outputs": ["<target-Serenity07-****************************************************************-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.2.sdk /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/SDKStatCaches.noindex/iphonesimulator17.2-21C52-4f2951bfe8f3cd53c99228b0131e163e.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.2.sdk /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/SDKStatCaches.noindex/iphonesimulator17.2-21C52-4f2951bfe8f3cd53c99228b0131e163e.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/SDKStatCaches.noindex/iphonesimulator17.2-21C52-4f2951bfe8f3cd53c99228b0131e163e.sdkstatcache", "<ClangStatCache /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/SDKStatCaches.noindex/iphonesimulator17.2-21C52-4f2951bfe8f3cd53c99228b0131e163e.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.2.sdk", "-o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/SDKStatCaches.noindex/iphonesimulator17.2-21C52-4f2951bfe8f3cd53c99228b0131e163e.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07.xcodeproj", "signature": "af25961eb6d584568ab5b8c752565d82"}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator"]}, "P0:::Gate target-Serenity07-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Serenity07-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Metadata.appintents>"], "outputs": ["<target-Serenity07-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--Barrier-ChangePermissions>", "<target-Serenity07-****************************************************************--will-sign>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-Serenity07-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--Barrier-StripSymbols>", "<target-Serenity07-****************************************************************--will-sign>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-Serenity07-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Serenity07-****************************************************************--will-sign>", "<target-Serenity07-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>"], "outputs": ["<target-Serenity07-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-Serenity07-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--Barrier-GenerateStubAPI>", "<target-Serenity07-****************************************************************--will-sign>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-Serenity07-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-Serenity07-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--Barrier-CodeSign>", "<target-Serenity07-****************************************************************--will-sign>", "<target-Serenity07-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>"], "outputs": ["<target-Serenity07-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-Serenity07-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--Barrier-Validate>", "<target-Serenity07-****************************************************************--will-sign>", "<target-Serenity07-****************************************************************--begin-compiling>", "<Touch /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>"], "outputs": ["<target-Serenity07-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-Serenity07-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--Barrier-CopyAside>", "<target-Serenity07-****************************************************************--will-sign>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-Serenity07-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Serenity07-****************************************************************--will-sign>", "<target-Serenity07-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>"], "outputs": ["<target-Serenity07-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-Serenity07-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ProductStructureTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent.der", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent.der", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Entitlements.plist"], "outputs": ["<target-Serenity07-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--RealityAssetsTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-all-target-headers.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-generated-files.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-own-target-headers.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-project-headers.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/all-product-headers.yaml"], "outputs": ["<target-Serenity07-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Info.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/PkgInfo", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/empty-Serenity07.plist"], "outputs": ["<target-Serenity07-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--HeadermapTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleMapTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Serenity07-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-Serenity07-****************************************************************--InfoPlistTaskProducer>", "<target-Serenity07-****************************************************************--VersionPlistTaskProducer>", "<target-Serenity07-****************************************************************--SanitizerTaskProducer>", "<target-Serenity07-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-Serenity07-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-Serenity07-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-Serenity07-****************************************************************--StubBinaryTaskProducer>", "<target-Serenity07-****************************************************************--TestTargetTaskProducer>", "<target-Serenity07-****************************************************************--TestHostTaskProducer>", "<target-Serenity07-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-Serenity07-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-Serenity07-****************************************************************--DocumentationTaskProducer>", "<target-Serenity07-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--start>", "<target-Serenity07-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>"], "outputs": ["<target-Serenity07-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--GeneratedFilesTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Serenity07-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>"], "outputs": ["<target-Serenity07-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--VersionPlistTaskProducer": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--VersionPlistTaskProducer>"]}, "P0:::Gate target-Serenity07-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-Serenity07-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-Serenity07-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Assets.car", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07 Swift Compilation Finished", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-OutputFileMap.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.SwiftFileList", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_const_extract_protocols.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_dependency_info.dat", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_lto.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07 Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftmodule", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftsourceinfo", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.abi.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-Swift.h", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftdoc", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.LinkFileList"], "outputs": ["<target-Serenity07-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-Serenity07-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-Serenity07-****************************************************************--generated-headers>"]}, "P0:::Gate target-Serenity07-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07 Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftmodule", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftsourceinfo", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.abi.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-Swift.h", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftdoc", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Serenity07-Swift.h"], "outputs": ["<target-Serenity07-****************************************************************--swift-generated-headers>"]}, "P0:target-Serenity07-****************************************************************-:Debug:AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Info.plist", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Metadata.appintents>", "<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Serenity07-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Info.plist", "--temp-dir-path", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/ssu", "--bundle-id", "com.breathe.Serenity07", "--product-path", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app", "--extracted-metadata-path", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Metadata.appintents", "--archive-ssu-assets"], "env": {}, "working-directory": "/Users/<USER>/Desktop/007vscode/Serenity07", "signature": "fda0c718faaf158fbfdd640a5e4c5d51"}, "P0:target-Serenity07-****************************************************************-:Debug:CodeSign /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent/", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Info.plist/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Assets.xcassets/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/ContentView.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityButton.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityCard.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityProgressView.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityTextField.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Data/CoreDataManager.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Data/SerenityDataModel.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Localization/LocalizationManager.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/BreathingExercise.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/PersonalPlan.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/PracticeSession.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/User.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Navigation/NavigationRouter.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Navigation/TabBarView.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Animations.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Colors.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Spacing.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Typography.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Authentication/AuthenticationManager.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Authentication/AuthenticationView.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/BreathingAnimationView.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/BreathingExerciseLibrary.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/BreathingPracticeScreen.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/PracticeSessionManager.swift/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Serenity07App.swift/", "<target-Serenity07-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Serenity07-****************************************************************--will-sign>", "<target-Serenity07-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Serenity07 normal>", "<TRIGGER: MkDir /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/_CodeSignature", "<CodeSign /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>"]}, "P0:target-Serenity07-****************************************************************-:Debug:CompileAssetCatalog /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app /Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalog /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app /Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Assets.xcassets/", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app", "<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Assets.car"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--filter-for-thinning-device-configuration", "iPhone15,4", "--filter-for-device-os-version", "17.2", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "17.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Assets.xcassets"], "env": {}, "working-directory": "/Users/<USER>/Desktop/007vscode/Serenity07", "control-enabled": false, "deps": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/assetcatalog_dependencies"], "deps-style": "dependency-info", "signature": "d94cc83c16c150590d592424b1ebf8d0"}, "P0:target-Serenity07-****************************************************************-:Debug:CopySwiftLibs /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Serenity07", "<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Serenity07-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>"], "deps": "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-Serenity07-****************************************************************-:Debug:ExtractAppIntentsMetadata": {"tool": "appintents-metadata", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/BreathingExercise.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Colors.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/BreathingAnimationView.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/PersonalPlan.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/BreathingPracticeScreen.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Localization/LocalizationManager.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Authentication/AuthenticationManager.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/PracticeSession.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Animations.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Data/SerenityDataModel.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Data/CoreDataManager.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityProgressView.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityTextField.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/ContentView.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Typography.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/BreathingExerciseLibrary.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/PracticeSessionManager.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Authentication/AuthenticationView.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Spacing.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/User.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Serenity07App.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityCard.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Navigation/TabBarView.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityButton.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Navigation/NavigationRouter.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Serenity07", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_dependency_info.dat", "<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Serenity07-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Metadata.appintents>"]}, "P0:target-Serenity07-****************************************************************-:Debug:Gate target-Serenity07-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Serenity07.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-Serenity07-****************************************************************--begin-compiling>"]}, "P0:target-Serenity07-****************************************************************-:Debug:Gate target-Serenity07-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Serenity07.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-Serenity07-****************************************************************--begin-linking>"]}, "P0:target-Serenity07-****************************************************************-:Debug:Gate target-Serenity07-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Serenity07.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--begin-scanning>"]}, "P0:target-Serenity07-****************************************************************-:Debug:Gate target-Serenity07-****************************************************************--end": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--entry>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/ssu/root.ssu.yaml", "<CodeSign /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Assets.car", "<CopySwiftStdlib /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Metadata.appintents>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.h", "<MkDir /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Info.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/PkgInfo", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent.der", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07 Swift Compilation Finished", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "<Touch /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>", "<Validate /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>", "<ValidateDevelopmentAssets-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-OutputFileMap.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.SwiftFileList", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_const_extract_protocols.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-all-target-headers.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-generated-files.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-own-target-headers.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-project-headers.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/all-product-headers.yaml", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/empty-Serenity07.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_dependency_info.dat", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_lto.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07 Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftmodule", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftsourceinfo", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.abi.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-Swift.h", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftdoc", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Serenity07-Swift.h", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Serenity07-Swift.h", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.LinkFileList", "<target-Serenity07-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-Serenity07-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Serenity07-****************************************************************--Barrier-ChangePermissions>", "<target-Serenity07-****************************************************************--Barrier-CodeSign>", "<target-Serenity07-****************************************************************--Barrier-CopyAside>", "<target-Serenity07-****************************************************************--Barrier-GenerateStubAPI>", "<target-Serenity07-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Serenity07-****************************************************************--Barrier-RegisterProduct>", "<target-Serenity07-****************************************************************--Barrier-StripSymbols>", "<target-Serenity07-****************************************************************--Barrier-Validate>", "<target-Serenity07-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-Serenity07-****************************************************************--DocumentationTaskProducer>", "<target-Serenity07-****************************************************************--GeneratedFilesTaskProducer>", "<target-Serenity07-****************************************************************--HeadermapTaskProducer>", "<target-Serenity07-****************************************************************--InfoPlistTaskProducer>", "<target-Serenity07-****************************************************************--ModuleMapTaskProducer>", "<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Serenity07-****************************************************************--ProductStructureTaskProducer>", "<target-Serenity07-****************************************************************--RealityAssetsTaskProducer>", "<target-Serenity07-****************************************************************--SanitizerTaskProducer>", "<target-Serenity07-****************************************************************--StubBinaryTaskProducer>", "<target-Serenity07-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-Serenity07-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-Serenity07-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-Serenity07-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-Serenity07-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-Serenity07-****************************************************************--TestHostTaskProducer>", "<target-Serenity07-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-Serenity07-****************************************************************--TestTargetTaskProducer>", "<target-Serenity07-****************************************************************--VersionPlistTaskProducer>", "<target-Serenity07-****************************************************************--copy-headers-completion>", "<target-Serenity07-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Serenity07-****************************************************************--generated-headers>", "<target-Serenity07-****************************************************************--swift-generated-headers>"], "outputs": ["<target-Serenity07-****************************************************************--end>"]}, "P0:target-Serenity07-****************************************************************-:Debug:Gate target-Serenity07-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Serenity07.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["<target-Serenity07-****************************************************************--entry>"]}, "P0:target-Serenity07-****************************************************************-:Debug:Gate target-Serenity07-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Serenity07.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-Serenity07-****************************************************************--immediate>"]}, "P0:target-Serenity07-****************************************************************-:Debug:Gate target-Serenity07-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_dependency_info.dat", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_lto.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07 Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftmodule", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftsourceinfo", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.abi.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-Swift.h", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftdoc", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.LinkFileList"], "outputs": ["<target-Serenity07-****************************************************************--linker-inputs-ready>"]}, "P0:target-Serenity07-****************************************************************-:Debug:Gate target-Serenity07-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07 Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftmodule", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftsourceinfo", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.abi.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-Swift.h", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftdoc", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Serenity07-Swift.h"], "outputs": ["<target-Serenity07-****************************************************************--modules-ready>"]}, "P0:target-Serenity07-****************************************************************-:Debug:Gate target-Serenity07-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/ssu/root.ssu.yaml", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Assets.car", "<CopySwiftStdlib /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Metadata.appintents>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent.der", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent.der", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07 Swift Compilation Finished", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-OutputFileMap.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.SwiftFileList", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_const_extract_protocols.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_dependency_info.dat", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_lto.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07 Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftmodule", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftsourceinfo", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.abi.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-Swift.h", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftdoc", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Serenity07-Swift.h", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.LinkFileList", "<target-Serenity07-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-Serenity07-****************************************************************--unsigned-product-ready>"]}, "P0:target-Serenity07-****************************************************************-:Debug:Gate target-Serenity07-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-Serenity07-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-Serenity07-****************************************************************--will-sign>"]}, "P0:target-Serenity07-****************************************************************-:Debug:GenerateAssetSymbols /Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Assets.xcassets/", "<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.h"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "17.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Assets.xcassets", "--bundle-identifier", "com.breathe.Serenity07", "--generate-swift-asset-symbols", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Desktop/007vscode/Serenity07", "control-enabled": false, "signature": "93c2fad025b1331ea5a10bb0de1bd842"}, "P0:target-Serenity07-****************************************************************-:Debug:MkDir /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app", "inputs": ["<target-Serenity07-****************************************************************--start>", "<target-Serenity07-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app", "<MkDir /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>", "<TRIGGER: MkDir /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>"]}, "P0:target-Serenity07-****************************************************************-:Debug:ProcessInfoPlistFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Info.plist /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/empty-Serenity07.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Info.plist /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/empty-Serenity07.plist", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/empty-Serenity07.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/assetcatalog_generated_info.plist", "<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Info.plist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/PkgInfo"]}, "P0:target-Serenity07-****************************************************************-:Debug:ProcessProductPackaging  /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging  /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Entitlements-Simulated.plist", "<target-Serenity07-****************************************************************--ProductStructureTaskProducer>", "<target-Serenity07-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent"]}, "P0:target-Serenity07-****************************************************************-:Debug:ProcessProductPackaging  /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging  /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Entitlements.plist", "<target-Serenity07-****************************************************************--ProductStructureTaskProducer>", "<target-Serenity07-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent"]}, "P0:target-Serenity07-****************************************************************-:Debug:ProcessProductPackagingDER /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent.der", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent", "<target-Serenity07-****************************************************************--ProductStructureTaskProducer>", "<target-Serenity07-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent", "-o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Desktop/007vscode/Serenity07", "signature": "c091d1329034dffca2937d3f9b4c3be2"}, "P0:target-Serenity07-****************************************************************-:Debug:ProcessProductPackagingDER /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent.der", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent", "<target-Serenity07-****************************************************************--ProductStructureTaskProducer>", "<target-Serenity07-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent", "-o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Desktop/007vscode/Serenity07", "signature": "262c12adbc9b8aa1c5769c8caff7ec73"}, "P0:target-Serenity07-****************************************************************-:Debug:RegisterExecutionPolicyException /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app", "<target-Serenity07-****************************************************************--Barrier-CodeSign>", "<target-Serenity07-****************************************************************--will-sign>", "<target-Serenity07-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>"]}, "P0:target-Serenity07-****************************************************************-:Debug:SwiftDriver Compilation Serenity07 normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation Serenity07 normal x86_64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/BreathingExercise.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Colors.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/BreathingAnimationView.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/PersonalPlan.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/BreathingPracticeScreen.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Localization/LocalizationManager.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Authentication/AuthenticationManager.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/PracticeSession.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Animations.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Data/SerenityDataModel.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Data/CoreDataManager.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityProgressView.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityTextField.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/ContentView.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Typography.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/BreathingExerciseLibrary.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/PracticeSessionManager.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Authentication/AuthenticationView.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Spacing.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/User.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Serenity07App.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityCard.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Navigation/TabBarView.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityButton.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Navigation/NavigationRouter.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.SwiftFileList", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-OutputFileMap.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_const_extract_protocols.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-generated-files.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-own-target-headers.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-all-target-headers.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-project-headers.hmap", "<ClangStatCache /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/SDKStatCaches.noindex/iphonesimulator17.2-21C52-4f2951bfe8f3cd53c99228b0131e163e.sdkstatcache>", "<target-Serenity07-****************************************************************--generated-headers>", "<target-Serenity07-****************************************************************--copy-headers-completion>", "<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07 Swift Compilation Finished", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.swiftconstvalues", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-Serenity07-****************************************************************-:Debug:Touch /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app": {"tool": "shell", "description": "Touch /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app", "<target-Serenity07-****************************************************************--Barrier-Validate>", "<target-Serenity07-****************************************************************--will-sign>", "<target-Serenity07-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app"], "env": {}, "working-directory": "/Users/<USER>/Desktop/007vscode/Serenity07", "signature": "766842fd7a9cfb1cce8ea021cbf5e760"}, "P0:target-Serenity07-****************************************************************-:Debug:Validate /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app", "inputs": ["<target-Serenity07-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Serenity07-****************************************************************--will-sign>", "<target-Serenity07-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>"], "outputs": ["<Validate /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app>"]}, "P0:target-Serenity07-****************************************************************-:Debug:ValidateDevelopmentAssets /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Preview Content", "<target-Serenity07-****************************************************************--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build>"], "allow-missing-inputs": true}, "P0:target-Serenity07-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Entitlements-Simulated.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Entitlements-Simulated.plist", "inputs": ["<target-Serenity07-****************************************************************--ProductStructureTaskProducer>", "<target-Serenity07-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Entitlements-Simulated.plist"]}, "P0:target-Serenity07-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Entitlements.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Entitlements.plist", "inputs": ["<target-Serenity07-****************************************************************--ProductStructureTaskProducer>", "<target-Serenity07-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Entitlements.plist"]}, "P0:target-Serenity07-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-OutputFileMap.json", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-OutputFileMap.json"]}, "P0:target-Serenity07-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.SwiftFileList", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.SwiftFileList"]}, "P0:target-Serenity07-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_const_extract_protocols.json", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_const_extract_protocols.json"]}, "P0:target-Serenity07-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-all-non-framework-target-headers.hmap", "inputs": ["<target-Serenity07-****************************************************************--RealityAssetsTaskProducer>", "<target-Serenity07-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-all-non-framework-target-headers.hmap"]}, "P0:target-Serenity07-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-all-target-headers.hmap", "inputs": ["<target-Serenity07-****************************************************************--RealityAssetsTaskProducer>", "<target-Serenity07-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-all-target-headers.hmap"]}, "P0:target-Serenity07-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-generated-files.hmap", "inputs": ["<target-Serenity07-****************************************************************--RealityAssetsTaskProducer>", "<target-Serenity07-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-generated-files.hmap"]}, "P0:target-Serenity07-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-own-target-headers.hmap", "inputs": ["<target-Serenity07-****************************************************************--RealityAssetsTaskProducer>", "<target-Serenity07-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-own-target-headers.hmap"]}, "P0:target-Serenity07-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-project-headers.hmap", "inputs": ["<target-Serenity07-****************************************************************--RealityAssetsTaskProducer>", "<target-Serenity07-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-project-headers.hmap"]}, "P0:target-Serenity07-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.hmap", "inputs": ["<target-Serenity07-****************************************************************--RealityAssetsTaskProducer>", "<target-Serenity07-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.hmap"]}, "P0:target-Serenity07-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/all-product-headers.yaml", "inputs": ["<target-Serenity07-****************************************************************--RealityAssetsTaskProducer>", "<target-Serenity07-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/all-product-headers.yaml"]}, "P0:target-Serenity07-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/empty-Serenity07.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/empty-Serenity07.plist", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/empty-Serenity07.plist"]}, "P2:target-Serenity07-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftsourceinfo", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftsourceinfo/", "<target-Serenity07-****************************************************************--copy-headers-completion>", "<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo"]}, "P2:target-Serenity07-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.abi.json /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.abi.json /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.abi.json", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.abi.json/", "<target-Serenity07-****************************************************************--copy-headers-completion>", "<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.abi.json"]}, "P2:target-Serenity07-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.swiftdoc /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.swiftdoc /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftdoc", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftdoc/", "<target-Serenity07-****************************************************************--copy-headers-completion>", "<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.swiftdoc"]}, "P2:target-Serenity07-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.swiftmodule /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.swiftmodule /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftmodule", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftmodule/", "<target-Serenity07-****************************************************************--copy-headers-completion>", "<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.swiftmodule/x86_64-apple-ios-simulator.swiftmodule"]}, "P2:target-Serenity07-****************************************************************-:Debug:Ld /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Serenity07 normal": {"tool": "shell", "description": "Ld /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Serenity07 normal", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExercise.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Colors.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingAnimationView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PersonalPlan.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingPracticeScreen.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/LocalizationManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSession.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Animations.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityDataModel.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/CoreDataManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityProgressView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityTextField.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Typography.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/BreathingExerciseLibrary.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/PracticeSessionManager.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/AuthenticationView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Spacing.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/User.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07App.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityCard.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/TabBarView.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/SerenityButton.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/NavigationRouter.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.LinkFileList", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent.der", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator", "<target-Serenity07-****************************************************************--generated-headers>", "<target-Serenity07-****************************************************************--swift-generated-headers>", "<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Serenity07", "<Linked Binary /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Serenity07>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_dependency_info.dat", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_lto.o", "<TRIGGER: Ld /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Serenity07 normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "x86_64-apple-ios17.2-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator17.2.sdk", "-O0", "-L/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-L/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-F/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator", "-filelist", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_lto.o", "-<PERSON><PERSON><PERSON>", "-export_dynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftmodule", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07.app-Simulated.xcent.der", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_dependency_info.dat", "-o", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Products/Debug-iphonesimulator/Serenity07.app/Serenity07"], "env": {}, "working-directory": "/Users/<USER>/Desktop/007vscode/Serenity07", "deps": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_dependency_info.dat"], "deps-style": "dependency-info", "signature": "100c88cb93108214e39ef97b1c58e2d8"}, "P2:target-Serenity07-****************************************************************-:Debug:SwiftDriver Compilation Requirements Serenity07 normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements Serenity07 normal x86_64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/BreathingExercise.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Colors.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/BreathingAnimationView.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/PersonalPlan.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/BreathingPracticeScreen.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Localization/LocalizationManager.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Authentication/AuthenticationManager.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/PracticeSession.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Animations.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Data/SerenityDataModel.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Data/CoreDataManager.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityProgressView.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityTextField.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/ContentView.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Typography.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/BreathingExerciseLibrary.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/PracticeSessionManager.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Authentication/AuthenticationView.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Spacing.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/User.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Serenity07App.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityCard.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Navigation/TabBarView.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityButton.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Navigation/NavigationRouter.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.SwiftFileList", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-OutputFileMap.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07_const_extract_protocols.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-generated-files.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-own-target-headers.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-all-target-headers.hmap", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Serenity07-project-headers.hmap", "<ClangStatCache /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/SDKStatCaches.noindex/iphonesimulator17.2-21C52-4f2951bfe8f3cd53c99228b0131e163e.sdkstatcache>", "<target-Serenity07-****************************************************************--copy-headers-completion>", "<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07 Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftmodule", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftsourceinfo", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.abi.json", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-Swift.h", "/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.swiftdoc"]}, "P2:target-Serenity07-****************************************************************-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Serenity07-Swift.h /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Serenity07-Swift.h /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-Swift.h", "inputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07-Swift.h", "<target-Serenity07-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/Serenity07-Swift.h"]}, "P2:target-Serenity07-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.LinkFileList", "inputs": ["<target-Serenity07-****************************************************************--ModuleVerifierTaskProducer>", "<target-Serenity07-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/Objects-normal/x86_64/Serenity07.LinkFileList"]}}}