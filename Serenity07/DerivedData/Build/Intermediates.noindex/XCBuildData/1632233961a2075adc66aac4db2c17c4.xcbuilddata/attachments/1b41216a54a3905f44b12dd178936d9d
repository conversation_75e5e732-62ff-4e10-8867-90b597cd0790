/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/BreathingExercise.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Colors.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/BreathingAnimationView.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/PersonalPlan.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/BreathingPracticeScreen.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Localization/LocalizationManager.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Authentication/AuthenticationManager.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/PracticeSession.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Animations.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Data/SerenityDataModel.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Data/CoreDataManager.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityProgressView.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityTextField.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/ContentView.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Typography.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/BreathingExerciseLibrary.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Breathing/PracticeSessionManager.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Features/Authentication/AuthenticationView.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Theme/Spacing.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Models/User.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Serenity07App.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityCard.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Navigation/TabBarView.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Components/SerenityButton.swift
/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07/Core/Navigation/NavigationRouter.swift
/Users/<USER>/Desktop/007vscode/Serenity07/DerivedData/Build/Intermediates.noindex/Serenity07.build/Debug-iphonesimulator/Serenity07.build/DerivedSources/GeneratedAssetSymbols.swift
