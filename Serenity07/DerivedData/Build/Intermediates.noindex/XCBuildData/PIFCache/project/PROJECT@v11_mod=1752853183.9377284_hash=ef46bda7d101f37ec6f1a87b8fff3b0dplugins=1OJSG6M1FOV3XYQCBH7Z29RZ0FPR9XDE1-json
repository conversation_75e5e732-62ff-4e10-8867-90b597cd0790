{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "17.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d041702beb6c63412ede869a93479adc6", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "17.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "ef46bda7d101f37ec6f1a87b8fff3b0de5931ca4cd5f3f92e21f9e3f881adc1d", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"children": [{"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d12be6aa90d89dc52b68b9bf9d449cf4a", "path": "NavigationRouter.swift", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d075af3e231ed4bf5b46f3d43c98c889b", "path": "TabBarView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d17fddabd7da658657174579d2b1f4ba6", "name": "Navigation", "path": "Navigation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0db0707bb8aee01aca9b8fa0ab6c5345a8", "path": "PracticeSession.swift", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d75c8d4a5f135ed1da2696734134d60c2", "path": "User.swift", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d66536a275f8b03ccc868dea38987b929", "path": "BreathingExerc<PERSON>.swift", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0da7d896bfb921edcdd5c584f0641cec9a", "path": "PersonalPlan.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d12cd94185cd027e51d09973292f162ca", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0dc4f2255f8e287fe5e0ba1368e710621f", "path": "SerenityProgressView.swift", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0df20e03271821359ea4a186e9b7344c38", "path": "SerenityButton.swift", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d08975c4fc7fb6df77546f166f4d85374", "path": "SerenityTextField.swift", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d8579774ce2ea5503bf15dad4f7eedf81", "path": "SerenityCard.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ef46bda7d101f37ec6f1a87b8fff3b0dacf539a1013a02c47327b5aa81a36b45", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d057caa4327fe66cb283ab998c461c869", "path": "Typography.swift", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d19b8d4ed7c0dbb5cd70394209b011a90", "path": "Animations.swift", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0daff9acaefd63190c2e48e2d89724bd3e", "path": "Spacing.swift", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d8308508f4f4e926cac4ac346b81ec197", "path": "Colors.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d2357f18a4fecb2ee8cb1275f9c3fbe3c", "name": "Theme", "path": "Theme", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d73efdd0b0934b7bdb8b05e9b61ac4c1e", "path": "CoreDataManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d1ec79e7034001ed90db586c8ee3d421c", "path": "SerenityDataModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d212fa5da5e0458fac892bfa380d27c0d", "name": "Data", "path": "Data", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0dd99e5b2e89413097e717e9d6e5bb6500", "path": "LocalizationManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ef46bda7d101f37ec6f1a87b8fff3b0de1f53168423585139e112e8e98c16b7e", "name": "Localization", "path": "Localization", "sourceTree": "<group>", "type": "group"}], "guid": "ef46bda7d101f37ec6f1a87b8fff3b0dac18ff7147acd34d62fc7ad1583979ce", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0da505bf1522572dc0498dea9e0b7cb63f", "path": "BreathingAnimationView.swift", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d02f0ddf6fbcc4a8b22909934a471bfe5", "path": "BreathingExerciseLibrary.swift", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0dcb21b893a1d99d0bf784a3ed7a5394ae", "path": "PracticeSessionManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0de245841144cda7eeb308097f1354ef7c", "path": "BreathingPracticeScreen.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d82df00546e855b175591ca71a9a1700c", "name": "Breathing", "path": "Breathing", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0dac616f7fbc4c0ae6f578f02fc1604b27", "path": "AuthenticationManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileTextEncoding": "utf-8", "fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d48ce697afa788963695dd91fb6bb3798", "path": "AuthenticationView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d27e244f6b7e109d948443ca6c5805791", "name": "Authentication", "path": "Authentication", "sourceTree": "<group>", "type": "group"}], "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d2ae0a911a25e075417ef9a954f2ca22b", "name": "Features", "path": "Features", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d3ea0d083b6584e6c778eeeb43c158424", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d2a9ffd9ee1b503a601a7e8122b8ef599", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d68630393e66a3bb879405ec4178e2545", "path": "Serenity07App.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0ddcddad59e254685127c9dc23258431b0", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d0b8ffeb62fdf393c3dd0bab7760a6718", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d63ff38a9e361559be9e1b4e01cb91332", "name": "Serenity07", "path": "Serenity07", "sourceTree": "<group>", "type": "group"}, {"guid": "ef46bda7d101f37ec6f1a87b8fff3b0dd5239216239167352a4363fa71fb5e01", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d35e1bc1d71bae880775f625c2d99dda6", "name": "Serenity07", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "ef46bda7d101f37ec6f1a87b8fff3b0d", "path": "/Users/<USER>/Desktop/007vscode/Serenity07/Serenity07.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/007vscode/Serenity07", "targets": ["TARGET@v11_hash=5b0b200857d5c6a6080da5488aa5864c"]}