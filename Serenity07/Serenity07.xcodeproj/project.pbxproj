// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		4D9CF7B72E2AA23100024E60 /* BreathingAnimationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7962E2AA23100024E60 /* BreathingAnimationView.swift */; };
		4D9CF7B82E2AA23100024E60 /* BreathingExerciseLibrary.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7972E2AA23100024E60 /* BreathingExerciseLibrary.swift */; };
		4D9CF7B92E2AA23100024E60 /* PracticeSessionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7982E2AA23100024E60 /* PracticeSessionManager.swift */; };
		4D9CF7BA2E2AA23100024E60 /* BreathingPracticeScreen.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7992E2AA23100024E60 /* BreathingPracticeScreen.swift */; };
		4D9CF7BB2E2AA23100024E60 /* AuthenticationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF79B2E2AA23100024E60 /* AuthenticationManager.swift */; };
		4D9CF7BC2E2AA23100024E60 /* AuthenticationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF79C2E2AA23100024E60 /* AuthenticationView.swift */; };
		4D9CF7BD2E2AA23100024E60 /* NavigationRouter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF79F2E2AA23100024E60 /* NavigationRouter.swift */; };
		4D9CF7BE2E2AA23100024E60 /* TabBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7A02E2AA23100024E60 /* TabBarView.swift */; };
		4D9CF7BF2E2AA23100024E60 /* PracticeSession.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7A22E2AA23100024E60 /* PracticeSession.swift */; };
		4D9CF7C02E2AA23100024E60 /* User.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7A32E2AA23100024E60 /* User.swift */; };
		4D9CF7C12E2AA23100024E60 /* BreathingExercise.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7A42E2AA23100024E60 /* BreathingExercise.swift */; };
		4D9CF7C22E2AA23100024E60 /* PersonalPlan.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7A52E2AA23100024E60 /* PersonalPlan.swift */; };
		4D9CF7C32E2AA23100024E60 /* SerenityProgressView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7A72E2AA23100024E60 /* SerenityProgressView.swift */; };
		4D9CF7C42E2AA23100024E60 /* SerenityButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7A82E2AA23100024E60 /* SerenityButton.swift */; };
		4D9CF7C52E2AA23100024E60 /* SerenityTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7A92E2AA23100024E60 /* SerenityTextField.swift */; };
		4D9CF7C62E2AA23100024E60 /* SerenityCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7AA2E2AA23100024E60 /* SerenityCard.swift */; };
		4D9CF7C72E2AA23100024E60 /* Typography.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7AC2E2AA23100024E60 /* Typography.swift */; };
		4D9CF7C82E2AA23100024E60 /* Animations.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7AD2E2AA23100024E60 /* Animations.swift */; };
		4D9CF7C92E2AA23100024E60 /* Spacing.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7AE2E2AA23100024E60 /* Spacing.swift */; };
		4D9CF7CA2E2AA23100024E60 /* Colors.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7AF2E2AA23100024E60 /* Colors.swift */; };
		4D9CF7CB2E2AA23100024E60 /* CoreDataManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7B12E2AA23100024E60 /* CoreDataManager.swift */; };
		4D9CF7CC2E2AA23100024E60 /* SerenityDataModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7B22E2AA23100024E60 /* SerenityDataModel.swift */; };
		4D9CF7CD2E2AA23100024E60 /* LocalizationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7B42E2AA23100024E60 /* LocalizationManager.swift */; };
		4D9CF7CE2E2AA23100024E60 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 4D9CF7B62E2AA23100024E60 /* Preview Assets.xcassets */; };
		4DA07D3F2E280D6700F07DE5 /* Serenity07App.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DA07D3E2E280D6700F07DE5 /* Serenity07App.swift */; };
		4DA07D412E280D6700F07DE5 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DA07D402E280D6700F07DE5 /* ContentView.swift */; };
		4DA07D432E280D6A00F07DE5 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 4DA07D422E280D6A00F07DE5 /* Assets.xcassets */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		4D9CF7962E2AA23100024E60 /* BreathingAnimationView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BreathingAnimationView.swift; sourceTree = "<group>"; };
		4D9CF7972E2AA23100024E60 /* BreathingExerciseLibrary.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BreathingExerciseLibrary.swift; sourceTree = "<group>"; };
		4D9CF7982E2AA23100024E60 /* PracticeSessionManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PracticeSessionManager.swift; sourceTree = "<group>"; };
		4D9CF7992E2AA23100024E60 /* BreathingPracticeScreen.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BreathingPracticeScreen.swift; sourceTree = "<group>"; };
		4D9CF79B2E2AA23100024E60 /* AuthenticationManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AuthenticationManager.swift; sourceTree = "<group>"; };
		4D9CF79C2E2AA23100024E60 /* AuthenticationView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AuthenticationView.swift; sourceTree = "<group>"; };
		4D9CF79F2E2AA23100024E60 /* NavigationRouter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NavigationRouter.swift; sourceTree = "<group>"; };
		4D9CF7A02E2AA23100024E60 /* TabBarView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TabBarView.swift; sourceTree = "<group>"; };
		4D9CF7A22E2AA23100024E60 /* PracticeSession.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PracticeSession.swift; sourceTree = "<group>"; };
		4D9CF7A32E2AA23100024E60 /* User.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = User.swift; sourceTree = "<group>"; };
		4D9CF7A42E2AA23100024E60 /* BreathingExercise.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BreathingExercise.swift; sourceTree = "<group>"; };
		4D9CF7A52E2AA23100024E60 /* PersonalPlan.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PersonalPlan.swift; sourceTree = "<group>"; };
		4D9CF7A72E2AA23100024E60 /* SerenityProgressView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SerenityProgressView.swift; sourceTree = "<group>"; };
		4D9CF7A82E2AA23100024E60 /* SerenityButton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SerenityButton.swift; sourceTree = "<group>"; };
		4D9CF7A92E2AA23100024E60 /* SerenityTextField.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SerenityTextField.swift; sourceTree = "<group>"; };
		4D9CF7AA2E2AA23100024E60 /* SerenityCard.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SerenityCard.swift; sourceTree = "<group>"; };
		4D9CF7AC2E2AA23100024E60 /* Typography.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Typography.swift; sourceTree = "<group>"; };
		4D9CF7AD2E2AA23100024E60 /* Animations.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Animations.swift; sourceTree = "<group>"; };
		4D9CF7AE2E2AA23100024E60 /* Spacing.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Spacing.swift; sourceTree = "<group>"; };
		4D9CF7AF2E2AA23100024E60 /* Colors.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Colors.swift; sourceTree = "<group>"; };
		4D9CF7B12E2AA23100024E60 /* CoreDataManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CoreDataManager.swift; sourceTree = "<group>"; };
		4D9CF7B22E2AA23100024E60 /* SerenityDataModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SerenityDataModel.swift; sourceTree = "<group>"; };
		4D9CF7B42E2AA23100024E60 /* LocalizationManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LocalizationManager.swift; sourceTree = "<group>"; };
		4D9CF7B62E2AA23100024E60 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		4DA07D3B2E280D6700F07DE5 /* Serenity07.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Serenity07.app; sourceTree = BUILT_PRODUCTS_DIR; };
		4DA07D3E2E280D6700F07DE5 /* Serenity07App.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Serenity07App.swift; sourceTree = "<group>"; };
		4DA07D402E280D6700F07DE5 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		4DA07D422E280D6A00F07DE5 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		4DA07D382E280D6700F07DE5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4D9CF7942E2AA23100024E60 /* Features */ = {
			isa = PBXGroup;
			children = (
				4D9CF7952E2AA23100024E60 /* Breathing */,
				4D9CF79A2E2AA23100024E60 /* Authentication */,
			);
			path = Features;
			sourceTree = "<group>";
		};
		4D9CF7952E2AA23100024E60 /* Breathing */ = {
			isa = PBXGroup;
			children = (
				4D9CF7962E2AA23100024E60 /* BreathingAnimationView.swift */,
				4D9CF7972E2AA23100024E60 /* BreathingExerciseLibrary.swift */,
				4D9CF7982E2AA23100024E60 /* PracticeSessionManager.swift */,
				4D9CF7992E2AA23100024E60 /* BreathingPracticeScreen.swift */,
			);
			path = Breathing;
			sourceTree = "<group>";
		};
		4D9CF79A2E2AA23100024E60 /* Authentication */ = {
			isa = PBXGroup;
			children = (
				4D9CF79B2E2AA23100024E60 /* AuthenticationManager.swift */,
				4D9CF79C2E2AA23100024E60 /* AuthenticationView.swift */,
			);
			path = Authentication;
			sourceTree = "<group>";
		};
		4D9CF79D2E2AA23100024E60 /* Core */ = {
			isa = PBXGroup;
			children = (
				4D9CF79E2E2AA23100024E60 /* Navigation */,
				4D9CF7A12E2AA23100024E60 /* Models */,
				4D9CF7A62E2AA23100024E60 /* Components */,
				4D9CF7AB2E2AA23100024E60 /* Theme */,
				4D9CF7B02E2AA23100024E60 /* Data */,
				4D9CF7B32E2AA23100024E60 /* Localization */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		4D9CF79E2E2AA23100024E60 /* Navigation */ = {
			isa = PBXGroup;
			children = (
				4D9CF79F2E2AA23100024E60 /* NavigationRouter.swift */,
				4D9CF7A02E2AA23100024E60 /* TabBarView.swift */,
			);
			path = Navigation;
			sourceTree = "<group>";
		};
		4D9CF7A12E2AA23100024E60 /* Models */ = {
			isa = PBXGroup;
			children = (
				4D9CF7A22E2AA23100024E60 /* PracticeSession.swift */,
				4D9CF7A32E2AA23100024E60 /* User.swift */,
				4D9CF7A42E2AA23100024E60 /* BreathingExercise.swift */,
				4D9CF7A52E2AA23100024E60 /* PersonalPlan.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		4D9CF7A62E2AA23100024E60 /* Components */ = {
			isa = PBXGroup;
			children = (
				4D9CF7A72E2AA23100024E60 /* SerenityProgressView.swift */,
				4D9CF7A82E2AA23100024E60 /* SerenityButton.swift */,
				4D9CF7A92E2AA23100024E60 /* SerenityTextField.swift */,
				4D9CF7AA2E2AA23100024E60 /* SerenityCard.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		4D9CF7AB2E2AA23100024E60 /* Theme */ = {
			isa = PBXGroup;
			children = (
				4D9CF7AC2E2AA23100024E60 /* Typography.swift */,
				4D9CF7AD2E2AA23100024E60 /* Animations.swift */,
				4D9CF7AE2E2AA23100024E60 /* Spacing.swift */,
				4D9CF7AF2E2AA23100024E60 /* Colors.swift */,
			);
			path = Theme;
			sourceTree = "<group>";
		};
		4D9CF7B02E2AA23100024E60 /* Data */ = {
			isa = PBXGroup;
			children = (
				4D9CF7B12E2AA23100024E60 /* CoreDataManager.swift */,
				4D9CF7B22E2AA23100024E60 /* SerenityDataModel.swift */,
			);
			path = Data;
			sourceTree = "<group>";
		};
		4D9CF7B32E2AA23100024E60 /* Localization */ = {
			isa = PBXGroup;
			children = (
				4D9CF7B42E2AA23100024E60 /* LocalizationManager.swift */,
			);
			path = Localization;
			sourceTree = "<group>";
		};
		4D9CF7B52E2AA23100024E60 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				4D9CF7B62E2AA23100024E60 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		4DA07D322E280D6700F07DE5 = {
			isa = PBXGroup;
			children = (
				4DA07D3D2E280D6700F07DE5 /* Serenity07 */,
				4DA07D3C2E280D6700F07DE5 /* Products */,
			);
			sourceTree = "<group>";
		};
		4DA07D3C2E280D6700F07DE5 /* Products */ = {
			isa = PBXGroup;
			children = (
				4DA07D3B2E280D6700F07DE5 /* Serenity07.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		4DA07D3D2E280D6700F07DE5 /* Serenity07 */ = {
			isa = PBXGroup;
			children = (
				4D9CF79D2E2AA23100024E60 /* Core */,
				4D9CF7942E2AA23100024E60 /* Features */,
				4D9CF7B52E2AA23100024E60 /* Preview Content */,
				4DA07D3E2E280D6700F07DE5 /* Serenity07App.swift */,
				4DA07D402E280D6700F07DE5 /* ContentView.swift */,
				4DA07D422E280D6A00F07DE5 /* Assets.xcassets */,
			);
			path = Serenity07;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		4DA07D3A2E280D6700F07DE5 /* Serenity07 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4DA07D492E280D6A00F07DE5 /* Build configuration list for PBXNativeTarget "Serenity07" */;
			buildPhases = (
				4DA07D372E280D6700F07DE5 /* Sources */,
				4DA07D382E280D6700F07DE5 /* Frameworks */,
				4DA07D392E280D6700F07DE5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Serenity07;
			productName = Serenity07;
			productReference = 4DA07D3B2E280D6700F07DE5 /* Serenity07.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		4DA07D332E280D6700F07DE5 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1520;
				LastUpgradeCheck = 1520;
				TargetAttributes = {
					4DA07D3A2E280D6700F07DE5 = {
						CreatedOnToolsVersion = 15.2;
					};
				};
			};
			buildConfigurationList = 4DA07D362E280D6700F07DE5 /* Build configuration list for PBXProject "Serenity07" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 4DA07D322E280D6700F07DE5;
			productRefGroup = 4DA07D3C2E280D6700F07DE5 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				4DA07D3A2E280D6700F07DE5 /* Serenity07 */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		4DA07D392E280D6700F07DE5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4D9CF7CE2E2AA23100024E60 /* Preview Assets.xcassets in Resources */,
				4DA07D432E280D6A00F07DE5 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		4DA07D372E280D6700F07DE5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4D9CF7C12E2AA23100024E60 /* BreathingExercise.swift in Sources */,
				4D9CF7CA2E2AA23100024E60 /* Colors.swift in Sources */,
				4D9CF7B72E2AA23100024E60 /* BreathingAnimationView.swift in Sources */,
				4D9CF7C22E2AA23100024E60 /* PersonalPlan.swift in Sources */,
				4D9CF7BA2E2AA23100024E60 /* BreathingPracticeScreen.swift in Sources */,
				4D9CF7CD2E2AA23100024E60 /* LocalizationManager.swift in Sources */,
				4D9CF7BB2E2AA23100024E60 /* AuthenticationManager.swift in Sources */,
				4D9CF7BF2E2AA23100024E60 /* PracticeSession.swift in Sources */,
				4D9CF7C82E2AA23100024E60 /* Animations.swift in Sources */,
				4D9CF7CC2E2AA23100024E60 /* SerenityDataModel.swift in Sources */,
				4D9CF7CB2E2AA23100024E60 /* CoreDataManager.swift in Sources */,
				4D9CF7C32E2AA23100024E60 /* SerenityProgressView.swift in Sources */,
				4D9CF7C52E2AA23100024E60 /* SerenityTextField.swift in Sources */,
				4DA07D412E280D6700F07DE5 /* ContentView.swift in Sources */,
				4D9CF7C72E2AA23100024E60 /* Typography.swift in Sources */,
				4D9CF7B82E2AA23100024E60 /* BreathingExerciseLibrary.swift in Sources */,
				4D9CF7B92E2AA23100024E60 /* PracticeSessionManager.swift in Sources */,
				4D9CF7BC2E2AA23100024E60 /* AuthenticationView.swift in Sources */,
				4D9CF7C92E2AA23100024E60 /* Spacing.swift in Sources */,
				4D9CF7C02E2AA23100024E60 /* User.swift in Sources */,
				4DA07D3F2E280D6700F07DE5 /* Serenity07App.swift in Sources */,
				4D9CF7C62E2AA23100024E60 /* SerenityCard.swift in Sources */,
				4D9CF7BE2E2AA23100024E60 /* TabBarView.swift in Sources */,
				4D9CF7C42E2AA23100024E60 /* SerenityButton.swift in Sources */,
				4D9CF7BD2E2AA23100024E60 /* NavigationRouter.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		4DA07D472E280D6A00F07DE5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		4DA07D482E280D6A00F07DE5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		4DA07D4A2E280D6A00F07DE5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Serenity07/Preview Content\"";
				DEVELOPMENT_TEAM = 48C635F3FN;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.breathe.Serenity07;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		4DA07D4B2E280D6A00F07DE5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Serenity07/Preview Content\"";
				DEVELOPMENT_TEAM = 48C635F3FN;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.breathe.Serenity07;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		4DA07D362E280D6700F07DE5 /* Build configuration list for PBXProject "Serenity07" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4DA07D472E280D6A00F07DE5 /* Debug */,
				4DA07D482E280D6A00F07DE5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4DA07D492E280D6A00F07DE5 /* Build configuration list for PBXNativeTarget "Serenity07" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4DA07D4A2E280D6A00F07DE5 /* Debug */,
				4DA07D4B2E280D6A00F07DE5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 4DA07D332E280D6700F07DE5 /* Project object */;
}
