//
//  PracticeSessionManager.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import Combine
import AVFoundation
import UIKit

/// Manages breathing practice sessions including timing, progress tracking, and completion
class PracticeSessionManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var currentSession: PracticeSession?
    @Published var sessionState: SessionState = .idle
    @Published var currentTime: TimeInterval = 0
    @Published var currentPhase: BreathingPhase?
    @Published var currentPhaseProgress: Double = 0
    @Published var cycleCount: Int = 0
    @Published var breathingSpeed: BreathingSpeed = .normal
    @Published var backgroundSoundVolume: Float = 0.5
    @Published var hapticFeedbackEnabled: Bool = true
    
    // MARK: - Private Properties
    
    private let coreDataManager = CoreDataManager.shared
    private var sessionTimer: Timer?
    private var phaseTimer: Timer?
    private var phaseStartTime: Date?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Session State
    
    enum SessionState {
        case idle
        case preparing
        case active
        case paused
        case completed
        case cancelled
    }
    
    // MARK: - Session Management
    
    /// Start a new practice session
    func startSession(exercise: BreathingExercise, duration: TimeInterval) {
        guard let user = coreDataManager.getCurrentUser() else { return }
        
        // Create new session
        let session = PracticeSession.create(
            in: coreDataManager.viewContext,
            user: user,
            exercise: exercise,
            plannedDuration: Int(duration)
        )
        
        currentSession = session
        sessionState = .preparing
        currentTime = 0
        cycleCount = 0
        
        // Get user preferences
        breathingSpeed = BreathingSpeed(rawValue: user.getPreference(.breathingSpeed)) ?? .normal
        backgroundSoundVolume = user.backgroundSoundVolume
        hapticFeedbackEnabled = user.hapticFeedbackEnabled
        
        // Start the session after a brief preparation
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.beginActiveSession()
        }
        
        coreDataManager.save()
    }
    
    /// Begin the active breathing session
    private func beginActiveSession() {
        guard let session = currentSession,
              let exercise = session.exercise else { return }
        
        sessionState = .active
        startSessionTimer()
        startBreathingCycle(with: exercise.breathingPhases)
    }
    
    /// Pause the current session
    func pauseSession() {
        guard sessionState == .active else { return }
        
        sessionState = .paused
        stopTimers()
    }
    
    /// Resume the paused session
    func resumeSession() {
        guard sessionState == .paused else { return }
        
        sessionState = .active
        startSessionTimer()
        
        // Resume current phase if available
        if let phase = currentPhase {
            continueCurrentPhase(phase)
        }
    }
    
    /// Stop and cancel the current session
    func cancelSession() {
        guard let session = currentSession else { return }
        
        sessionState = .cancelled
        stopTimers()
        
        session.cancel(reason: "User cancelled")
        coreDataManager.save()
        
        resetSession()
    }
    
    /// Complete the current session
    func completeSession(feedback: Int? = nil, notes: String? = nil) {
        guard let session = currentSession else { return }
        
        sessionState = .completed
        stopTimers()
        
        session.complete(with: feedback, notes: notes)
        coreDataManager.save()
        
        // Update user statistics
        updateUserStatistics()
        
        resetSession()
    }
    
    /// Force complete session when time runs out
    private func forceCompleteSession() {
        completeSession()
    }
    
    // MARK: - Breathing Cycle Management
    
    private func startBreathingCycle(with phases: [BreathingPhase]) {
        guard !phases.isEmpty else { return }
        
        var currentPhaseIndex = 0
        
        func executeNextPhase() {
            guard sessionState == .active else { return }
            
            let phase = phases[currentPhaseIndex]
            executePhase(phase) {
                currentPhaseIndex += 1
                
                if currentPhaseIndex >= phases.count {
                    // Completed one full cycle
                    currentPhaseIndex = 0
                    self.cycleCount += 1
                    self.triggerHapticFeedback(.light)
                }
                
                executeNextPhase()
            }
        }
        
        executeNextPhase()
    }
    
    private func executePhase(_ phase: BreathingPhase, completion: @escaping () -> Void) {
        currentPhase = phase
        currentPhaseProgress = 0
        phaseStartTime = Date()
        
        let adjustedDuration = Double(phase.duration) * breathingSpeed.multiplier
        
        // Trigger haptic feedback for phase start
        triggerHapticFeedback(.medium)
        
        // Animate phase progress
        animatePhaseProgress(duration: adjustedDuration)
        
        // Set timer for phase completion
        phaseTimer = Timer.scheduledTimer(withTimeInterval: adjustedDuration, repeats: false) { _ in
            completion()
        }
    }
    
    private func continueCurrentPhase(_ phase: BreathingPhase) {
        guard let startTime = phaseStartTime else {
            // If we don't have start time, restart the phase
            executePhase(phase) {}
            return
        }
        
        let elapsed = Date().timeIntervalSince(startTime)
        let adjustedDuration = Double(phase.duration) * breathingSpeed.multiplier
        let remaining = max(0, adjustedDuration - elapsed)
        
        if remaining > 0 {
            currentPhaseProgress = elapsed / adjustedDuration
            animatePhaseProgress(duration: remaining, startProgress: currentPhaseProgress)
            
            phaseTimer = Timer.scheduledTimer(withTimeInterval: remaining, repeats: false) { _ in
                // Phase completed
            }
        }
    }
    
    private func animatePhaseProgress(duration: TimeInterval, startProgress: Double = 0) {
        let startTime = Date()
        let progressTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { timer in
            guard self.sessionState == .active else {
                timer.invalidate()
                return
            }
            
            let elapsed = Date().timeIntervalSince(startTime)
            let progress = startProgress + (elapsed / duration)
            
            if progress >= 1.0 {
                self.currentPhaseProgress = 1.0
                timer.invalidate()
            } else {
                self.currentPhaseProgress = progress
            }
        }
        
        // Store timer reference if needed
        _ = progressTimer
    }
    
    // MARK: - Timer Management
    
    private func startSessionTimer() {
        sessionTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            self.currentTime += 1
            
            // Check if session should end
            if let session = self.currentSession,
               self.currentTime >= TimeInterval(session.plannedDuration) {
                self.forceCompleteSession()
            }
        }
    }
    
    private func stopTimers() {
        sessionTimer?.invalidate()
        phaseTimer?.invalidate()
        sessionTimer = nil
        phaseTimer = nil
    }
    
    // MARK: - Settings Management
    
    func updateBreathingSpeed(_ speed: BreathingSpeed) {
        breathingSpeed = speed
        
        // Update user preference
        if let user = coreDataManager.getCurrentUser() {
            user.setPreference(.breathingSpeed, value: speed.rawValue, in: coreDataManager.viewContext)
            coreDataManager.save()
        }
    }
    
    func updateBackgroundVolume(_ volume: Float) {
        backgroundSoundVolume = volume
        
        // Update user preference
        if let user = coreDataManager.getCurrentUser() {
            user.setPreference(.backgroundSoundVolume, value: String(volume), in: coreDataManager.viewContext)
            coreDataManager.save()
        }
    }
    
    func updateHapticFeedback(_ enabled: Bool) {
        hapticFeedbackEnabled = enabled
        
        // Update user preference
        if let user = coreDataManager.getCurrentUser() {
            user.setPreference(.hapticFeedback, value: enabled ? "true" : "false", in: coreDataManager.viewContext)
            coreDataManager.save()
        }
    }
    
    // MARK: - Haptic Feedback
    
    private func triggerHapticFeedback(_ style: UIImpactFeedbackGenerator.FeedbackStyle) {
        guard hapticFeedbackEnabled else { return }
        
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.impactOccurred()
    }
    
    // MARK: - Statistics and Progress
    
    private func updateUserStatistics() {
        // This would update user's practice statistics
        // For now, we'll just trigger a notification
        NotificationCenter.default.post(name: .sessionCompleted, object: currentSession)
    }
    
    func getSessionProgress() -> Double {
        guard let session = currentSession else { return 0 }
        
        let plannedDuration = Double(session.plannedDuration)
        guard plannedDuration > 0 else { return 0 }
        
        return min(currentTime / plannedDuration, 1.0)
    }
    
    func getRemainingTime() -> TimeInterval {
        guard let session = currentSession else { return 0 }
        
        let plannedDuration = TimeInterval(session.plannedDuration)
        return max(0, plannedDuration - currentTime)
    }
    
    func getFormattedCurrentTime() -> String {
        return formatTime(currentTime)
    }
    
    func getFormattedRemainingTime() -> String {
        return formatTime(getRemainingTime())
    }
    
    func getFormattedTotalTime() -> String {
        guard let session = currentSession else { return "0:00" }
        return formatTime(TimeInterval(session.plannedDuration))
    }
    
    private func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    // MARK: - Session Reset
    
    private func resetSession() {
        currentSession = nil
        sessionState = .idle
        currentTime = 0
        currentPhase = nil
        currentPhaseProgress = 0
        cycleCount = 0
        phaseStartTime = nil
        stopTimers()
    }
    
    // MARK: - Session History
    
    func getRecentSessions(limit: Int = 10) -> [PracticeSession] {
        return coreDataManager.fetchRecentSessions(limit: limit)
    }
    
    func getTodaysSessions() -> [PracticeSession] {
        return coreDataManager.fetchTodaySessions()
    }
    
    func getUserStatistics() -> SessionStatistics? {
        return coreDataManager.getUserStatistics()
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let sessionCompleted = Notification.Name("SessionCompleted")
    static let sessionCancelled = Notification.Name("SessionCancelled")
    static let phaseChanged = Notification.Name("PhaseChanged")
}

// MARK: - Session Quality Assessment

extension PracticeSessionManager {
    
    /// Assess the quality of the current session
    func assessSessionQuality() -> SessionQuality {
        guard currentSession != nil else { return .poor }

        let completionRate = getSessionProgress()
        let wasInterrupted = sessionState == .cancelled || sessionState == .paused
        
        if completionRate >= 0.9 && !wasInterrupted {
            return .excellent
        } else if completionRate >= 0.7 && !wasInterrupted {
            return .good
        } else if completionRate >= 0.5 {
            return .fair
        } else {
            return .poor
        }
    }
    
    enum SessionQuality: String, CaseIterable {
        case excellent = "excellent"
        case good = "good"
        case fair = "fair"
        case poor = "poor"
        
        var displayName: String {
            switch self {
            case .excellent:
                return NSLocalizedString("Excellent", comment: "Excellent session quality")
            case .good:
                return NSLocalizedString("Good", comment: "Good session quality")
            case .fair:
                return NSLocalizedString("Fair", comment: "Fair session quality")
            case .poor:
                return NSLocalizedString("Poor", comment: "Poor session quality")
            }
        }
        
        var color: String {
            switch self {
            case .excellent:
                return "success"
            case .good:
                return "primaryTeal"
            case .fair:
                return "warning"
            case .poor:
                return "error"
            }
        }
        
        var emoji: String {
            switch self {
            case .excellent:
                return "🌟"
            case .good:
                return "😊"
            case .fair:
                return "😐"
            case .poor:
                return "😔"
            }
        }
    }
}
