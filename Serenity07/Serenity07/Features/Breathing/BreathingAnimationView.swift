//
//  BreathingAnimationView.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI
import Combine

/// Breathing animation view with customizable patterns and visual effects
struct BreathingAnimationView: View {
    
    // MARK: - Properties
    
    let breathingPhases: [BreathingPhase]
    let speed: BreathingSpeed
    let onPhaseChange: ((BreathingPhase) -> Void)?
    let onCycleComplete: (() -> Void)?
    
    @State private var currentPhaseIndex = 0
    @State private var animationScale: CGFloat = 1.0
    @State private var animationOpacity: Double = 0.6
    @State private var isAnimating = false
    @State private var timer: Timer?
    @State private var phaseTimer: Timer?
    @State private var cycleCount = 0
    
    // MARK: - Animation Configuration
    
    private let minScale: CGFloat = 1.0
    private let maxScale: CGFloat = 1.3
    private let minOpacity: Double = 0.6
    private let maxOpacity: Double = 1.0
    
    // MARK: - Initialization
    
    init(
        breathingPhases: [BreathingPhase],
        speed: BreathingSpeed = .normal,
        onPhaseChange: ((BreathingPhase) -> Void)? = nil,
        onCycleComplete: (() -> Void)? = nil
    ) {
        self.breathingPhases = breathingPhases
        self.speed = speed
        self.onPhaseChange = onPhaseChange
        self.onCycleComplete = onCycleComplete
    }
    
    // MARK: - Body
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background circles for depth
                ForEach(0..<3, id: \.self) { index in
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [
                                    Color.primaryTeal.opacity(0.1 - Double(index) * 0.03),
                                    Color.primaryPurple.opacity(0.05 - Double(index) * 0.02)
                                ],
                                center: .center,
                                startRadius: 50,
                                endRadius: 200
                            )
                        )
                        .frame(width: circleSize(for: geometry) + CGFloat(index * 20))
                        .scaleEffect(animationScale - CGFloat(index) * 0.1)
                        .opacity(animationOpacity - Double(index) * 0.1)
                }
                
                // Main breathing circle
                Circle()
                    .stroke(
                        LinearGradient(
                            colors: [Color.primaryTeal, Color.primaryPurple],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 3
                    )
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.primaryTeal.opacity(animationOpacity * 0.3),
                                Color.primaryPurple.opacity(animationOpacity * 0.2)
                            ],
                            center: .center,
                            startRadius: 50,
                            endRadius: 150
                        )
                    )
                    .frame(width: circleSize(for: geometry), height: circleSize(for: geometry))
                    .scaleEffect(animationScale)
                    .opacity(animationOpacity)
                
                // Phase indicator text
                VStack(spacing: Spacing.sm) {
                    Text(currentPhase.type.displayName)
                        .textStyle(.cardTitle)
                        .foregroundColor(.primaryTeal)
                    
                    Text(currentPhase.instructions)
                        .textStyle(.secondaryBody)
                        .multilineTextAlignment(.center)
                        .opacity(0.8)
                }
                .padding(.horizontal, Spacing.lg)
            }
        }
        .onAppear {
            startAnimation()
        }
        .onDisappear {
            stopAnimation()
        }
    }
    
    // MARK: - Animation Control
    
    func startAnimation() {
        guard !isAnimating else { return }
        isAnimating = true
        currentPhaseIndex = 0
        animateCurrentPhase()
    }
    
    func stopAnimation() {
        isAnimating = false
        timer?.invalidate()
        phaseTimer?.invalidate()
        timer = nil
        phaseTimer = nil
    }
    
    func pauseAnimation() {
        timer?.invalidate()
        phaseTimer?.invalidate()
    }
    
    func resumeAnimation() {
        guard isAnimating else { return }
        animateCurrentPhase()
    }
    
    // MARK: - Private Methods
    
    private var currentPhase: BreathingPhase {
        guard currentPhaseIndex < breathingPhases.count else {
            return breathingPhases.first ?? BreathingPhase.defaultPattern.first!
        }
        return breathingPhases[currentPhaseIndex]
    }
    
    private func circleSize(for geometry: GeometryProxy) -> CGFloat {
        let maxSize = min(geometry.size.width, geometry.size.height) * 0.7
        return min(maxSize, ComponentSize.breathingCircleMaxSize)
    }
    
    private func animateCurrentPhase() {
        let phase = currentPhase
        let adjustedDuration = Double(phase.duration) * speed.multiplier
        
        // Notify phase change
        onPhaseChange?(phase)
        
        // Determine target values based on phase type
        let targetScale: CGFloat
        let targetOpacity: Double
        
        switch phase.type {
        case .inhale:
            targetScale = maxScale
            targetOpacity = maxOpacity
        case .hold:
            targetScale = maxScale
            targetOpacity = maxOpacity
        case .exhale:
            targetScale = minScale
            targetOpacity = minOpacity
        case .pause:
            targetScale = minScale
            targetOpacity = minOpacity
        }
        
        // Animate to target values
        withAnimation(.easeInOut(duration: adjustedDuration)) {
            animationScale = targetScale
            animationOpacity = targetOpacity
        }
        
        // Set timer for phase completion
        phaseTimer = Timer.scheduledTimer(withTimeInterval: adjustedDuration, repeats: false) { _ in
            moveToNextPhase()
        }
    }
    
    private func moveToNextPhase() {
        currentPhaseIndex += 1
        
        // Check if we completed a full cycle
        if currentPhaseIndex >= breathingPhases.count {
            currentPhaseIndex = 0
            cycleCount += 1
            onCycleComplete?()
        }
        
        // Continue animation if still active
        if isAnimating {
            animateCurrentPhase()
        }
    }
}

// MARK: - Breathing Control View

struct BreathingControlView: View {
    
    @Binding var isPlaying: Bool
    @Binding var currentTime: TimeInterval
    let totalTime: TimeInterval
    let onPlayPause: () -> Void
    let onStop: () -> Void
    let onSpeedChange: (BreathingSpeed) -> Void
    
    @State private var selectedSpeed: BreathingSpeed = .normal
    
    var body: some View {
        VStack(spacing: Spacing.lg) {
            // Progress indicator
            VStack(spacing: Spacing.sm) {
                HStack {
                    Text(formatTime(currentTime))
                        .textStyle(.caption)
                    
                    Spacer()
                    
                    Text(formatTime(totalTime))
                        .textStyle(.caption)
                }
                
                ProgressView(value: currentTime, total: totalTime)
                    .progressViewStyle(LinearProgressViewStyle(tint: .primaryTeal))
                    .frame(height: 4)
            }
            
            // Control buttons
            HStack(spacing: Spacing.xl) {
                // Speed control
                Menu {
                    ForEach(BreathingSpeed.allCases, id: \.self) { speed in
                        Button(speed.displayName) {
                            selectedSpeed = speed
                            onSpeedChange(speed)
                        }
                    }
                } label: {
                    SerenityButton.icon("speedometer", style: .tertiary) {
                        // Menu trigger - no action needed
                    }
                }
                
                Spacer()
                
                // Stop button
                SerenityButton.icon("stop.fill", style: .tertiary) {
                    onStop()
                }
                
                // Play/Pause button
                SerenityButton.icon(
                    isPlaying ? "pause.fill" : "play.fill",
                    style: .primary,
                    size: .large
                ) {
                    onPlayPause()
                }
                
                // Settings button (placeholder)
                SerenityButton.icon("gearshape.fill", style: .tertiary) {
                    // Open settings
                }
                
                Spacer()
            }
        }
        .padding(.horizontal, Spacing.lg)
        .padding(.vertical, Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: CornerRadius.large)
                .fill(Color.cardBackground)
        )
    }
    
    private func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - Breathing Session View

struct BreathingSessionView: View {
    
    let exercise: BreathingExercise
    let duration: TimeInterval
    let onComplete: () -> Void
    let onCancel: () -> Void
    
    @State private var isPlaying = false
    @State private var currentTime: TimeInterval = 0
    @State private var currentPhase: BreathingPhase?
    @State private var breathingSpeed: BreathingSpeed = .normal
    @State private var timer: Timer?
    
    var body: some View {
        ZStack {
            Color.appBackground.ignoresSafeArea()
            
            VStack(spacing: Spacing.xl) {
                // Header
                VStack(spacing: Spacing.sm) {
                    Text(exercise.localizedName)
                        .textStyle(.primaryHeading)
                    
                    Text(exercise.exerciseCategory.displayName)
                        .textStyle(.secondaryBody)
                        .foregroundColor(.primaryTeal)
                }
                .padding(.top, Spacing.lg)
                
                Spacer()
                
                // Breathing animation
                BreathingAnimationView(
                    breathingPhases: exercise.breathingPhases,
                    speed: breathingSpeed,
                    onPhaseChange: { phase in
                        currentPhase = phase
                    },
                    onCycleComplete: {
                        // Handle cycle completion
                    }
                )
                .frame(maxWidth: .infinity, maxHeight: 400)
                
                Spacer()
                
                // Controls
                BreathingControlView(
                    isPlaying: $isPlaying,
                    currentTime: $currentTime,
                    totalTime: duration,
                    onPlayPause: togglePlayPause,
                    onStop: onCancel,
                    onSpeedChange: { speed in
                        breathingSpeed = speed
                    }
                )
            }
            .containerPadding()
        }
        .navigationBarHidden(true)
        .onAppear {
            startSession()
        }
        .onDisappear {
            stopSession()
        }
    }
    
    private func startSession() {
        isPlaying = true
        startTimer()
    }
    
    private func stopSession() {
        timer?.invalidate()
        timer = nil
    }
    
    private func togglePlayPause() {
        isPlaying.toggle()
        
        if isPlaying {
            startTimer()
        } else {
            timer?.invalidate()
        }
    }
    
    private func startTimer() {
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            currentTime += 1
            
            if currentTime >= duration {
                completeSession()
            }
        }
    }
    
    private func completeSession() {
        stopSession()
        onComplete()
    }
}

// MARK: - Preview

#Preview {
    let samplePhases = [
        BreathingPhase(type: .inhale, duration: 4, instructions: "Breathe in slowly"),
        BreathingPhase(type: .hold, duration: 4, instructions: "Hold your breath"),
        BreathingPhase(type: .exhale, duration: 4, instructions: "Breathe out slowly"),
        BreathingPhase(type: .pause, duration: 4, instructions: "Pause briefly")
    ]

    return ZStack {
        Color.appBackground.ignoresSafeArea()

        BreathingAnimationView(
            breathingPhases: samplePhases,
            speed: .normal
        )
        .frame(width: 300, height: 300)
    }
}
