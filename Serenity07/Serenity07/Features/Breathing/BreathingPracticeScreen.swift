//
//  BreathingPracticeScreen.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

/// Main breathing practice screen with exercise selection and session management
struct BreathingPracticeScreen: View {
    
    let router: NavigationRouter
    @EnvironmentObject var coreDataManager: CoreDataManager
    @StateObject private var sessionManager = PracticeSessionManager()
    @State private var selectedCategory: ExerciseCategory? = nil
    @State private var searchText = ""
    @State private var showExerciseDetail = false
    @State private var selectedExercise: BreathingExercise?
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.appBackground.ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: Spacing.lg) {
                        // Header
                        headerSection
                        
                        // Quick start section
                        quickStartSection
                        
                        // Categories
                        categoriesSection
                        
                        // Exercise list
                        exerciseListSection
                    }
                    .containerPadding()
                }
            }
            .navigationBarHidden(true)
            .sheet(isPresented: $showExerciseDetail) {
                if let exercise = selectedExercise {
                    ExerciseDetailView(
                        exercise: exercise,
                        sessionManager: sessionManager,
                        router: router
                    )
                }
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: Spacing.md) {
            HStack {
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text("Breathing Exercises")
                        .textStyle(.primaryHeading)
                    
                    Text("Choose your practice")
                        .textStyle(.secondaryBody)
                        .opacity(0.8)
                }
                
                Spacer()
                
                // Settings button
                SerenityButton.icon("gearshape.fill", style: .tertiary) {
                    router.presentSheet(.notificationSettings)
                }
            }
            
            // Search bar
            SerenityTextField.search(
                placeholder: "Search exercises...",
                text: $searchText
            )
        }
    }
    
    // MARK: - Quick Start Section
    
    private var quickStartSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Quick Start")
                .textStyle(.cardTitle)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: Spacing.md) {
                    QuickStartCard(
                        title: "5-Minute Calm",
                        subtitle: "Equal breathing for quick relaxation",
                        icon: "wind",
                        color: .primaryTeal,
                        duration: 300
                    ) {
                        startQuickSession(exerciseId: "equal_breathing", duration: 300)
                    }
                    
                    QuickStartCard(
                        title: "Stress Relief",
                        subtitle: "Box breathing for stress reduction",
                        icon: "leaf.fill",
                        color: .success,
                        duration: 600
                    ) {
                        startQuickSession(exerciseId: "box_breathing", duration: 600)
                    }
                    
                    QuickStartCard(
                        title: "Sleep Prep",
                        subtitle: "4-7-8 breathing for better sleep",
                        icon: "moon.fill",
                        color: .primaryPurple,
                        duration: 300
                    ) {
                        startQuickSession(exerciseId: "four_seven_eight", duration: 300)
                    }
                }
                .padding(.horizontal, Spacing.containerMargin)
            }
        }
    }
    
    // MARK: - Categories Section
    
    private var categoriesSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Categories")
                .textStyle(.cardTitle)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: Spacing.sm) {
                    CategoryChip(
                        category: nil,
                        isSelected: selectedCategory == nil,
                        title: "All"
                    ) {
                        selectedCategory = nil
                    }
                    
                    ForEach(ExerciseCategory.allCases, id: \.self) { category in
                        CategoryChip(
                            category: category,
                            isSelected: selectedCategory == category
                        ) {
                            selectedCategory = category
                        }
                    }
                }
                .padding(.horizontal, Spacing.containerMargin)
            }
        }
    }
    
    // MARK: - Exercise List Section
    
    private var exerciseListSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Exercises")
                .textStyle(.cardTitle)
            
            LazyVStack(spacing: Spacing.md) {
                ForEach(filteredExercises, id: \.exerciseId) { exercise in
                    ExerciseCard(exercise: exercise) {
                        selectedExercise = exercise
                        showExerciseDetail = true
                    }
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var allExercises: [BreathingExercise] {
        return coreDataManager.fetchBreathingExercises()
    }
    
    private var filteredExercises: [BreathingExercise] {
        var exercises = allExercises
        
        // Filter by category
        if let category = selectedCategory {
            exercises = exercises.filter { $0.exerciseCategory == category }
        }
        
        // Filter by search text
        if !searchText.isEmpty {
            exercises = exercises.filter { exercise in
                exercise.localizedName.localizedCaseInsensitiveContains(searchText) ||
                exercise.exerciseDescription.localizedCaseInsensitiveContains(searchText) ||
                exercise.benefits.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        return exercises
    }
    
    // MARK: - Methods
    
    private func startQuickSession(exerciseId: String, duration: Int) {
        guard let exercise = coreDataManager.getBreathingExercise(id: exerciseId) else { return }
        
        router.presentFullScreen(.breathingSession(exercise, duration: duration))
    }
}

// MARK: - Quick Start Card

struct QuickStartCard: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let duration: Int
    let onTap: () -> Void
    
    var body: some View {
        SerenityCard(style: .glass, shadow: .medium, onTap: onTap) {
            VStack(alignment: .leading, spacing: Spacing.sm) {
                HStack {
                    Image(systemName: icon)
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(color)
                    
                    Spacer()
                    
                    Text("\(duration / 60) min")
                        .textStyle(.caption)
                        .foregroundColor(.secondaryText)
                }
                
                Text(title)
                    .textStyle(.cardTitle)
                
                Text(subtitle)
                    .textStyle(.caption)
                    .foregroundColor(.secondaryText)
                    .lineLimit(2)
            }
        }
        .frame(width: 160, height: 120)
    }
}

// MARK: - Category Chip

struct CategoryChip: View {
    let category: ExerciseCategory?
    let isSelected: Bool
    let title: String?
    let onTap: () -> Void
    
    init(category: ExerciseCategory?, isSelected: Bool, title: String? = nil, onTap: @escaping () -> Void) {
        self.category = category
        self.isSelected = isSelected
        self.title = title
        self.onTap = onTap
    }
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: Spacing.xs) {
                if let category = category {
                    Image(systemName: category.iconName)
                        .font(.system(size: ComponentSize.smallIconSize))
                }
                
                Text(displayTitle)
                    .textStyle(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, Spacing.sm)
            .padding(.vertical, Spacing.xs)
            .background(backgroundColor)
            .foregroundColor(textColor)
            .cornerRadius(CornerRadius.small)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.small)
                    .stroke(borderColor, lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var displayTitle: String {
        return title ?? category?.displayName ?? "All"
    }
    
    private var backgroundColor: Color {
        return isSelected ? .primaryTeal : .cardBackground
    }
    
    private var textColor: Color {
        return isSelected ? .white : .primaryText
    }
    
    private var borderColor: Color {
        return isSelected ? .primaryTeal : .mediumGray.opacity(0.3)
    }
}

// MARK: - Exercise Detail View

struct ExerciseDetailView: View {
    let exercise: BreathingExercise
    let sessionManager: PracticeSessionManager
    let router: NavigationRouter
    
    @Environment(\.dismiss) private var dismiss
    @State private var selectedDuration: Int
    @State private var showSessionPreview = false
    
    init(exercise: BreathingExercise, sessionManager: PracticeSessionManager, router: NavigationRouter) {
        self.exercise = exercise
        self.sessionManager = sessionManager
        self.router = router
        self._selectedDuration = State(initialValue: exercise.availableDurations.first ?? 300)
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.appBackground.ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: Spacing.lg) {
                        // Exercise header
                        exerciseHeader
                        
                        // Duration selection
                        durationSelection
                        
                        // Exercise details
                        exerciseDetails
                        
                        // Benefits
                        benefitsSection
                        
                        // Instructions
                        instructionsSection
                        
                        Spacer(minLength: Spacing.xl)
                    }
                    .containerPadding()
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    SerenityButton.primary("Start Practice") {
                        startPractice()
                    }
                }
            }
        }
    }
    
    private var exerciseHeader: some View {
        VStack(spacing: Spacing.md) {
            // Exercise icon
            ZStack {
                Circle()
                    .fill(Color.primaryTeal.opacity(0.2))
                    .frame(width: 80, height: 80)
                
                Image(systemName: exercise.exerciseCategory.iconName)
                    .font(.system(size: 32, weight: .medium))
                    .foregroundColor(.primaryTeal)
            }
            
            // Exercise info
            VStack(spacing: Spacing.xs) {
                Text(exercise.localizedName)
                    .textStyle(.primaryHeading)
                    .multilineTextAlignment(.center)
                
                HStack {
                    DifficultyBadge(difficulty: exercise.difficultyLevel)
                    
                    Text("•")
                        .foregroundColor(.mediumGray)
                    
                    Text(exercise.exerciseCategory.displayName)
                        .textStyle(.caption)
                        .foregroundColor(.primaryTeal)
                }
            }
            
            Text(exercise.exerciseDescription)
                .textStyle(.secondaryBody)
                .multilineTextAlignment(.center)
                .opacity(0.8)
        }
    }
    
    private var durationSelection: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text("Duration")
                .textStyle(.cardTitle)
            
            HStack(spacing: Spacing.sm) {
                ForEach(exercise.availableDurations, id: \.self) { duration in
                    DurationChip(
                        duration: duration,
                        isSelected: selectedDuration == duration
                    ) {
                        selectedDuration = duration
                    }
                }
                
                Spacer()
            }
        }
    }
    
    private var exerciseDetails: some View {
        SerenityCard(style: .standard) {
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text("Exercise Pattern")
                    .textStyle(.cardTitle)
                
                ForEach(Array(exercise.breathingPhases.enumerated()), id: \.offset) { index, phase in
                    HStack {
                        Text("\(index + 1).")
                            .textStyle(.caption)
                            .foregroundColor(.mediumGray)
                        
                        Text(phase.type.displayName)
                            .textStyle(.secondaryBody)
                        
                        Spacer()
                        
                        Text("\(phase.duration)s")
                            .textStyle(.caption)
                            .foregroundColor(.primaryTeal)
                    }
                }
            }
        }
    }
    
    private var benefitsSection: some View {
        SerenityCard(style: .standard) {
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text("Benefits")
                    .textStyle(.cardTitle)
                
                ForEach(exercise.benefitsList, id: \.self) { benefit in
                    HStack(alignment: .top, spacing: Spacing.xs) {
                        Text("•")
                            .textStyle(.secondaryBody)
                            .foregroundColor(.primaryTeal)
                        
                        Text(benefit)
                            .textStyle(.secondaryBody)
                    }
                }
            }
        }
    }
    
    private var instructionsSection: some View {
        SerenityCard(style: .standard) {
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text("Instructions")
                    .textStyle(.cardTitle)
                
                ForEach(Array(exercise.instructionsList.enumerated()), id: \.offset) { index, instruction in
                    HStack(alignment: .top, spacing: Spacing.sm) {
                        Text("\(index + 1)")
                            .textStyle(.caption)
                            .foregroundColor(.primaryTeal)
                            .frame(width: 20, alignment: .leading)
                        
                        Text(instruction)
                            .textStyle(.secondaryBody)
                    }
                }
            }
        }
    }
    
    private func startPractice() {
        dismiss()
        router.presentFullScreen(.breathingSession(exercise, duration: selectedDuration))
    }
}

// MARK: - Duration Chip

struct DurationChip: View {
    let duration: Int
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            Text("\(duration / 60) min")
                .textStyle(.caption)
                .fontWeight(.medium)
                .padding(.horizontal, Spacing.md)
                .padding(.vertical, Spacing.xs)
                .background(backgroundColor)
                .foregroundColor(textColor)
                .cornerRadius(CornerRadius.small)
                .overlay(
                    RoundedRectangle(cornerRadius: CornerRadius.small)
                        .stroke(borderColor, lineWidth: 1)
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var backgroundColor: Color {
        return isSelected ? .primaryTeal : .cardBackground
    }
    
    private var textColor: Color {
        return isSelected ? .white : .primaryText
    }
    
    private var borderColor: Color {
        return isSelected ? .primaryTeal : .mediumGray.opacity(0.3)
    }
}

// MARK: - Preview

#Preview {
    BreathingPracticeScreen(router: NavigationRouter())
        .environmentObject(CoreDataManager.shared)
}
