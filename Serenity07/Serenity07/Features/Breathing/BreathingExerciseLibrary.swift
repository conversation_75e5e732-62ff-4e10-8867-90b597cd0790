//
//  BreathingExerciseLibrary.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData

/// Comprehensive library of breathing exercises with predefined patterns and configurations
class BreathingExerciseLibrary {
    
    static let shared = BreathingExerciseLibrary()
    
    private init() {}
    
    // MARK: - Exercise Creation
    
    /// Create all predefined breathing exercises in Core Data
    func createAllExercises(in context: NSManagedObjectContext) {
        let exercises = getAllExerciseDefinitions()
        
        for exerciseData in exercises {
            let exercise = BreathingExercise(context: context)
            exercise.exerciseId = exerciseData.id
            exercise.name = exerciseData.name
            exercise.localizedName = exerciseData.localizedName
            exercise.exerciseDescription = exerciseData.description
            exercise.instructions = exerciseData.instructions.joined(separator: "\n")
            exercise.benefits = exerciseData.benefits.joined(separator: ", ")
            exercise.difficultyLevel = exerciseData.difficulty
            exercise.exerciseCategory = exerciseData.category
            exercise.isActive = true
            exercise.isPremium = exerciseData.isPremium
            exercise.sortOrder = Int32(exerciseData.sortOrder)
            exercise.createdDate = Date()
            exercise.updatedDate = Date()
            
            // Encode patterns and durations as JSON
            if let patternData = try? JSONEncoder().encode(exerciseData.pattern),
               let patternString = String(data: patternData, encoding: .utf8) {
                exercise.breathingPattern = patternString
            }
            
            if let durationData = try? JSONEncoder().encode(exerciseData.durations),
               let durationString = String(data: durationData, encoding: .utf8) {
                exercise.durationOptions = durationString
            }
        }
    }
    
    // MARK: - Exercise Definitions
    
    private func getAllExerciseDefinitions() -> [ExerciseDefinition] {
        return [
            // Beginner Exercises
            createEqualBreathingExercise(),
            createSimpleBreathingExercise(),
            createRelaxationBreathingExercise(),
            
            // Intermediate Exercises
            createBoxBreathingExercise(),
            createFourSevenEightExercise(),
            createTriangleBreathingExercise(),
            
            // Advanced Exercises
            createWimHofBreathingExercise(),
            createAlternateNostrilExercise(),
            createBreathOfFireExercise(),
            
            // Specialized Exercises
            createStressReliefExercise(),
            createSleepBreathingExercise(),
            createEnergyBoostExercise(),
            createFocusBreathingExercise(),
            createAnxietyReliefExercise()
        ]
    }
    
    // MARK: - Individual Exercise Definitions
    
    private func createEqualBreathingExercise() -> ExerciseDefinition {
        return ExerciseDefinition(
            id: "equal_breathing",
            name: "Equal Breathing",
            localizedName: NSLocalizedString("Equal Breathing", comment: "Equal breathing exercise"),
            description: "A foundational breathing technique where inhale and exhale are of equal length, promoting balance and calm.",
            instructions: [
                "Sit comfortably with your spine straight",
                "Breathe in slowly through your nose for 4 counts",
                "Breathe out slowly through your nose for 4 counts",
                "Continue this rhythm, keeping inhale and exhale equal",
                "Focus on the smooth, steady flow of breath"
            ],
            benefits: [
                "Reduces stress and anxiety",
                "Improves focus and concentration",
                "Balances the nervous system",
                "Promotes relaxation"
            ],
            difficulty: .beginner,
            category: .general,
            pattern: [
                BreathingPhase(type: .inhale, duration: 4, instructions: "Breathe in slowly through your nose"),
                BreathingPhase(type: .exhale, duration: 4, instructions: "Breathe out slowly through your nose")
            ],
            durations: [300, 600, 900, 1200], // 5, 10, 15, 20 minutes
            isPremium: false,
            sortOrder: 1
        )
    }
    
    private func createBoxBreathingExercise() -> ExerciseDefinition {
        return ExerciseDefinition(
            id: "box_breathing",
            name: "Box Breathing",
            localizedName: NSLocalizedString("Box Breathing", comment: "Box breathing exercise"),
            description: "A powerful stress-relief technique used by Navy SEALs and athletes to enhance performance and reduce stress.",
            instructions: [
                "Sit upright with feet flat on the floor",
                "Exhale completely through your mouth",
                "Inhale through your nose for 4 counts",
                "Hold your breath for 4 counts",
                "Exhale through your mouth for 4 counts",
                "Hold empty for 4 counts",
                "Repeat the cycle"
            ],
            benefits: [
                "Reduces stress and cortisol levels",
                "Improves concentration and performance",
                "Enhances emotional regulation",
                "Builds mental resilience"
            ],
            difficulty: .intermediate,
            category: .stress,
            pattern: [
                BreathingPhase(type: .inhale, duration: 4, instructions: "Breathe in slowly through your nose"),
                BreathingPhase(type: .hold, duration: 4, instructions: "Hold your breath gently"),
                BreathingPhase(type: .exhale, duration: 4, instructions: "Breathe out through your mouth"),
                BreathingPhase(type: .pause, duration: 4, instructions: "Hold empty, stay relaxed")
            ],
            durations: [300, 600, 900],
            isPremium: false,
            sortOrder: 2
        )
    }
    
    private func createFourSevenEightExercise() -> ExerciseDefinition {
        return ExerciseDefinition(
            id: "four_seven_eight",
            name: "4-7-8 Breathing",
            localizedName: NSLocalizedString("4-7-8 Breathing", comment: "4-7-8 breathing exercise"),
            description: "Dr. Andrew Weil's natural tranquilizer for the nervous system, perfect for sleep and anxiety relief.",
            instructions: [
                "Place the tip of your tongue behind your upper front teeth",
                "Exhale completely through your mouth with a whoosh sound",
                "Close your mouth and inhale through your nose for 4 counts",
                "Hold your breath for 7 counts",
                "Exhale through your mouth for 8 counts with a whoosh sound",
                "Repeat for 3-4 cycles initially"
            ],
            benefits: [
                "Promotes deep sleep",
                "Reduces anxiety and stress",
                "Calms the nervous system",
                "Helps with insomnia"
            ],
            difficulty: .intermediate,
            category: .sleep,
            pattern: [
                BreathingPhase(type: .inhale, duration: 4, instructions: "Breathe in quietly through your nose"),
                BreathingPhase(type: .hold, duration: 7, instructions: "Hold your breath completely"),
                BreathingPhase(type: .exhale, duration: 8, instructions: "Exhale through your mouth with a whoosh")
            ],
            durations: [180, 300, 600], // 3, 5, 10 minutes
            isPremium: false,
            sortOrder: 3
        )
    }
    
    private func createWimHofBreathingExercise() -> ExerciseDefinition {
        return ExerciseDefinition(
            id: "wim_hof_breathing",
            name: "Wim Hof Method",
            localizedName: NSLocalizedString("Wim Hof Method", comment: "Wim Hof breathing exercise"),
            description: "A powerful breathing technique that can boost energy, reduce stress, and strengthen the immune system.",
            instructions: [
                "Sit or lie down comfortably",
                "Take 30-40 deep, powerful breaths",
                "Breathe in fully, breathe out without force",
                "After the last exhale, hold your breath as long as comfortable",
                "When you need to breathe, take a deep breath and hold for 15 seconds",
                "Repeat for 3-4 rounds"
            ],
            benefits: [
                "Increases energy and alertness",
                "Strengthens immune system",
                "Improves stress resilience",
                "Enhances mental clarity",
                "Boosts mood and confidence"
            ],
            difficulty: .advanced,
            category: .energy,
            pattern: [
                BreathingPhase(type: .inhale, duration: 2, instructions: "Deep, powerful inhale"),
                BreathingPhase(type: .exhale, duration: 2, instructions: "Let go without force")
            ],
            durations: [600, 900, 1200], // 10, 15, 20 minutes
            isPremium: true,
            sortOrder: 4
        )
    }
    
    private func createAlternateNostrilExercise() -> ExerciseDefinition {
        return ExerciseDefinition(
            id: "alternate_nostril",
            name: "Alternate Nostril Breathing",
            localizedName: NSLocalizedString("Alternate Nostril Breathing", comment: "Alternate nostril breathing exercise"),
            description: "An ancient yogic breathing technique that balances the nervous system and enhances mental clarity.",
            instructions: [
                "Sit comfortably with spine straight",
                "Use your right thumb to close your right nostril",
                "Inhale through your left nostril for 4 counts",
                "Close left nostril with ring finger, release thumb",
                "Exhale through right nostril for 4 counts",
                "Inhale through right nostril for 4 counts",
                "Close right nostril, release left nostril",
                "Exhale through left nostril for 4 counts",
                "This completes one cycle"
            ],
            benefits: [
                "Balances left and right brain hemispheres",
                "Reduces stress and anxiety",
                "Improves concentration",
                "Enhances respiratory function",
                "Promotes mental clarity"
            ],
            difficulty: .advanced,
            category: .focus,
            pattern: [
                BreathingPhase(type: .inhale, duration: 4, instructions: "Inhale through left nostril"),
                BreathingPhase(type: .exhale, duration: 4, instructions: "Exhale through right nostril"),
                BreathingPhase(type: .inhale, duration: 4, instructions: "Inhale through right nostril"),
                BreathingPhase(type: .exhale, duration: 4, instructions: "Exhale through left nostril")
            ],
            durations: [300, 600, 900],
            isPremium: true,
            sortOrder: 5
        )
    }
    
    private func createStressReliefExercise() -> ExerciseDefinition {
        return ExerciseDefinition(
            id: "stress_relief",
            name: "Stress Relief Breathing",
            localizedName: NSLocalizedString("Stress Relief Breathing", comment: "Stress relief breathing exercise"),
            description: "A gentle breathing pattern specifically designed to activate the parasympathetic nervous system and reduce stress.",
            instructions: [
                "Find a comfortable seated position",
                "Place one hand on chest, one on belly",
                "Breathe in slowly through nose for 6 counts",
                "Feel your belly rise more than your chest",
                "Pause briefly at the top",
                "Exhale slowly through mouth for 8 counts",
                "Let all tension release with each exhale"
            ],
            benefits: [
                "Rapidly reduces stress hormones",
                "Activates relaxation response",
                "Lowers blood pressure",
                "Reduces muscle tension",
                "Improves emotional regulation"
            ],
            difficulty: .beginner,
            category: .stress,
            pattern: [
                BreathingPhase(type: .inhale, duration: 6, instructions: "Breathe in slowly, belly rising"),
                BreathingPhase(type: .pause, duration: 1, instructions: "Brief pause at the top"),
                BreathingPhase(type: .exhale, duration: 8, instructions: "Exhale slowly, releasing tension")
            ],
            durations: [300, 600, 900, 1200],
            isPremium: false,
            sortOrder: 6
        )
    }
    
    private func createSleepBreathingExercise() -> ExerciseDefinition {
        return ExerciseDefinition(
            id: "sleep_breathing",
            name: "Sleep Preparation",
            localizedName: NSLocalizedString("Sleep Preparation", comment: "Sleep preparation breathing exercise"),
            description: "A calming breathing sequence designed to prepare your body and mind for restful sleep.",
            instructions: [
                "Lie down comfortably in bed",
                "Close your eyes and relax your body",
                "Breathe in slowly through nose for 5 counts",
                "Hold gently for 3 counts",
                "Exhale slowly through mouth for 7 counts",
                "With each exhale, feel yourself sinking deeper into relaxation",
                "Continue until you feel drowsy"
            ],
            benefits: [
                "Promotes faster sleep onset",
                "Improves sleep quality",
                "Reduces bedtime anxiety",
                "Calms racing thoughts",
                "Relaxes physical tension"
            ],
            difficulty: .beginner,
            category: .sleep,
            pattern: [
                BreathingPhase(type: .inhale, duration: 5, instructions: "Breathe in slowly and deeply"),
                BreathingPhase(type: .hold, duration: 3, instructions: "Hold gently, stay relaxed"),
                BreathingPhase(type: .exhale, duration: 7, instructions: "Exhale slowly, releasing the day")
            ],
            durations: [300, 600, 900],
            isPremium: false,
            sortOrder: 7
        )
    }
    
    // Additional exercises would be implemented similarly...
    private func createSimpleBreathingExercise() -> ExerciseDefinition {
        return ExerciseDefinition(
            id: "simple_breathing",
            name: "Simple Deep Breathing",
            localizedName: NSLocalizedString("Simple Deep Breathing", comment: "Simple deep breathing exercise"),
            description: "The most basic and accessible breathing exercise for beginners.",
            instructions: [
                "Sit or stand comfortably",
                "Breathe in slowly through your nose",
                "Breathe out slowly through your mouth",
                "Focus on making each breath deeper than normal",
                "Continue at your own pace"
            ],
            benefits: ["Reduces immediate stress", "Easy to do anywhere", "Improves oxygen flow"],
            difficulty: .beginner,
            category: .general,
            pattern: [
                BreathingPhase(type: .inhale, duration: 3, instructions: "Breathe in slowly"),
                BreathingPhase(type: .exhale, duration: 3, instructions: "Breathe out slowly")
            ],
            durations: [180, 300, 600],
            isPremium: false,
            sortOrder: 8
        )
    }
    
    private func createRelaxationBreathingExercise() -> ExerciseDefinition {
        return ExerciseDefinition(
            id: "relaxation_breathing",
            name: "Progressive Relaxation",
            localizedName: NSLocalizedString("Progressive Relaxation", comment: "Progressive relaxation breathing"),
            description: "Combines breathing with progressive muscle relaxation for deep calm.",
            instructions: [
                "Lie down comfortably",
                "Start with deep, slow breaths",
                "With each exhale, relax a different body part",
                "Begin with your toes and work upward",
                "Continue until your whole body is relaxed"
            ],
            benefits: ["Deep physical relaxation", "Reduces muscle tension", "Promotes better sleep"],
            difficulty: .beginner,
            category: .general,
            pattern: [
                BreathingPhase(type: .inhale, duration: 4, instructions: "Breathe in, tense muscles"),
                BreathingPhase(type: .exhale, duration: 6, instructions: "Breathe out, release tension")
            ],
            durations: [600, 900, 1200],
            isPremium: false,
            sortOrder: 9
        )
    }
    
    private func createTriangleBreathingExercise() -> ExerciseDefinition {
        return ExerciseDefinition(
            id: "triangle_breathing",
            name: "Triangle Breathing",
            localizedName: NSLocalizedString("Triangle Breathing", comment: "Triangle breathing exercise"),
            description: "A three-part breathing pattern that builds focus and control.",
            instructions: [
                "Sit with good posture",
                "Inhale for 4 counts",
                "Hold for 4 counts", 
                "Exhale for 4 counts",
                "No pause between cycles",
                "Visualize drawing a triangle with your breath"
            ],
            benefits: ["Improves breath control", "Enhances concentration", "Builds breathing stamina"],
            difficulty: .intermediate,
            category: .focus,
            pattern: [
                BreathingPhase(type: .inhale, duration: 4, instructions: "Breathe in steadily"),
                BreathingPhase(type: .hold, duration: 4, instructions: "Hold with control"),
                BreathingPhase(type: .exhale, duration: 4, instructions: "Exhale completely")
            ],
            durations: [300, 600, 900],
            isPremium: false,
            sortOrder: 10
        )
    }
    
    private func createBreathOfFireExercise() -> ExerciseDefinition {
        return ExerciseDefinition(
            id: "breath_of_fire",
            name: "Breath of Fire",
            localizedName: NSLocalizedString("Breath of Fire", comment: "Breath of fire exercise"),
            description: "An energizing yogic breathing technique that builds heat and energy.",
            instructions: [
                "Sit with spine straight",
                "Begin with normal breathing",
                "Start rapid, rhythmic breathing through nose",
                "Focus on forceful exhales, passive inhales",
                "Keep shoulders and chest relatively still",
                "Start with 30 seconds, build gradually"
            ],
            benefits: ["Increases energy", "Improves lung capacity", "Builds core strength", "Enhances mental alertness"],
            difficulty: .advanced,
            category: .energy,
            pattern: [
                BreathingPhase(type: .inhale, duration: 1, instructions: "Quick passive inhale"),
                BreathingPhase(type: .exhale, duration: 1, instructions: "Forceful exhale through nose")
            ],
            durations: [300, 600],
            isPremium: true,
            sortOrder: 11
        )
    }
    
    private func createEnergyBoostExercise() -> ExerciseDefinition {
        return ExerciseDefinition(
            id: "energy_boost",
            name: "Energy Boost",
            localizedName: NSLocalizedString("Energy Boost", comment: "Energy boost breathing"),
            description: "A revitalizing breathing pattern to increase alertness and energy.",
            instructions: [
                "Stand or sit upright",
                "Take 3 deep preparatory breaths",
                "Inhale quickly and deeply",
                "Exhale with force through mouth",
                "Repeat 10-15 times rapidly",
                "End with 3 slow, deep breaths"
            ],
            benefits: ["Increases alertness", "Boosts energy levels", "Improves circulation", "Enhances mood"],
            difficulty: .intermediate,
            category: .energy,
            pattern: [
                BreathingPhase(type: .inhale, duration: 2, instructions: "Quick, deep inhale"),
                BreathingPhase(type: .exhale, duration: 2, instructions: "Forceful exhale")
            ],
            durations: [180, 300, 600],
            isPremium: false,
            sortOrder: 12
        )
    }
    
    private func createFocusBreathingExercise() -> ExerciseDefinition {
        return ExerciseDefinition(
            id: "focus_breathing",
            name: "Focus Enhancement",
            localizedName: NSLocalizedString("Focus Enhancement", comment: "Focus enhancement breathing"),
            description: "A concentration-building breathing technique for mental clarity.",
            instructions: [
                "Sit comfortably with eyes closed",
                "Count each breath from 1 to 10",
                "Inhale on odd numbers, exhale on even",
                "If you lose count, start over at 1",
                "Focus completely on the counting",
                "Gradually extend the practice"
            ],
            benefits: ["Improves concentration", "Enhances mental clarity", "Builds mindfulness", "Reduces mental chatter"],
            difficulty: .intermediate,
            category: .focus,
            pattern: [
                BreathingPhase(type: .inhale, duration: 4, instructions: "Inhale while counting"),
                BreathingPhase(type: .exhale, duration: 4, instructions: "Exhale while counting")
            ],
            durations: [300, 600, 900, 1200],
            isPremium: false,
            sortOrder: 13
        )
    }
    
    private func createAnxietyReliefExercise() -> ExerciseDefinition {
        return ExerciseDefinition(
            id: "anxiety_relief",
            name: "Anxiety Relief",
            localizedName: NSLocalizedString("Anxiety Relief", comment: "Anxiety relief breathing"),
            description: "A calming breathing pattern specifically designed to reduce anxiety and worry.",
            instructions: [
                "Find a quiet, safe space",
                "Place feet flat on floor, hands on thighs",
                "Breathe in slowly for 4 counts",
                "Hold gently for 2 counts",
                "Exhale slowly for 6 counts",
                "Focus on the exhale releasing worry",
                "Continue until you feel calmer"
            ],
            benefits: ["Reduces anxiety symptoms", "Calms nervous system", "Decreases worry", "Promotes emotional stability"],
            difficulty: .beginner,
            category: .anxiety,
            pattern: [
                BreathingPhase(type: .inhale, duration: 4, instructions: "Breathe in calmly"),
                BreathingPhase(type: .hold, duration: 2, instructions: "Hold gently"),
                BreathingPhase(type: .exhale, duration: 6, instructions: "Exhale worry away")
            ],
            durations: [300, 600, 900],
            isPremium: false,
            sortOrder: 14
        )
    }
}

// MARK: - Exercise Definition Structure

struct ExerciseDefinition {
    let id: String
    let name: String
    let localizedName: String
    let description: String
    let instructions: [String]
    let benefits: [String]
    let difficulty: ExerciseDifficulty
    let category: ExerciseCategory
    let pattern: [BreathingPhase]
    let durations: [Int] // in seconds
    let isPremium: Bool
    let sortOrder: Int
}
