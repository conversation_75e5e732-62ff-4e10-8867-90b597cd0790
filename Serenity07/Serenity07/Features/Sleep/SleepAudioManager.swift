//
//  SleepAudioManager.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import SwiftUI
import AVFoundation
import MediaPlayer

/// Manages sleep audio playback with advanced features like sleep timer, fade out, and background play
@MainActor
class SleepAudioManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var isPlaying: Bool = false
    @Published var isPaused: Bool = false
    @Published var currentContent: SleepContent?
    @Published var currentTime: TimeInterval = 0
    @Published var duration: TimeInterval = 0
    @Published var volume: Float = 0.7
    @Published var playbackRate: Float = 1.0
    @Published var sleepTimer: TimeInterval = 0 // 0 means no timer
    @Published var sleepTimerRemaining: TimeInterval = 0
    @Published var fadeOutDuration: TimeInterval = 300 // 5 minutes default
    @Published var isLooping: Bool = false
    @Published var showingNowPlaying: Bool = false
    
    // MARK: - Private Properties
    
    private var audioPlayer: AVAudioPlayer?
    private var playbackTimer: Timer?
    private var sleepTimerTimer: Timer?
    private var fadeOutTimer: Timer?
    private var originalVolume: Float = 0.7
    private let coreDataManager = CoreDataManager.shared
    
    // MARK: - Initialization
    
    init() {
        setupAudioSession()
        setupRemoteTransportControls()
        setupNotifications()
    }
    
    deinit {
        stopPlayback()
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Audio Session Setup
    
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playback, mode: .default, options: [.allowAirPlay, .allowBluetooth])
            try audioSession.setActive(true)
        } catch {
            print("Error setting up audio session: \(error)")
        }
    }
    
    private func setupRemoteTransportControls() {
        let commandCenter = MPRemoteCommandCenter.shared()
        
        commandCenter.playCommand.addTarget { [weak self] _ in
            self?.resumePlayback()
            return .success
        }
        
        commandCenter.pauseCommand.addTarget { [weak self] _ in
            self?.pausePlayback()
            return .success
        }
        
        commandCenter.stopCommand.addTarget { [weak self] _ in
            self?.stopPlayback()
            return .success
        }
        
        commandCenter.nextTrackCommand.isEnabled = false
        commandCenter.previousTrackCommand.isEnabled = false
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleInterruption),
            name: AVAudioSession.interruptionNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleRouteChange),
            name: AVAudioSession.routeChangeNotification,
            object: nil
        )
    }
    
    // MARK: - Playback Control
    
    /// Start playing sleep content
    func playContent(_ content: SleepContent) {
        guard let url = Bundle.main.url(forResource: content.audioUrl.replacingOccurrences(of: ".mp3", with: ""), withExtension: "mp3") else {
            print("Audio file not found: \(content.audioUrl)")
            return
        }
        
        do {
            // Stop current playback
            stopPlayback()
            
            // Create new audio player
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.delegate = self
            audioPlayer?.prepareToPlay()
            audioPlayer?.volume = volume
            audioPlayer?.rate = playbackRate
            audioPlayer?.numberOfLoops = isLooping ? -1 : 0
            
            // Update state
            currentContent = content
            duration = audioPlayer?.duration ?? 0
            currentTime = 0
            
            // Start playback
            audioPlayer?.play()
            isPlaying = true
            isPaused = false
            
            // Start timers
            startPlaybackTimer()
            
            // Update now playing info
            updateNowPlayingInfo()
            
            // Increment play count
            content.incrementPlayCount()
            saveContext()
            
            print("Started playing: \(content.localizedTitle)")
            
        } catch {
            print("Error playing audio: \(error)")
        }
    }
    
    /// Pause playback
    func pausePlayback() {
        guard isPlaying else { return }
        
        audioPlayer?.pause()
        isPlaying = false
        isPaused = true
        
        stopTimers()
        updateNowPlayingInfo()
        
        print("Playback paused")
    }
    
    /// Resume playback
    func resumePlayback() {
        guard isPaused, let player = audioPlayer else { return }
        
        player.play()
        isPlaying = true
        isPaused = false
        
        startPlaybackTimer()
        updateNowPlayingInfo()
        
        print("Playback resumed")
    }
    
    /// Stop playback
    func stopPlayback() {
        audioPlayer?.stop()
        audioPlayer = nil
        
        isPlaying = false
        isPaused = false
        currentTime = 0
        duration = 0
        
        stopTimers()
        clearNowPlayingInfo()
        
        print("Playback stopped")
    }
    
    /// Seek to specific time
    func seek(to time: TimeInterval) {
        guard let player = audioPlayer else { return }
        
        player.currentTime = time
        currentTime = time
        updateNowPlayingInfo()
    }
    
    /// Set playback volume
    func setVolume(_ newVolume: Float) {
        volume = newVolume
        audioPlayer?.volume = newVolume
        originalVolume = newVolume
    }
    
    /// Set playback rate
    func setPlaybackRate(_ rate: Float) {
        playbackRate = rate
        audioPlayer?.rate = rate
    }
    
    /// Toggle looping
    func toggleLooping() {
        isLooping.toggle()
        audioPlayer?.numberOfLoops = isLooping ? -1 : 0
    }
    
    // MARK: - Sleep Timer
    
    /// Set sleep timer
    func setSleepTimer(minutes: Int) {
        sleepTimer = TimeInterval(minutes * 60)
        sleepTimerRemaining = sleepTimer
        
        if sleepTimer > 0 {
            startSleepTimer()
        } else {
            stopSleepTimer()
        }
    }
    
    /// Cancel sleep timer
    func cancelSleepTimer() {
        sleepTimer = 0
        sleepTimerRemaining = 0
        stopSleepTimer()
    }
    
    private func startSleepTimer() {
        stopSleepTimer()
        
        sleepTimerTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateSleepTimer()
            }
        }
    }
    
    private func stopSleepTimer() {
        sleepTimerTimer?.invalidate()
        sleepTimerTimer = nil
    }
    
    private func updateSleepTimer() {
        guard sleepTimerRemaining > 0 else {
            // Timer finished - start fade out
            startFadeOut()
            stopSleepTimer()
            return
        }
        
        sleepTimerRemaining -= 1
    }
    
    // MARK: - Fade Out
    
    private func startFadeOut() {
        guard isPlaying, let player = audioPlayer else { return }
        
        originalVolume = player.volume
        let fadeSteps = Int(fadeOutDuration)
        let volumeDecrement = originalVolume / Float(fadeSteps)
        
        fadeOutTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] timer in
            Task { @MainActor in
                guard let self = self, let player = self.audioPlayer else {
                    timer.invalidate()
                    return
                }
                
                let newVolume = max(0, player.volume - volumeDecrement)
                player.volume = newVolume
                
                if newVolume <= 0 {
                    timer.invalidate()
                    self.stopPlayback()
                }
            }
        }
    }
    
    // MARK: - Timer Management
    
    private func startPlaybackTimer() {
        stopPlaybackTimer()
        
        playbackTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updatePlaybackTime()
            }
        }
    }
    
    private func stopPlaybackTimer() {
        playbackTimer?.invalidate()
        playbackTimer = nil
    }
    
    private func updatePlaybackTime() {
        guard let player = audioPlayer, isPlaying else { return }
        
        currentTime = player.currentTime
        updateNowPlayingInfo()
    }
    
    private func stopTimers() {
        stopPlaybackTimer()
        fadeOutTimer?.invalidate()
        fadeOutTimer = nil
    }
    
    // MARK: - Now Playing Info
    
    private func updateNowPlayingInfo() {
        guard let content = currentContent else { return }
        
        var nowPlayingInfo = [String: Any]()
        nowPlayingInfo[MPMediaItemPropertyTitle] = content.localizedTitle
        nowPlayingInfo[MPMediaItemPropertyArtist] = content.narrator ?? "Serenity"
        nowPlayingInfo[MPMediaItemPropertyAlbumTitle] = content.sleepContentType.displayName
        nowPlayingInfo[MPMediaItemPropertyPlaybackDuration] = duration
        nowPlayingInfo[MPNowPlayingInfoPropertyElapsedPlaybackTime] = currentTime
        nowPlayingInfo[MPNowPlayingInfoPropertyPlaybackRate] = isPlaying ? playbackRate : 0.0
        
        // Add artwork if available
        if let imageUrl = content.imageUrl,
           let image = UIImage(named: imageUrl) {
            nowPlayingInfo[MPMediaItemPropertyArtwork] = MPMediaItemArtwork(boundsSize: image.size) { _ in image }
        }
        
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nowPlayingInfo
    }
    
    private func clearNowPlayingInfo() {
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nil
    }
    
    // MARK: - Notification Handlers
    
    @objc private func handleInterruption(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }
        
        switch type {
        case .began:
            pausePlayback()
        case .ended:
            if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                if options.contains(.shouldResume) {
                    resumePlayback()
                }
            }
        @unknown default:
            break
        }
    }
    
    @objc private func handleRouteChange(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let reasonValue = userInfo[AVAudioSessionRouteChangeReasonKey] as? UInt,
              let reason = AVAudioSession.RouteChangeReason(rawValue: reasonValue) else {
            return
        }
        
        switch reason {
        case .oldDeviceUnavailable:
            // Headphones were unplugged
            pausePlayback()
        default:
            break
        }
    }
    
    // MARK: - Utility Methods
    
    /// Get formatted time string
    func getFormattedTime(_ time: TimeInterval) -> String {
        let hours = Int(time) / 3600
        let minutes = Int(time) % 3600 / 60
        let seconds = Int(time) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%d:%02d", minutes, seconds)
        }
    }
    
    /// Get formatted sleep timer remaining
    func getFormattedSleepTimer() -> String {
        let minutes = Int(sleepTimerRemaining) / 60
        let seconds = Int(sleepTimerRemaining) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    /// Check if content is currently playing
    func isContentPlaying(_ content: SleepContent) -> Bool {
        return currentContent?.contentId == content.contentId && isPlaying
    }
    
    /// Check if content is currently paused
    func isContentPaused(_ content: SleepContent) -> Bool {
        return currentContent?.contentId == content.contentId && isPaused
    }
    
    private func saveContext() {
        do {
            try coreDataManager.viewContext.save()
        } catch {
            print("Error saving context: \(error)")
        }
    }
}

// MARK: - AVAudioPlayerDelegate

extension SleepAudioManager: AVAudioPlayerDelegate {
    
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        if flag && !isLooping {
            stopPlayback()
        }
    }
    
    func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        print("Audio player decode error: \(error?.localizedDescription ?? "Unknown error")")
        stopPlayback()
    }
}
