//
//  SleepContentLibrary.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData

/// Library of predefined sleep content including stories, soundscapes, and audio
class SleepContentLibrary {
    
    /// Load default sleep content into Core Data
    static func loadDefaultContent(into context: NSManagedObjectContext) {
        // Check if content already exists
        let request: NSFetchRequest<SleepContent> = SleepContent.fetchRequest()
        
        do {
            let existingContent = try context.fetch(request)
            if !existingContent.isEmpty {
                print("Sleep content already loaded")
                return
            }
        } catch {
            print("Error checking existing sleep content: \(error)")
        }
        
        // Create default content
        let contentItems = createDefaultContent()
        
        for (index, contentData) in contentItems.enumerated() {
            let content = SleepContent(context: context)
            content.contentId = contentData.id
            content.title = contentData.title
            content.localizedTitle = NSLocalizedString(contentData.title, comment: "Sleep content title")
            content.contentDescription = NSLocalizedString(contentData.description, comment: "Sleep content description")
            content.narrator = contentData.narrator
            content.duration = Int32(contentData.duration)
            content.sleepContentType = contentData.contentType
            content.sleepCategory = contentData.category
            content.audioUrl = contentData.audioUrl
            content.imageUrl = contentData.imageUrl
            content.thumbnailUrl = contentData.thumbnailUrl
            content.isActive = true
            content.isPremium = contentData.isPremium
            content.sortOrder = Int32(index)
            content.playCount = 0
            content.averageRating = contentData.averageRating
            content.tags = contentData.tagsJSON
            content.createdDate = Date()
            content.updatedDate = Date()
            content.metadata = contentData.metadataJSON
        }
        
        // Save context
        do {
            try context.save()
            print("Successfully loaded \(contentItems.count) sleep content items")
        } catch {
            print("Error saving sleep content: \(error)")
        }
    }
    
    /// Create default sleep content
    private static func createDefaultContent() -> [SleepContentData] {
        return [
            // Sleep Stories
            SleepContentData(
                id: "enchanted_forest",
                title: "The Enchanted Forest",
                description: "A peaceful journey through a magical forest where time moves slowly and worries fade away",
                narrator: "Sarah Mitchell",
                duration: 1800, // 30 minutes
                contentType: .story,
                category: .fairyTales,
                audioUrl: "sleep_story_enchanted_forest.mp3",
                imageUrl: "sleep_enchanted_forest.jpg",
                thumbnailUrl: "sleep_enchanted_forest_thumb.jpg",
                isPremium: false,
                averageRating: 4.8,
                tags: ["forest", "magic", "peaceful", "nature"],
                metadata: ["narrator_voice": "soft", "background_music": "gentle"]
            ),
            
            SleepContentData(
                id: "ocean_voyage",
                title: "Peaceful Ocean Voyage",
                description: "Drift away on a gentle boat ride across calm seas under a starlit sky",
                narrator: "David Chen",
                duration: 2100, // 35 minutes
                contentType: .story,
                category: .adventure,
                audioUrl: "sleep_story_ocean_voyage.mp3",
                imageUrl: "sleep_ocean_voyage.jpg",
                thumbnailUrl: "sleep_ocean_voyage_thumb.jpg",
                isPremium: true,
                averageRating: 4.9,
                tags: ["ocean", "boat", "stars", "calm"],
                metadata: ["narrator_voice": "deep", "background_music": "ocean_waves"]
            ),
            
            SleepContentData(
                id: "mountain_cabin",
                title: "Cozy Mountain Cabin",
                description: "Find comfort in a warm cabin nestled in snow-covered mountains",
                narrator: "Emma Thompson",
                duration: 1500, // 25 minutes
                contentType: .story,
                category: .nature,
                audioUrl: "sleep_story_mountain_cabin.mp3",
                imageUrl: "sleep_mountain_cabin.jpg",
                thumbnailUrl: "sleep_mountain_cabin_thumb.jpg",
                isPremium: false,
                averageRating: 4.7,
                tags: ["mountain", "cabin", "cozy", "winter"],
                metadata: ["narrator_voice": "warm", "background_music": "fireplace"]
            ),
            
            // Nature Soundscapes
            SleepContentData(
                id: "rainforest_night",
                title: "Rainforest Night",
                description: "Immerse yourself in the gentle sounds of a tropical rainforest at night",
                narrator: nil,
                duration: 3600, // 60 minutes
                contentType: .natureSound,
                category: .forest,
                audioUrl: "soundscape_rainforest_night.mp3",
                imageUrl: "soundscape_rainforest.jpg",
                thumbnailUrl: "soundscape_rainforest_thumb.jpg",
                isPremium: false,
                averageRating: 4.6,
                tags: ["rainforest", "night", "insects", "nature"],
                metadata: ["loop": "seamless", "volume_fade": "gradual"]
            ),
            
            SleepContentData(
                id: "ocean_waves",
                title: "Gentle Ocean Waves",
                description: "Rhythmic waves lapping against a peaceful shore",
                narrator: nil,
                duration: 3600, // 60 minutes
                contentType: .natureSound,
                category: .ocean,
                audioUrl: "soundscape_ocean_waves.mp3",
                imageUrl: "soundscape_ocean.jpg",
                thumbnailUrl: "soundscape_ocean_thumb.jpg",
                isPremium: false,
                averageRating: 4.8,
                tags: ["ocean", "waves", "beach", "rhythmic"],
                metadata: ["loop": "seamless", "wave_intensity": "gentle"]
            ),
            
            SleepContentData(
                id: "summer_rain",
                title: "Summer Rain",
                description: "Soft rain falling on leaves with distant thunder",
                narrator: nil,
                duration: 3600, // 60 minutes
                contentType: .natureSound,
                category: .rain,
                audioUrl: "soundscape_summer_rain.mp3",
                imageUrl: "soundscape_rain.jpg",
                thumbnailUrl: "soundscape_rain_thumb.jpg",
                isPremium: false,
                averageRating: 4.7,
                tags: ["rain", "thunder", "summer", "leaves"],
                metadata: ["loop": "seamless", "thunder_frequency": "occasional"]
            ),
            
            // White Noise & Ambient
            SleepContentData(
                id: "brown_noise",
                title: "Brown Noise",
                description: "Deep, rich brown noise for masking distractions and promoting deep sleep",
                narrator: nil,
                duration: 3600, // 60 minutes
                contentType: .whiteNoise,
                category: .ambient,
                audioUrl: "white_noise_brown.mp3",
                imageUrl: "white_noise_brown.jpg",
                thumbnailUrl: "white_noise_brown_thumb.jpg",
                isPremium: false,
                averageRating: 4.5,
                tags: ["brown_noise", "masking", "deep", "consistent"],
                metadata: ["frequency_range": "low", "consistency": "perfect"]
            ),
            
            SleepContentData(
                id: "pink_noise",
                title: "Pink Noise",
                description: "Balanced pink noise that promotes natural sleep patterns",
                narrator: nil,
                duration: 3600, // 60 minutes
                contentType: .whiteNoise,
                category: .ambient,
                audioUrl: "white_noise_pink.mp3",
                imageUrl: "white_noise_pink.jpg",
                thumbnailUrl: "white_noise_pink_thumb.jpg",
                isPremium: true,
                averageRating: 4.6,
                tags: ["pink_noise", "balanced", "natural", "sleep_patterns"],
                metadata: ["frequency_range": "balanced", "sleep_enhancement": "proven"]
            ),
            
            // Sleep Music
            SleepContentData(
                id: "piano_lullabies",
                title: "Gentle Piano Lullabies",
                description: "Soft piano melodies designed to ease you into peaceful sleep",
                narrator: nil,
                duration: 2400, // 40 minutes
                contentType: .music,
                category: .classical,
                audioUrl: "sleep_music_piano_lullabies.mp3",
                imageUrl: "sleep_music_piano.jpg",
                thumbnailUrl: "sleep_music_piano_thumb.jpg",
                isPremium: true,
                averageRating: 4.9,
                tags: ["piano", "lullabies", "gentle", "melodies"],
                metadata: ["tempo": "slow", "key": "major", "instrument": "piano"]
            ),
            
            SleepContentData(
                id: "ambient_drones",
                title: "Ambient Drones",
                description: "Ethereal ambient soundscapes that create a cocoon of tranquility",
                narrator: nil,
                duration: 3000, // 50 minutes
                contentType: .music,
                category: .ambient,
                audioUrl: "sleep_music_ambient_drones.mp3",
                imageUrl: "sleep_music_ambient.jpg",
                thumbnailUrl: "sleep_music_ambient_thumb.jpg",
                isPremium: true,
                averageRating: 4.4,
                tags: ["ambient", "drones", "ethereal", "tranquil"],
                metadata: ["style": "ambient", "mood": "tranquil", "progression": "minimal"]
            ),
            
            // Sleep Meditations
            SleepContentData(
                id: "body_scan_sleep",
                title: "Body Scan for Sleep",
                description: "A guided body scan meditation to release tension and prepare for sleep",
                narrator: "Dr. Lisa Park",
                duration: 1200, // 20 minutes
                contentType: .meditation,
                category: .bedtimeStories,
                audioUrl: "sleep_meditation_body_scan.mp3",
                imageUrl: "sleep_meditation_body_scan.jpg",
                thumbnailUrl: "sleep_meditation_body_scan_thumb.jpg",
                isPremium: false,
                averageRating: 4.8,
                tags: ["body_scan", "tension_release", "guided", "preparation"],
                metadata: ["meditation_style": "body_scan", "guidance_frequency": "regular"]
            ),
            
            SleepContentData(
                id: "gratitude_sleep",
                title: "Gratitude & Sleep",
                description: "Reflect on the day with gratitude while drifting into peaceful sleep",
                narrator: "Michael Rodriguez",
                duration: 900, // 15 minutes
                contentType: .meditation,
                category: .bedtimeStories,
                audioUrl: "sleep_meditation_gratitude.mp3",
                imageUrl: "sleep_meditation_gratitude.jpg",
                thumbnailUrl: "sleep_meditation_gratitude_thumb.jpg",
                isPremium: true,
                averageRating: 4.7,
                tags: ["gratitude", "reflection", "peaceful", "day_review"],
                metadata: ["meditation_style": "gratitude", "emotional_tone": "positive"]
            ),
            
            // Binaural Beats
            SleepContentData(
                id: "delta_waves",
                title: "Delta Wave Sleep",
                description: "Binaural beats tuned to delta frequencies for deep sleep induction",
                narrator: nil,
                duration: 3600, // 60 minutes
                contentType: .binaural,
                category: .ambient,
                audioUrl: "binaural_delta_waves.mp3",
                imageUrl: "binaural_delta.jpg",
                thumbnailUrl: "binaural_delta_thumb.jpg",
                isPremium: true,
                averageRating: 4.3,
                tags: ["delta_waves", "binaural", "deep_sleep", "brainwave"],
                metadata: ["frequency": "0.5-4Hz", "carrier_frequency": "200Hz", "beat_frequency": "2Hz"]
            ),
            
            SleepContentData(
                id: "theta_relaxation",
                title: "Theta Relaxation",
                description: "Theta frequency binaural beats for deep relaxation and REM sleep",
                narrator: nil,
                duration: 2700, // 45 minutes
                contentType: .binaural,
                category: .ambient,
                audioUrl: "binaural_theta_relaxation.mp3",
                imageUrl: "binaural_theta.jpg",
                thumbnailUrl: "binaural_theta_thumb.jpg",
                isPremium: true,
                averageRating: 4.4,
                tags: ["theta_waves", "relaxation", "REM", "brainwave"],
                metadata: ["frequency": "4-8Hz", "carrier_frequency": "250Hz", "beat_frequency": "6Hz"]
            )
        ]
    }
}

// MARK: - Sleep Content Data Structure

private struct SleepContentData {
    let id: String
    let title: String
    let description: String
    let narrator: String?
    let duration: Int
    let contentType: SleepContentType
    let category: SleepCategory
    let audioUrl: String
    let imageUrl: String?
    let thumbnailUrl: String?
    let isPremium: Bool
    let averageRating: Float
    let tags: [String]
    let metadata: [String: Any]
    
    var tagsJSON: String {
        guard let data = try? JSONEncoder().encode(tags),
              let json = String(data: data, encoding: .utf8) else {
            return "[]"
        }
        return json
    }
    
    var metadataJSON: String {
        guard let data = try? JSONSerialization.data(withJSONObject: metadata),
              let json = String(data: data, encoding: .utf8) else {
            return "{}"
        }
        return json
    }
}
