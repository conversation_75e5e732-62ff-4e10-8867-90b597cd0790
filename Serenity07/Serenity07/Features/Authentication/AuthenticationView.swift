//
//  AuthenticationView.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI
import Combine
import LocalAuthentication

/// Main authentication view with sign in and sign up options
struct AuthenticationView: View {
    
    @StateObject private var authManager = AuthenticationManager.shared
    @State private var showSignUp = false
    @State private var showForgotPassword = false
    
    var body: some View {
        ZStack {
            Color.appBackground.ignoresSafeArea()
            
            VStack(spacing: Spacing.xl) {
                Spacer()
                
                // App branding
                VStack(spacing: Spacing.lg) {
                    // Logo
                    ZStack {
                        Circle()
                            .fill(Color.primaryTeal.opacity(0.2))
                            .frame(width: 120, height: 120)
                        
                        Image(systemName: "wind")
                            .font(.system(size: 48, weight: .light))
                            .foregroundColor(.primaryTeal)
                    }
                    
                    // App name
                    VStack(spacing: Spacing.xs) {
                        Text("静息")
                            .font(.system(size: 36, weight: .light, design: .serif))
                            .foregroundColor(.white)
                        
                        Text("Serenity")
                            .font(.system(size: 24, weight: .light))
                            .foregroundColor(.primaryTeal)
                            .opacity(0.8)
                    }
                    
                    Text("Your personal breathing coach")
                        .textStyle(.secondaryBody)
                        .multilineTextAlignment(.center)
                        .opacity(0.8)
                }
                
                Spacer()
                
                // Authentication options
                VStack(spacing: Spacing.md) {
                    if showSignUp {
                        SignUpView(onSignUpSuccess: {
                            showSignUp = false
                        }, onShowSignIn: {
                            showSignUp = false
                        })
                    } else {
                        SignInView(onSignInSuccess: {
                            // Handle successful sign in
                        }, onShowSignUp: {
                            showSignUp = true
                        }, onShowForgotPassword: {
                            showForgotPassword = true
                        })
                    }
                }
                .containerPadding()
                
                Spacer()
            }
        }
        .sheet(isPresented: $showForgotPassword) {
            ForgotPasswordView()
        }
    }
}

// MARK: - Sign In View

struct SignInView: View {
    
    @StateObject private var authManager = AuthenticationManager.shared
    @State private var email = ""
    @State private var password = ""
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var showPassword = false
    @State private var cancellables = Set<AnyCancellable>()
    
    let onSignInSuccess: () -> Void
    let onShowSignUp: () -> Void
    let onShowForgotPassword: () -> Void
    
    var body: some View {
        VStack(spacing: Spacing.lg) {
            // Title
            Text("Welcome Back")
                .textStyle(.primaryHeading)
            
            // Form fields
            VStack(spacing: Spacing.md) {
                SerenityTextField.email(
                    text: $email,
                    errorMessage: emailErrorMessage
                )
                
                SerenityTextField.password(
                    text: $password,
                    errorMessage: passwordErrorMessage
                )
            }
            
            // Error message
            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .textStyle(.caption)
                    .foregroundColor(.error)
                    .multilineTextAlignment(.center)
            }
            
            // Sign in button
            SerenityButton.primary(
                "Sign In",
                isEnabled: isFormValid && !isLoading,
                isLoading: isLoading
            ) {
                signIn()
            }
            
            // Biometric sign in
            if authManager.biometricAuthEnabled {
                SerenityButton.secondary(
                    biometricButtonTitle,
                    icon: biometricIcon,
                    isEnabled: !isLoading
                ) {
                    signInWithBiometric()
                }
            }
            
            // Forgot password
            Button("Forgot Password?") {
                onShowForgotPassword()
            }
            .textStyle(.caption)
            .foregroundColor(.primaryTeal)
            
            // Sign up option
            HStack {
                Text("Don't have an account?")
                    .textStyle(.caption)
                
                Button("Sign Up") {
                    onShowSignUp()
                }
                .textStyle(.caption)
                .foregroundColor(.primaryTeal)
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var isFormValid: Bool {
        !email.isEmpty && !password.isEmpty && email.contains("@")
    }
    
    private var emailErrorMessage: String? {
        if !email.isEmpty && !email.contains("@") {
            return "Please enter a valid email address"
        }
        return nil
    }
    
    private var passwordErrorMessage: String? {
        if !password.isEmpty && password.count < 6 {
            return "Password must be at least 6 characters"
        }
        return nil
    }
    
    private var biometricButtonTitle: String {
        switch authManager.getBiometricType() {
        case .faceID:
            return "Sign in with Face ID"
        case .touchID:
            return "Sign in with Touch ID"
        default:
            return "Sign in with Biometric"
        }
    }
    
    private var biometricIcon: String {
        switch authManager.getBiometricType() {
        case .faceID:
            return "faceid"
        case .touchID:
            return "touchid"
        default:
            return "person.badge.key"
        }
    }
    
    // MARK: - Methods
    
    private func signIn() {
        isLoading = true
        errorMessage = nil
        
        authManager.signIn(email: email, password: password)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    isLoading = false
                    if case .failure(let error) = completion {
                        errorMessage = error.localizedDescription
                    }
                },
                receiveValue: { user in
                    onSignInSuccess()
                }
            )
            .store(in: &cancellables)
    }
    
    private func signInWithBiometric() {
        isLoading = true
        errorMessage = nil
        
        authManager.signInWithBiometric()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    isLoading = false
                    if case .failure(let error) = completion {
                        errorMessage = error.localizedDescription
                    }
                },
                receiveValue: { user in
                    onSignInSuccess()
                }
            )
            .store(in: &cancellables)
    }
}

// MARK: - Sign Up View

struct SignUpView: View {
    
    @StateObject private var authManager = AuthenticationManager.shared
    @State private var name = ""
    @State private var email = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var cancellables = Set<AnyCancellable>()
    
    let onSignUpSuccess: () -> Void
    let onShowSignIn: () -> Void
    
    var body: some View {
        VStack(spacing: Spacing.lg) {
            // Title
            Text("Create Account")
                .textStyle(.primaryHeading)
            
            // Form fields
            VStack(spacing: Spacing.md) {
                SerenityTextField.standard(
                    title: "Name",
                    placeholder: "Enter your name",
                    text: $name,
                    errorMessage: nameErrorMessage
                )
                
                SerenityTextField.email(
                    text: $email,
                    errorMessage: emailErrorMessage
                )
                
                SerenityTextField.password(
                    text: $password,
                    errorMessage: passwordErrorMessage
                )
                
                SerenityTextField.password(
                    title: "Confirm Password",
                    placeholder: "Confirm your password",
                    text: $confirmPassword,
                    errorMessage: confirmPasswordErrorMessage
                )
            }
            
            // Error message
            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .textStyle(.caption)
                    .foregroundColor(.error)
                    .multilineTextAlignment(.center)
            }
            
            // Sign up button
            SerenityButton.primary(
                "Create Account",
                isEnabled: isFormValid && !isLoading,
                isLoading: isLoading
            ) {
                signUp()
            }
            
            // Sign in option
            HStack {
                Text("Already have an account?")
                    .textStyle(.caption)
                
                Button("Sign In") {
                    onShowSignIn()
                }
                .textStyle(.caption)
                .foregroundColor(.primaryTeal)
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var isFormValid: Bool {
        !name.isEmpty && 
        !email.isEmpty && 
        !password.isEmpty && 
        !confirmPassword.isEmpty &&
        email.contains("@") && 
        password.count >= 6 && 
        password == confirmPassword
    }
    
    private var nameErrorMessage: String? {
        if !name.isEmpty && name.count < 2 {
            return "Name must be at least 2 characters"
        }
        return nil
    }
    
    private var emailErrorMessage: String? {
        if !email.isEmpty && !email.contains("@") {
            return "Please enter a valid email address"
        }
        return nil
    }
    
    private var passwordErrorMessage: String? {
        if !password.isEmpty && password.count < 6 {
            return "Password must be at least 6 characters"
        }
        return nil
    }
    
    private var confirmPasswordErrorMessage: String? {
        if !confirmPassword.isEmpty && password != confirmPassword {
            return "Passwords do not match"
        }
        return nil
    }
    
    // MARK: - Methods
    
    private func signUp() {
        isLoading = true
        errorMessage = nil
        
        authManager.signUp(name: name, email: email, password: password)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    isLoading = false
                    if case .failure(let error) = completion {
                        errorMessage = error.localizedDescription
                    }
                },
                receiveValue: { user in
                    onSignUpSuccess()
                }
            )
            .store(in: &cancellables)
    }
}

// MARK: - Forgot Password View

struct ForgotPasswordView: View {
    
    @Environment(\.dismiss) private var dismiss
    @State private var email = ""
    @State private var isLoading = false
    @State private var showSuccess = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: Spacing.lg) {
                if showSuccess {
                    // Success state
                    VStack(spacing: Spacing.md) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.success)
                        
                        Text("Check Your Email")
                            .textStyle(.primaryHeading)
                        
                        Text("We've sent password reset instructions to \(email)")
                            .textStyle(.secondaryBody)
                            .multilineTextAlignment(.center)
                    }
                    
                    Spacer()
                    
                    SerenityButton.primary("Done") {
                        dismiss()
                    }
                } else {
                    // Form state
                    VStack(spacing: Spacing.lg) {
                        Text("Reset Password")
                            .textStyle(.primaryHeading)
                        
                        Text("Enter your email address and we'll send you instructions to reset your password.")
                            .textStyle(.secondaryBody)
                            .multilineTextAlignment(.center)
                        
                        SerenityTextField.email(
                            text: $email
                        )
                        
                        SerenityButton.primary(
                            "Send Reset Instructions",
                            isEnabled: !email.isEmpty && email.contains("@") && !isLoading,
                            isLoading: isLoading
                        ) {
                            sendResetInstructions()
                        }
                    }
                    
                    Spacer()
                }
            }
            .containerPadding()
            .background(Color.appBackground)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private func sendResetInstructions() {
        isLoading = true
        
        // Simulate API call
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            isLoading = false
            showSuccess = true
        }
    }
}

// MARK: - Preview

#Preview {
    AuthenticationView()
}
