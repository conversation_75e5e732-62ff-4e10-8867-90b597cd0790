//
//  AuthenticationManager.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import Combine
import LocalAuthentication

/// Authentication manager for handling user authentication and session management
class AuthenticationManager: ObservableObject {
    
    static let shared = AuthenticationManager()
    
    // MARK: - Published Properties
    
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var authenticationState: AuthenticationState = .unauthenticated
    @Published var biometricAuthEnabled = false
    @Published var lastAuthenticationDate: Date?
    
    // MARK: - Private Properties
    
    private let coreDataManager = CoreDataManager.shared
    private let keychain = KeychainManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Authentication State
    
    enum AuthenticationState {
        case unauthenticated
        case authenticating
        case authenticated
        case biometricRequired
        case sessionExpired
        case error(AuthenticationError)
    }
    
    enum AuthenticationError: LocalizedError {
        case invalidCredentials
        case userNotFound
        case networkError
        case biometricNotAvailable
        case biometricFailed
        case sessionExpired
        case unknown(Error)
        
        var errorDescription: String? {
            switch self {
            case .invalidCredentials:
                return NSLocalizedString("Invalid email or password", comment: "Invalid credentials error")
            case .userNotFound:
                return NSLocalizedString("User not found", comment: "User not found error")
            case .networkError:
                return NSLocalizedString("Network connection error", comment: "Network error")
            case .biometricNotAvailable:
                return NSLocalizedString("Biometric authentication not available", comment: "Biometric not available error")
            case .biometricFailed:
                return NSLocalizedString("Biometric authentication failed", comment: "Biometric failed error")
            case .sessionExpired:
                return NSLocalizedString("Session expired. Please log in again", comment: "Session expired error")
            case .unknown(let error):
                return error.localizedDescription
            }
        }
    }
    
    // MARK: - Initialization
    
    private init() {
        checkAuthenticationState()
        setupBiometricAuth()
    }
    
    // MARK: - Authentication Methods
    
    /// Sign in with email and password
    func signIn(email: String, password: String) -> AnyPublisher<User, AuthenticationError> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(.unknown(NSError(domain: "AuthenticationManager", code: -1))))
                return
            }
            
            self.authenticationState = .authenticating
            
            // Simulate network request delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                // For now, we'll use a simple validation
                // In a real app, this would make an API call
                if self.validateCredentials(email: email, password: password) {
                    let user = self.coreDataManager.getOrCreateDefaultUser()
                    user.email = email
                    user.lastLoginDate = Date()
                    self.coreDataManager.save()
                    
                    self.completeAuthentication(user: user)
                    promise(.success(user))
                } else {
                    self.authenticationState = .error(.invalidCredentials)
                    promise(.failure(.invalidCredentials))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// Sign up with email, password, and name
    func signUp(name: String, email: String, password: String) -> AnyPublisher<User, AuthenticationError> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(.unknown(NSError(domain: "AuthenticationManager", code: -1))))
                return
            }
            
            self.authenticationState = .authenticating
            
            // Simulate network request delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                // Check if user already exists
                if self.userExists(email: email) {
                    self.authenticationState = .error(.invalidCredentials)
                    promise(.failure(.invalidCredentials))
                    return
                }
                
                // Create new user
                let user = User.create(
                    in: self.coreDataManager.viewContext,
                    userId: UUID().uuidString,
                    name: name,
                    email: email
                )
                
                self.coreDataManager.save()
                
                // Store credentials securely
                self.keychain.store(password: password, for: email)
                
                self.completeAuthentication(user: user)
                promise(.success(user))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// Sign in with biometric authentication
    func signInWithBiometric() -> AnyPublisher<User, AuthenticationError> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(.unknown(NSError(domain: "AuthenticationManager", code: -1))))
                return
            }
            
            let context = LAContext()
            var error: NSError?
            
            guard context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) else {
                promise(.failure(.biometricNotAvailable))
                return
            }
            
            let reason = NSLocalizedString("Authenticate to access Serenity", comment: "Biometric authentication reason")
            
            context.evaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, localizedReason: reason) { success, error in
                DispatchQueue.main.async {
                    if success {
                        // Get stored user
                        if let user = self.coreDataManager.getCurrentUser() {
                            self.completeAuthentication(user: user)
                            promise(.success(user))
                        } else {
                            promise(.failure(.userNotFound))
                        }
                    } else {
                        promise(.failure(.biometricFailed))
                    }
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// Sign out current user
    func signOut() {
        currentUser = nil
        isAuthenticated = false
        authenticationState = .unauthenticated
        lastAuthenticationDate = nil
        
        // Clear sensitive data
        keychain.clearAll()
        
        // Clear user session data
        UserDefaults.standard.removeObject(forKey: "last_authentication_date")
    }
    
    /// Check if session is still valid
    func validateSession() -> Bool {
        guard let lastAuth = lastAuthenticationDate else { return false }
        
        let sessionTimeout: TimeInterval = 24 * 60 * 60 // 24 hours
        let timeSinceAuth = Date().timeIntervalSince(lastAuth)
        
        if timeSinceAuth > sessionTimeout {
            authenticationState = .sessionExpired
            return false
        }
        
        return true
    }
    
    /// Refresh authentication session
    func refreshSession() {
        lastAuthenticationDate = Date()
        UserDefaults.standard.set(lastAuthenticationDate, forKey: "last_authentication_date")
    }
    
    // MARK: - Biometric Authentication
    
    private func setupBiometricAuth() {
        let context = LAContext()
        biometricAuthEnabled = context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: nil)
    }
    
    func getBiometricType() -> LABiometryType {
        let context = LAContext()
        _ = context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: nil)
        return context.biometryType
    }
    
    // MARK: - Private Methods
    
    private func checkAuthenticationState() {
        // Check if user was previously authenticated
        if let lastAuth = UserDefaults.standard.object(forKey: "last_authentication_date") as? Date {
            lastAuthenticationDate = lastAuth
            
            if validateSession() {
                if let user = coreDataManager.getCurrentUser() {
                    completeAuthentication(user: user)
                }
            }
        }
    }
    
    private func completeAuthentication(user: User) {
        currentUser = user
        isAuthenticated = true
        authenticationState = .authenticated
        lastAuthenticationDate = Date()
        
        // Store authentication date
        UserDefaults.standard.set(lastAuthenticationDate, forKey: "last_authentication_date")
        
        // Update user's last login
        user.lastLoginDate = Date()
        coreDataManager.save()
    }
    
    private func validateCredentials(email: String, password: String) -> Bool {
        // In a real app, this would validate against a server
        // For demo purposes, we'll use simple validation
        
        // Check if stored password matches
        if let storedPassword = keychain.getPassword(for: email) {
            return storedPassword == password
        }
        
        // For demo, allow any email with password "password"
        return password == "password" && email.contains("@")
    }
    
    private func userExists(email: String) -> Bool {
        // Check if user exists in Core Data
        if let user = coreDataManager.getCurrentUser() {
            return user.email == email
        }
        return false
    }
}

// MARK: - Keychain Manager

class KeychainManager {
    
    static let shared = KeychainManager()
    
    private let service = "com.serenity.app"
    
    private init() {}
    
    func store(password: String, for email: String) {
        let data = password.data(using: .utf8)!
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: email,
            kSecValueData as String: data
        ]
        
        // Delete existing item
        SecItemDelete(query as CFDictionary)
        
        // Add new item
        SecItemAdd(query as CFDictionary, nil)
    }
    
    func getPassword(for email: String) -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: email,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess,
              let data = result as? Data,
              let password = String(data: data, encoding: .utf8) else {
            return nil
        }
        
        return password
    }
    
    func deletePassword(for email: String) {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: email
        ]
        
        SecItemDelete(query as CFDictionary)
    }
    
    func clearAll() {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service
        ]
        
        SecItemDelete(query as CFDictionary)
    }
}
