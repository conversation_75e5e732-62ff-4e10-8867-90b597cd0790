//
//  SettingsSheet.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI
import UserNotifications

struct SettingsSheet: View {
    
    // MARK: - Properties
    
    @Environment(\.dismiss) private var dismiss
    @StateObject private var settingsManager = SettingsManager()
    @State private var showingLanguageSelection = false
    @State private var showingNotificationPermission = false
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            List {
                // Notifications Section
                notificationsSection
                
                // Audio & Haptics Section
                audioHapticsSection
                
                // Appearance Section
                appearanceSection
                
                // Language & Region Section
                languageSection
                
                // Privacy & Data Section
                privacySection
                
                // About Section
                aboutSection
            }
            .listStyle(InsetGroupedListStyle())
            .background(Color.appBackground)
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("Done") {
                        dismiss()
                    }
                    .foregroundColor(.primaryTeal)
                }
            }
        }
        .preferredColorScheme(.dark)
        .onAppear {
            settingsManager.loadSettings()
        }
        .sheet(isPresented: $showingLanguageSelection) {
            LanguageSelectionSheet(settingsManager: settingsManager)
        }
        .alert("Notification Permission", isPresented: $showingNotificationPermission) {
            Button("Settings") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("To receive practice reminders, please enable notifications in Settings.")
        }
    }
    
    // MARK: - Notifications Section
    
    private var notificationsSection: some View {
        Section("Notifications") {
            SettingsToggleRow(
                title: "Practice Reminders",
                subtitle: "Daily reminders to practice",
                isOn: $settingsManager.practiceRemindersEnabled
            ) {
                settingsManager.togglePracticeReminders()
                checkNotificationPermission()
            }
            
            if settingsManager.practiceRemindersEnabled {
                SettingsTimeRow(
                    title: "Reminder Time",
                    time: $settingsManager.reminderTime
                ) {
                    settingsManager.updateReminderTime()
                }
            }
            
            SettingsToggleRow(
                title: "Achievement Notifications",
                subtitle: "Get notified when you unlock achievements",
                isOn: $settingsManager.achievementNotificationsEnabled
            ) {
                settingsManager.toggleAchievementNotifications()
            }
            
            SettingsToggleRow(
                title: "Streak Reminders",
                subtitle: "Reminders to maintain your streak",
                isOn: $settingsManager.streakRemindersEnabled
            ) {
                settingsManager.toggleStreakReminders()
            }
        }
    }
    
    // MARK: - Audio & Haptics Section
    
    private var audioHapticsSection: some View {
        Section("Audio & Haptics") {
            SettingsSliderRow(
                title: "Background Sound Volume",
                value: $settingsManager.backgroundSoundVolume,
                range: 0...1
            ) {
                settingsManager.updateBackgroundSoundVolume()
            }
            
            SettingsSliderRow(
                title: "Voice Guidance Volume",
                value: $settingsManager.voiceGuidanceVolume,
                range: 0...1
            ) {
                settingsManager.updateVoiceGuidanceVolume()
            }
            
            SettingsToggleRow(
                title: "Haptic Feedback",
                subtitle: "Vibration for session events",
                isOn: $settingsManager.hapticFeedbackEnabled
            ) {
                settingsManager.toggleHapticFeedback()
            }
            
            SettingsToggleRow(
                title: "Auto-play Background Sounds",
                subtitle: "Continue playing sounds between sessions",
                isOn: $settingsManager.autoPlayBackgroundSounds
            ) {
                settingsManager.toggleAutoPlayBackgroundSounds()
            }
        }
    }
    
    // MARK: - Appearance Section
    
    private var appearanceSection: some View {
        Section("Appearance") {
            SettingsPickerRow(
                title: "Theme",
                selection: $settingsManager.selectedTheme,
                options: AppTheme.allCases
            ) {
                settingsManager.updateTheme()
            }
            
            SettingsToggleRow(
                title: "Reduce Motion",
                subtitle: "Minimize animations and effects",
                isOn: $settingsManager.reduceMotionEnabled
            ) {
                settingsManager.toggleReduceMotion()
            }
            
            SettingsToggleRow(
                title: "Large Text",
                subtitle: "Use larger text throughout the app",
                isOn: $settingsManager.largeTextEnabled
            ) {
                settingsManager.toggleLargeText()
            }
        }
    }
    
    // MARK: - Language Section
    
    private var languageSection: some View {
        Section("Language & Region") {
            Button(action: {
                showingLanguageSelection = true
            }) {
                HStack {
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Language")
                            .foregroundColor(.textPrimary)
                        
                        Text(settingsManager.selectedLanguage.displayName)
                            .font(.caption)
                            .foregroundColor(.textSecondary)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                }
            }
            .buttonStyle(PlainButtonStyle())
            
            SettingsToggleRow(
                title: "Use 24-Hour Time",
                subtitle: "Display time in 24-hour format",
                isOn: $settingsManager.use24HourTime
            ) {
                settingsManager.toggleTimeFormat()
            }
        }
    }
    
    // MARK: - Privacy Section
    
    private var privacySection: some View {
        Section("Privacy & Data") {
            SettingsToggleRow(
                title: "Analytics",
                subtitle: "Help improve the app with usage data",
                isOn: $settingsManager.analyticsEnabled
            ) {
                settingsManager.toggleAnalytics()
            }
            
            SettingsToggleRow(
                title: "Crash Reports",
                subtitle: "Automatically send crash reports",
                isOn: $settingsManager.crashReportsEnabled
            ) {
                settingsManager.toggleCrashReports()
            }
            
            Button("Clear Cache") {
                settingsManager.clearCache()
            }
            .foregroundColor(.primaryTeal)
            
            Button("Reset All Settings") {
                settingsManager.resetAllSettings()
            }
            .foregroundColor(.error)
        }
    }
    
    // MARK: - About Section
    
    private var aboutSection: some View {
        Section("About") {
            SettingsInfoRow(title: "Version", value: settingsManager.appVersion)
            SettingsInfoRow(title: "Build", value: settingsManager.buildNumber)
            
            Button("Privacy Policy") {
                settingsManager.openPrivacyPolicy()
            }
            .foregroundColor(.primaryTeal)
            
            Button("Terms of Service") {
                settingsManager.openTermsOfService()
            }
            .foregroundColor(.primaryTeal)
            
            Button("Contact Support") {
                settingsManager.contactSupport()
            }
            .foregroundColor(.primaryTeal)
        }
    }
    
    // MARK: - Helper Methods
    
    private func checkNotificationPermission() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                if settings.authorizationStatus == .denied {
                    showingNotificationPermission = true
                }
            }
        }
    }
}

// MARK: - Settings Row Components

struct SettingsToggleRow: View {
    let title: String
    let subtitle: String
    @Binding var isOn: Bool
    let action: () -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .foregroundColor(.textPrimary)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .onChange(of: isOn) { oldValue, newValue in
                    action()
                }
        }
    }
}

struct SettingsSliderRow: View {
    let title: String
    @Binding var value: Float
    let range: ClosedRange<Float>
    let action: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .foregroundColor(.textPrimary)
                
                Spacer()
                
                Text("\(Int(value * 100))%")
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }
            
            Slider(value: $value, in: range) { _ in
                action()
            }
            .accentColor(.primaryTeal)
        }
    }
}

struct SettingsTimeRow: View {
    let title: String
    @Binding var time: Date
    let action: () -> Void
    
    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.textPrimary)
            
            Spacer()
            
            DatePicker("", selection: $time, displayedComponents: .hourAndMinute)
                .onChange(of: time) { oldValue, newValue in
                    action()
                }
        }
    }
}

struct SettingsPickerRow<T: CaseIterable & Hashable & RawRepresentable>: View where T.RawValue == String {
    let title: String
    @Binding var selection: T
    let options: [T]
    let action: () -> Void
    
    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.textPrimary)
            
            Spacer()
            
            Picker(title, selection: $selection) {
                ForEach(options, id: \.self) { option in
                    Text(option.rawValue.capitalized)
                        .tag(option)
                }
            }
            .pickerStyle(MenuPickerStyle())
            .onChange(of: selection) { oldValue, newValue in
                action()
            }
        }
    }
}

struct SettingsInfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.textPrimary)
            
            Spacer()
            
            Text(value)
                .foregroundColor(.textSecondary)
        }
    }
}

// MARK: - App Theme

enum AppTheme: String, CaseIterable {
    case system = "system"
    case light = "light"
    case dark = "dark"
    
    var displayName: String {
        switch self {
        case .system:
            return "System"
        case .light:
            return "Light"
        case .dark:
            return "Dark"
        }
    }
}

// MARK: - Settings Manager

@MainActor
class SettingsManager: ObservableObject {

    // MARK: - Published Properties

    @Published var practiceRemindersEnabled: Bool = false
    @Published var reminderTime: Date = Calendar.current.date(from: DateComponents(hour: 19, minute: 0)) ?? Date()
    @Published var achievementNotificationsEnabled: Bool = true
    @Published var streakRemindersEnabled: Bool = true
    @Published var backgroundSoundVolume: Float = 0.7
    @Published var voiceGuidanceVolume: Float = 0.8
    @Published var hapticFeedbackEnabled: Bool = true
    @Published var autoPlayBackgroundSounds: Bool = false
    @Published var selectedTheme: AppTheme = .dark
    @Published var reduceMotionEnabled: Bool = false
    @Published var largeTextEnabled: Bool = false
    @Published var selectedLanguage: AppLanguage = .english
    @Published var use24HourTime: Bool = false
    @Published var analyticsEnabled: Bool = true
    @Published var crashReportsEnabled: Bool = true

    // MARK: - Computed Properties

    var appVersion: String {
        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
    }

    var buildNumber: String {
        Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
    }

    // MARK: - Private Properties

    private let userDefaults = UserDefaults.standard

    // MARK: - Settings Keys

    private enum SettingsKey: String {
        case practiceRemindersEnabled
        case reminderTime
        case achievementNotificationsEnabled
        case streakRemindersEnabled
        case backgroundSoundVolume
        case voiceGuidanceVolume
        case hapticFeedbackEnabled
        case autoPlayBackgroundSounds
        case selectedTheme
        case reduceMotionEnabled
        case largeTextEnabled
        case selectedLanguage
        case use24HourTime
        case analyticsEnabled
        case crashReportsEnabled
    }

    // MARK: - Public Methods

    func loadSettings() {
        practiceRemindersEnabled = userDefaults.bool(forKey: SettingsKey.practiceRemindersEnabled.rawValue)

        if let reminderTimeData = userDefaults.data(forKey: SettingsKey.reminderTime.rawValue),
           let time = try? JSONDecoder().decode(Date.self, from: reminderTimeData) {
            reminderTime = time
        }

        achievementNotificationsEnabled = userDefaults.object(forKey: SettingsKey.achievementNotificationsEnabled.rawValue) as? Bool ?? true
        streakRemindersEnabled = userDefaults.object(forKey: SettingsKey.streakRemindersEnabled.rawValue) as? Bool ?? true
        backgroundSoundVolume = userDefaults.object(forKey: SettingsKey.backgroundSoundVolume.rawValue) as? Float ?? 0.7
        voiceGuidanceVolume = userDefaults.object(forKey: SettingsKey.voiceGuidanceVolume.rawValue) as? Float ?? 0.8
        hapticFeedbackEnabled = userDefaults.object(forKey: SettingsKey.hapticFeedbackEnabled.rawValue) as? Bool ?? true
        autoPlayBackgroundSounds = userDefaults.bool(forKey: SettingsKey.autoPlayBackgroundSounds.rawValue)

        if let themeString = userDefaults.string(forKey: SettingsKey.selectedTheme.rawValue),
           let theme = AppTheme(rawValue: themeString) {
            selectedTheme = theme
        }

        reduceMotionEnabled = userDefaults.bool(forKey: SettingsKey.reduceMotionEnabled.rawValue)
        largeTextEnabled = userDefaults.bool(forKey: SettingsKey.largeTextEnabled.rawValue)

        if let languageString = userDefaults.string(forKey: SettingsKey.selectedLanguage.rawValue),
           let language = AppLanguage(rawValue: languageString) {
            selectedLanguage = language
        }

        use24HourTime = userDefaults.bool(forKey: SettingsKey.use24HourTime.rawValue)
        analyticsEnabled = userDefaults.object(forKey: SettingsKey.analyticsEnabled.rawValue) as? Bool ?? true
        crashReportsEnabled = userDefaults.object(forKey: SettingsKey.crashReportsEnabled.rawValue) as? Bool ?? true
    }

    // MARK: - Notification Settings

    func togglePracticeReminders() {
        practiceRemindersEnabled.toggle()
        userDefaults.set(practiceRemindersEnabled, forKey: SettingsKey.practiceRemindersEnabled.rawValue)

        if practiceRemindersEnabled {
            scheduleReminderNotifications()
        } else {
            cancelReminderNotifications()
        }
    }

    func updateReminderTime() {
        if let timeData = try? JSONEncoder().encode(reminderTime) {
            userDefaults.set(timeData, forKey: SettingsKey.reminderTime.rawValue)
        }

        if practiceRemindersEnabled {
            scheduleReminderNotifications()
        }
    }

    func toggleAchievementNotifications() {
        achievementNotificationsEnabled.toggle()
        userDefaults.set(achievementNotificationsEnabled, forKey: SettingsKey.achievementNotificationsEnabled.rawValue)
    }

    func toggleStreakReminders() {
        streakRemindersEnabled.toggle()
        userDefaults.set(streakRemindersEnabled, forKey: SettingsKey.streakRemindersEnabled.rawValue)
    }

    // MARK: - Audio Settings

    func updateBackgroundSoundVolume() {
        userDefaults.set(backgroundSoundVolume, forKey: SettingsKey.backgroundSoundVolume.rawValue)
    }

    func updateVoiceGuidanceVolume() {
        userDefaults.set(voiceGuidanceVolume, forKey: SettingsKey.voiceGuidanceVolume.rawValue)
    }

    func toggleHapticFeedback() {
        hapticFeedbackEnabled.toggle()
        userDefaults.set(hapticFeedbackEnabled, forKey: SettingsKey.hapticFeedbackEnabled.rawValue)
    }

    func toggleAutoPlayBackgroundSounds() {
        autoPlayBackgroundSounds.toggle()
        userDefaults.set(autoPlayBackgroundSounds, forKey: SettingsKey.autoPlayBackgroundSounds.rawValue)
    }

    // MARK: - Appearance Settings

    func updateTheme() {
        userDefaults.set(selectedTheme.rawValue, forKey: SettingsKey.selectedTheme.rawValue)
    }

    func toggleReduceMotion() {
        reduceMotionEnabled.toggle()
        userDefaults.set(reduceMotionEnabled, forKey: SettingsKey.reduceMotionEnabled.rawValue)
    }

    func toggleLargeText() {
        largeTextEnabled.toggle()
        userDefaults.set(largeTextEnabled, forKey: SettingsKey.largeTextEnabled.rawValue)
    }

    // MARK: - Language Settings

    func updateLanguage(_ language: AppLanguage) {
        selectedLanguage = language
        userDefaults.set(language.rawValue, forKey: SettingsKey.selectedLanguage.rawValue)

        // Update app language
        UserDefaults.standard.set([language.code], forKey: "AppleLanguages")
        UserDefaults.standard.synchronize()
    }

    func toggleTimeFormat() {
        use24HourTime.toggle()
        userDefaults.set(use24HourTime, forKey: SettingsKey.use24HourTime.rawValue)
    }

    // MARK: - Privacy Settings

    func toggleAnalytics() {
        analyticsEnabled.toggle()
        userDefaults.set(analyticsEnabled, forKey: SettingsKey.analyticsEnabled.rawValue)
    }

    func toggleCrashReports() {
        crashReportsEnabled.toggle()
        userDefaults.set(crashReportsEnabled, forKey: SettingsKey.crashReportsEnabled.rawValue)
    }

    func clearCache() {
        // Clear image cache, temporary files, etc.
        let cacheURL = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first!
        try? FileManager.default.removeItem(at: cacheURL)
    }

    func resetAllSettings() {
        // Reset to defaults
        let keys = [
            SettingsKey.practiceRemindersEnabled.rawValue,
            SettingsKey.achievementNotificationsEnabled.rawValue,
            SettingsKey.streakRemindersEnabled.rawValue,
            SettingsKey.backgroundSoundVolume.rawValue,
            SettingsKey.voiceGuidanceVolume.rawValue,
            SettingsKey.hapticFeedbackEnabled.rawValue,
            SettingsKey.autoPlayBackgroundSounds.rawValue,
            SettingsKey.selectedTheme.rawValue,
            SettingsKey.reduceMotionEnabled.rawValue,
            SettingsKey.largeTextEnabled.rawValue,
            SettingsKey.use24HourTime.rawValue,
            SettingsKey.analyticsEnabled.rawValue,
            SettingsKey.crashReportsEnabled.rawValue
        ]

        keys.forEach { userDefaults.removeObject(forKey: $0) }
        loadSettings()
    }

    // MARK: - External Actions

    func openPrivacyPolicy() {
        if let url = URL(string: "https://serenity.app/privacy") {
            UIApplication.shared.open(url)
        }
    }

    func openTermsOfService() {
        if let url = URL(string: "https://serenity.app/terms") {
            UIApplication.shared.open(url)
        }
    }

    func contactSupport() {
        if let url = URL(string: "mailto:<EMAIL>") {
            UIApplication.shared.open(url)
        }
    }

    // MARK: - Notification Scheduling

    private func scheduleReminderNotifications() {
        cancelReminderNotifications()

        let center = UNUserNotificationCenter.current()

        // Request permission first
        center.requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            guard granted else { return }

            DispatchQueue.main.async {
                self.createReminderNotifications()
            }
        }
    }

    private func createReminderNotifications() {
        let center = UNUserNotificationCenter.current()
        let calendar = Calendar.current

        // Create daily reminder
        let content = UNMutableNotificationContent()
        content.title = "Time to Practice"
        content.body = "Take a moment for mindfulness and breathing exercises"
        content.sound = .default

        let components = calendar.dateComponents([.hour, .minute], from: reminderTime)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: true)

        let request = UNNotificationRequest(identifier: "daily_reminder", content: content, trigger: trigger)
        center.add(request)
    }

    private func cancelReminderNotifications() {
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: ["daily_reminder"])
    }
}

// MARK: - App Language

enum AppLanguage: String, CaseIterable {
    case english = "en"
    case chinese = "zh"
    case chineseTraditional = "zh-Hant"

    var displayName: String {
        switch self {
        case .english:
            return "English"
        case .chinese:
            return "中文 (简体)"
        case .chineseTraditional:
            return "中文 (繁體)"
        }
    }

    var code: String {
        return rawValue
    }
}

// MARK: - Preview

#Preview {
    SettingsSheet()
}
