//
//  ProfileScreen.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct ProfileScreen: View {
    
    // MARK: - Properties
    
    @EnvironmentObject private var authManager: AuthenticationManager
    @StateObject private var profileManager = ProfileManager()
    @State private var showingEditProfile = false
    @State private var showingSettings = false
    @State private var showingStatistics = false
    @State private var showingAchievements = false
    @State private var showingDataExport = false
    @State private var showingSignOutAlert = false
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Profile Header
                    profileHeaderView
                    
                    // Quick Stats
                    quickStatsView
                    
                    // Recent Achievements
                    recentAchievementsView
                    
                    // Profile Actions
                    profileActionsView
                    
                    // Settings & Account
                    settingsActionsView
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 100) // Tab bar padding
            }
            .background(Color.appBackground)
            .navigationTitle("Profile")
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                await profileManager.refreshData()
            }
        }
        .onAppear {
            profileManager.loadUserData(authManager.currentUser)
        }
        .sheet(isPresented: $showingEditProfile) {
            EditProfileSheet(profileManager: profileManager)
        }
        .sheet(isPresented: $showingSettings) {
            SettingsSheet()
        }
        .sheet(isPresented: $showingStatistics) {
            StatisticsSheet(profileManager: profileManager)
        }
        .sheet(isPresented: $showingAchievements) {
            AchievementsSheet(profileManager: profileManager)
        }
        .sheet(isPresented: $showingDataExport) {
            DataExportSheet(profileManager: profileManager)
        }
        .alert("Sign Out", isPresented: $showingSignOutAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Sign Out", role: .destructive) {
                authManager.signOut()
            }
        } message: {
            Text("Are you sure you want to sign out? Your data will be saved locally.")
        }
    }
    
    // MARK: - Profile Header
    
    private var profileHeaderView: some View {
        SerenityCard {
            VStack(spacing: 16) {
                // Avatar and basic info
                HStack(spacing: 16) {
                    // Avatar
                    Button(action: {
                        showingEditProfile = true
                    }) {
                        ZStack {
                            Circle()
                                .fill(LinearGradient(
                                    colors: [.primaryTeal, .primaryPurple],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ))
                                .frame(width: 80, height: 80)
                            
                            if let avatarImage = profileManager.avatarImage {
                                Image(uiImage: avatarImage)
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                                    .frame(width: 76, height: 76)
                                    .clipShape(Circle())
                            } else {
                                Text(profileManager.initials)
                                    .font(.title)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)
                            }
                            
                            // Edit indicator
                            Circle()
                                .fill(Color.primaryTeal)
                                .frame(width: 24, height: 24)
                                .overlay(
                                    Image(systemName: "pencil")
                                        .font(.caption)
                                        .foregroundColor(.white)
                                )
                                .offset(x: 28, y: 28)
                        }
                    }
                    
                    // User info
                    VStack(alignment: .leading, spacing: 4) {
                        Text(profileManager.displayName)
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.textPrimary)
                        
                        if let email = authManager.currentUser?.email {
                            Text(email)
                                .font(.subheadline)
                                .foregroundColor(.textSecondary)
                        }
                        
                        // Member since
                        if let joinDate = authManager.currentUser?.createdDate {
                            Text("Member since \(joinDate, formatter: DateFormatter.monthYear)")
                                .font(.caption)
                                .foregroundColor(.textSecondary)
                        }
                    }
                    
                    Spacer()
                }
                
                // Level and points
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Level \(profileManager.userLevel)")
                            .font(.headline)
                            .foregroundColor(.primaryTeal)
                        
                        Text("\(profileManager.totalPoints) points")
                            .font(.caption)
                            .foregroundColor(.textSecondary)
                    }
                    
                    Spacer()
                    
                    // Level progress
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("\(profileManager.pointsToNextLevel) to next level")
                            .font(.caption)
                            .foregroundColor(.textSecondary)
                        
                        ProgressView(value: profileManager.levelProgress)
                            .progressViewStyle(LinearProgressViewStyle(tint: .primaryTeal))
                            .frame(width: 100)
                    }
                }
            }
        }
    }
    
    // MARK: - Quick Stats
    
    private var quickStatsView: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Stats")
                .font(.headline)
                .foregroundColor(.textPrimary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                StatCard(
                    title: "Total Sessions",
                    value: "\(profileManager.totalSessions)",
                    icon: "play.circle.fill",
                    color: .primaryTeal
                )
                
                StatCard(
                    title: "Total Time",
                    value: profileManager.formattedTotalTime,
                    icon: "clock.fill",
                    color: .primaryPurple
                )
                
                StatCard(
                    title: "Current Streak",
                    value: "\(profileManager.currentStreak) days",
                    icon: "flame.fill",
                    color: .warning
                )
                
                StatCard(
                    title: "Achievements",
                    value: "\(profileManager.unlockedAchievements)",
                    icon: "trophy.fill",
                    color: .success
                )
            }
        }
    }
    
    // MARK: - Recent Achievements
    
    private var recentAchievementsView: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Recent Achievements")
                    .font(.headline)
                    .foregroundColor(.textPrimary)
                
                Spacer()
                
                Button("View All") {
                    showingAchievements = true
                }
                .font(.subheadline)
                .foregroundColor(.primaryTeal)
            }
            
            if profileManager.recentAchievements.isEmpty {
                SerenityCard {
                    VStack(spacing: 12) {
                        Image(systemName: "trophy")
                            .font(.title)
                            .foregroundColor(.textSecondary)
                        
                        Text("No recent achievements")
                            .font(.subheadline)
                            .foregroundColor(.textSecondary)
                        
                        Text("Keep practicing to unlock new achievements!")
                            .font(.caption)
                            .foregroundColor(.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.vertical, 8)
                }
            } else {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                    ForEach(profileManager.recentAchievements.prefix(4), id: \.achievementId) { achievement in
                        AchievementCard(achievement: achievement, isCompact: true)
                    }
                }
            }
        }
    }
    
    // MARK: - Profile Actions
    
    private var profileActionsView: some View {
        VStack(spacing: 12) {
            ProfileActionRow(
                title: "Statistics & Analytics",
                subtitle: "View detailed practice insights",
                icon: "chart.bar.fill",
                action: {
                    showingStatistics = true
                }
            )
            
            ProfileActionRow(
                title: "Achievements",
                subtitle: "View all unlocked achievements",
                icon: "trophy.fill",
                action: {
                    showingAchievements = true
                }
            )
            
            ProfileActionRow(
                title: "Edit Profile",
                subtitle: "Update your profile information",
                icon: "person.crop.circle",
                action: {
                    showingEditProfile = true
                }
            )
        }
    }
    
    // MARK: - Settings Actions
    
    private var settingsActionsView: some View {
        VStack(spacing: 12) {
            ProfileActionRow(
                title: "Settings & Preferences",
                subtitle: "App settings and notifications",
                icon: "gearshape.fill",
                action: {
                    showingSettings = true
                }
            )
            
            ProfileActionRow(
                title: "Export Data",
                subtitle: "Download your practice data",
                icon: "square.and.arrow.up",
                action: {
                    showingDataExport = true
                }
            )
            
            ProfileActionRow(
                title: "Sign Out",
                subtitle: "Sign out of your account",
                icon: "rectangle.portrait.and.arrow.right",
                isDestructive: true,
                action: {
                    showingSignOutAlert = true
                }
            )
        }
    }
}

// MARK: - Stat Card

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        SerenityCard {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: icon)
                        .font(.title3)
                        .foregroundColor(color)
                    
                    Spacer()
                }
                
                Text(value)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.textPrimary)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }
        }
    }
}

// MARK: - Achievement Card

struct AchievementCard: View {
    let achievement: UserAchievement
    let isCompact: Bool
    
    var body: some View {
        SerenityCard {
            VStack(spacing: 8) {
                // Achievement icon
                ZStack {
                    Circle()
                        .fill(Color(achievement.achievement?.badgeColor ?? "primaryTeal").opacity(0.2))
                        .frame(width: isCompact ? 40 : 60, height: isCompact ? 40 : 60)
                    
                    Image(systemName: achievement.achievement?.iconName ?? "star.fill")
                        .font(.system(size: isCompact ? 20 : 30))
                        .foregroundColor(Color(achievement.achievement?.badgeColor ?? "primaryTeal"))
                }
                
                // Achievement info
                VStack(spacing: 4) {
                    Text(achievement.achievement?.localizedTitle ?? "")
                        .font(isCompact ? .caption : .subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.textPrimary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                    
                    if !isCompact {
                        Text(achievement.achievement?.localizedDescription ?? "")
                            .font(.caption)
                            .foregroundColor(.textSecondary)
                            .multilineTextAlignment(.center)
                            .lineLimit(3)
                    }
                    
                    if let unlockedDate = achievement.unlockedDate {
                        Text(unlockedDate, formatter: DateFormatter.shortDate)
                            .font(.caption2)
                            .foregroundColor(.textSecondary)
                    }
                }
            }
            .padding(.vertical, isCompact ? 4 : 8)
        }
    }
}

// MARK: - Profile Action Row

struct ProfileActionRow: View {
    let title: String
    let subtitle: String
    let icon: String
    let isDestructive: Bool
    let action: () -> Void
    
    init(title: String, subtitle: String, icon: String, isDestructive: Bool = false, action: @escaping () -> Void) {
        self.title = title
        self.subtitle = subtitle
        self.icon = icon
        self.isDestructive = isDestructive
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            SerenityCard {
                HStack(spacing: 16) {
                    // Icon
                    Image(systemName: icon)
                        .font(.title3)
                        .foregroundColor(isDestructive ? .error : .primaryTeal)
                        .frame(width: 24)
                    
                    // Content
                    VStack(alignment: .leading, spacing: 2) {
                        Text(title)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(isDestructive ? .error : .textPrimary)
                        
                        Text(subtitle)
                            .font(.caption)
                            .foregroundColor(.textSecondary)
                    }
                    
                    Spacer()
                    
                    // Chevron
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Date Formatters

extension DateFormatter {
    static let monthYear: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMM yyyy"
        return formatter
    }()
    
    static let shortDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return formatter
    }()
}

// MARK: - Profile Manager

@MainActor
class ProfileManager: ObservableObject {

    // MARK: - Published Properties

    @Published var displayName: String = ""
    @Published var initials: String = ""
    @Published var avatarImage: UIImage?
    @Published var userLevel: Int = 1
    @Published var totalPoints: Int = 0
    @Published var pointsToNextLevel: Int = 100
    @Published var levelProgress: Double = 0.0
    @Published var totalSessions: Int = 0
    @Published var formattedTotalTime: String = "0h 0m"
    @Published var currentStreak: Int = 0
    @Published var unlockedAchievements: Int = 0
    @Published var recentAchievements: [UserAchievement] = []
    @Published var userStatistics: UserStatistics?

    // MARK: - Private Properties

    private let coreDataManager = CoreDataManager.shared
    private var currentUser: User?

    // MARK: - Public Methods

    func loadUserData(_ user: User?) {
        guard let user = user else { return }

        currentUser = user
        displayName = user.displayName ?? user.email ?? "User"
        initials = getInitials(from: displayName)

        // Load avatar if available
        if let avatarData = user.avatarData {
            avatarImage = UIImage(data: avatarData)
        }

        // Load statistics
        loadStatistics(for: user)

        // Load achievements
        loadAchievements(for: user)
    }

    func refreshData() async {
        guard let user = currentUser else { return }
        loadUserData(user)
    }

    func updateAvatar(_ image: UIImage) {
        avatarImage = image

        // Save to user
        if let user = currentUser,
           let imageData = image.jpegData(compressionQuality: 0.8) {
            user.avatarData = imageData
            saveContext()
        }
    }

    func updateDisplayName(_ name: String) {
        displayName = name
        initials = getInitials(from: name)

        // Save to user
        if let user = currentUser {
            user.displayName = name
            saveContext()
        }
    }

    // MARK: - Private Methods

    private func loadStatistics(for user: User) {
        let stats = UserStatistics.getOrCreate(for: user, in: coreDataManager.viewContext)
        userStatistics = stats

        totalSessions = Int(stats.totalSessions)
        formattedTotalTime = formatTime(minutes: stats.totalMinutes)
        currentStreak = Int(stats.currentStreak)

        // Calculate level and points
        calculateLevelAndPoints()
    }

    private func loadAchievements(for user: User) {
        let achievements = UserAchievement.fetchUnlockedAchievements(for: user, in: coreDataManager.viewContext)
        unlockedAchievements = achievements.count

        let recent = UserAchievement.fetchRecentAchievements(for: user, in: coreDataManager.viewContext)
        recentAchievements = recent

        totalPoints = UserAchievement.calculateTotalPoints(for: user, in: coreDataManager.viewContext)
        calculateLevelAndPoints()
    }

    private func calculateLevelAndPoints() {
        // Simple level calculation: 100 points per level
        userLevel = max(1, totalPoints / 100 + 1)
        let pointsInCurrentLevel = totalPoints % 100
        pointsToNextLevel = 100 - pointsInCurrentLevel
        levelProgress = Double(pointsInCurrentLevel) / 100.0
    }

    private func getInitials(from name: String) -> String {
        let components = name.components(separatedBy: " ")
        let initials = components.compactMap { $0.first }.map { String($0) }
        return initials.prefix(2).joined().uppercased()
    }

    private func formatTime(minutes: Double) -> String {
        let hours = Int(minutes) / 60
        let remainingMinutes = Int(minutes) % 60

        if hours > 0 {
            return "\(hours)h \(remainingMinutes)m"
        } else {
            return "\(remainingMinutes)m"
        }
    }

    private func saveContext() {
        do {
            try coreDataManager.viewContext.save()
        } catch {
            print("Error saving profile changes: \(error)")
        }
    }
}

// MARK: - Preview

#Preview {
    ProfileScreen()
        .environmentObject(AuthenticationManager())
}
