//
//  AchievementLibrary.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData

/// Library of predefined achievements and milestones
class AchievementLibrary {
    
    /// Load default achievements into Core Data
    static func loadDefaultAchievements(into context: NSManagedObjectContext) {
        // Check if achievements already exist
        let request: NSFetchRequest<Achievement> = Achievement.fetchRequest()
        
        do {
            let existingAchievements = try context.fetch(request)
            if !existingAchievements.isEmpty {
                print("Achievements already loaded")
                return
            }
        } catch {
            print("Error checking existing achievements: \(error)")
        }
        
        // Create default achievements
        let achievements = createDefaultAchievements()
        
        for (index, achievementData) in achievements.enumerated() {
            let achievement = Achievement(context: context)
            achievement.achievementId = achievementData.id
            achievement.title = achievementData.title
            achievement.localizedTitle = NSLocalizedString(achievementData.title, comment: "Achievement title")
            achievement.achievementDescription = achievementData.description
            achievement.localizedDescription = NSLocalizedString(achievementData.description, comment: "Achievement description")
            achievement.achievementCategory = achievementData.category
            achievement.achievementType = achievementData.type
            achievement.iconName = achievementData.iconName
            achievement.badgeColor = achievementData.badgeColor
            achievement.targetValue = Int32(achievementData.targetValue)
            achievement.currentValue = 0
            achievement.isUnlocked = false
            achievement.sortOrder = Int32(index)
            achievement.isHidden = achievementData.isHidden
            achievement.points = Int32(achievementData.rarity.points)
            achievement.achievementRarity = achievementData.rarity
            achievement.createdDate = Date()
            achievement.updatedDate = Date()
        }
        
        // Save context
        do {
            try context.save()
            print("Successfully loaded \(achievements.count) achievements")
        } catch {
            print("Error saving achievements: \(error)")
        }
    }
    
    /// Create default achievements
    private static func createDefaultAchievements() -> [AchievementData] {
        return [
            // Practice Milestones
            AchievementData(
                id: "first_session",
                title: "First Steps",
                description: "Complete your first practice session",
                category: .practice,
                type: .milestone,
                iconName: "play.circle.fill",
                badgeColor: "success",
                targetValue: 1,
                rarity: .common,
                isHidden: false
            ),
            
            AchievementData(
                id: "sessions_10",
                title: "Getting Started",
                description: "Complete 10 practice sessions",
                category: .practice,
                type: .milestone,
                iconName: "10.circle.fill",
                badgeColor: "primaryTeal",
                targetValue: 10,
                rarity: .common,
                isHidden: false
            ),
            
            AchievementData(
                id: "sessions_50",
                title: "Dedicated Practitioner",
                description: "Complete 50 practice sessions",
                category: .practice,
                type: .milestone,
                iconName: "50.circle.fill",
                badgeColor: "primaryTeal",
                targetValue: 50,
                rarity: .rare,
                isHidden: false
            ),
            
            AchievementData(
                id: "sessions_100",
                title: "Century Club",
                description: "Complete 100 practice sessions",
                category: .practice,
                type: .milestone,
                iconName: "100.circle.fill",
                badgeColor: "primaryPurple",
                targetValue: 100,
                rarity: .epic,
                isHidden: false
            ),
            
            AchievementData(
                id: "sessions_500",
                title: "Master Practitioner",
                description: "Complete 500 practice sessions",
                category: .practice,
                type: .milestone,
                iconName: "crown.fill",
                badgeColor: "warning",
                targetValue: 500,
                rarity: .legendary,
                isHidden: true
            ),
            
            // Time-based Achievements
            AchievementData(
                id: "minutes_60",
                title: "First Hour",
                description: "Practice for a total of 60 minutes",
                category: .time,
                type: .milestone,
                iconName: "clock.fill",
                badgeColor: "info",
                targetValue: 60,
                rarity: .common,
                isHidden: false
            ),
            
            AchievementData(
                id: "minutes_600",
                title: "Ten Hours Strong",
                description: "Practice for a total of 10 hours",
                category: .time,
                type: .milestone,
                iconName: "timer",
                badgeColor: "info",
                targetValue: 600,
                rarity: .rare,
                isHidden: false
            ),
            
            AchievementData(
                id: "minutes_3000",
                title: "Time Master",
                description: "Practice for a total of 50 hours",
                category: .time,
                type: .milestone,
                iconName: "hourglass.fill",
                badgeColor: "primaryPurple",
                targetValue: 3000,
                rarity: .epic,
                isHidden: false
            ),
            
            AchievementData(
                id: "minutes_6000",
                title: "Zen Master",
                description: "Practice for a total of 100 hours",
                category: .time,
                type: .milestone,
                iconName: "infinity",
                badgeColor: "warning",
                targetValue: 6000,
                rarity: .legendary,
                isHidden: true
            ),
            
            // Streak Achievements
            AchievementData(
                id: "streak_3",
                title: "Building Habits",
                description: "Practice for 3 consecutive days",
                category: .streak,
                type: .streak,
                iconName: "flame.fill",
                badgeColor: "warning",
                targetValue: 3,
                rarity: .common,
                isHidden: false
            ),
            
            AchievementData(
                id: "streak_7",
                title: "Week Warrior",
                description: "Practice for 7 consecutive days",
                category: .streak,
                type: .streak,
                iconName: "flame.fill",
                badgeColor: "warning",
                targetValue: 7,
                rarity: .rare,
                isHidden: false
            ),
            
            AchievementData(
                id: "streak_30",
                title: "Monthly Master",
                description: "Practice for 30 consecutive days",
                category: .streak,
                type: .streak,
                iconName: "flame.fill",
                badgeColor: "error",
                targetValue: 30,
                rarity: .epic,
                isHidden: false
            ),
            
            AchievementData(
                id: "streak_100",
                title: "Unstoppable",
                description: "Practice for 100 consecutive days",
                category: .streak,
                type: .streak,
                iconName: "flame.fill",
                badgeColor: "error",
                targetValue: 100,
                rarity: .legendary,
                isHidden: true
            ),
            
            // Exploration Achievements
            AchievementData(
                id: "breathing_types_5",
                title: "Breath Explorer",
                description: "Try 5 different breathing exercises",
                category: .exploration,
                type: .exploration,
                iconName: "lungs.fill",
                badgeColor: "success",
                targetValue: 5,
                rarity: .common,
                isHidden: false
            ),
            
            AchievementData(
                id: "meditation_types_5",
                title: "Mind Explorer",
                description: "Try 5 different meditation types",
                category: .exploration,
                type: .exploration,
                iconName: "brain.head.profile",
                badgeColor: "primaryTeal",
                targetValue: 5,
                rarity: .rare,
                isHidden: false
            ),
            
            AchievementData(
                id: "sleep_content_10",
                title: "Dream Seeker",
                description: "Listen to 10 different sleep stories",
                category: .exploration,
                type: .exploration,
                iconName: "moon.fill",
                badgeColor: "primaryPurple",
                targetValue: 10,
                rarity: .rare,
                isHidden: false
            ),
            
            AchievementData(
                id: "all_categories",
                title: "Well-Rounded",
                description: "Practice breathing, meditation, and sleep content",
                category: .exploration,
                type: .variety,
                iconName: "star.circle.fill",
                badgeColor: "warning",
                targetValue: 3,
                rarity: .epic,
                isHidden: false
            ),
            
            // Perfectionist Achievements
            AchievementData(
                id: "perfect_week",
                title: "Perfect Week",
                description: "Complete all planned sessions for a week",
                category: .practice,
                type: .perfectionist,
                iconName: "checkmark.seal.fill",
                badgeColor: "success",
                targetValue: 1,
                rarity: .rare,
                isHidden: false
            ),
            
            AchievementData(
                id: "long_session_30",
                title: "Deep Dive",
                description: "Complete a 30-minute session",
                category: .practice,
                type: .dedication,
                iconName: "arrow.down.circle.fill",
                badgeColor: "info",
                targetValue: 30,
                rarity: .rare,
                isHidden: false
            ),
            
            AchievementData(
                id: "long_session_60",
                title: "Hour of Peace",
                description: "Complete a 60-minute session",
                category: .practice,
                type: .dedication,
                iconName: "clock.badge.checkmark.fill",
                badgeColor: "primaryPurple",
                targetValue: 60,
                rarity: .epic,
                isHidden: false
            ),
            
            // Special Achievements
            AchievementData(
                id: "early_bird",
                title: "Early Bird",
                description: "Practice before 7 AM",
                category: .special,
                type: .dedication,
                iconName: "sunrise.fill",
                badgeColor: "warning",
                targetValue: 1,
                rarity: .rare,
                isHidden: false
            ),
            
            AchievementData(
                id: "night_owl",
                title: "Night Owl",
                description: "Practice after 10 PM",
                category: .special,
                type: .dedication,
                iconName: "moon.stars.fill",
                badgeColor: "primaryPurple",
                targetValue: 1,
                rarity: .rare,
                isHidden: false
            ),
            
            AchievementData(
                id: "weekend_warrior",
                title: "Weekend Warrior",
                description: "Practice on both Saturday and Sunday",
                category: .special,
                type: .dedication,
                iconName: "calendar.badge.plus",
                badgeColor: "success",
                targetValue: 1,
                rarity: .common,
                isHidden: false
            ),
            
            AchievementData(
                id: "birthday_practice",
                title: "Birthday Blessing",
                description: "Practice on your birthday",
                category: .special,
                type: .dedication,
                iconName: "gift.fill",
                badgeColor: "error",
                targetValue: 1,
                rarity: .epic,
                isHidden: true
            ),
            
            AchievementData(
                id: "new_year_practice",
                title: "New Year, New You",
                description: "Practice on New Year's Day",
                category: .special,
                type: .dedication,
                iconName: "sparkles",
                badgeColor: "warning",
                targetValue: 1,
                rarity: .epic,
                isHidden: true
            ),
            
            // Social Achievements (for future features)
            AchievementData(
                id: "share_achievement",
                title: "Inspiration Spreader",
                description: "Share your first achievement",
                category: .social,
                type: .exploration,
                iconName: "square.and.arrow.up.fill",
                badgeColor: "primaryTeal",
                targetValue: 1,
                rarity: .common,
                isHidden: false
            ),
            
            AchievementData(
                id: "invite_friend",
                title: "Community Builder",
                description: "Invite a friend to join Serenity",
                category: .social,
                type: .exploration,
                iconName: "person.2.fill",
                badgeColor: "primaryPurple",
                targetValue: 1,
                rarity: .rare,
                isHidden: false
            )
        ]
    }
}

// MARK: - Achievement Data Structure

private struct AchievementData {
    let id: String
    let title: String
    let description: String
    let category: AchievementCategory
    let type: AchievementType
    let iconName: String
    let badgeColor: String
    let targetValue: Int
    let rarity: AchievementRarity
    let isHidden: Bool
}
