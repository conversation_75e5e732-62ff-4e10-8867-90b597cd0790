//
//  MeditationSessionManager.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import SwiftUI
import AVFoundation
import UIKit
import CoreData

/// Manages meditation sessions including timer, audio, and progress tracking
@MainActor
class MeditationSessionManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var sessionState: SessionState = .idle
    @Published var currentSession: MeditationSession?
    @Published var timeRemaining: TimeInterval = 0
    @Published var timeElapsed: TimeInterval = 0
    @Published var progress: Double = 0.0
    @Published var currentPhase: MeditationPhase = .preparation
    @Published var backgroundSound: BackgroundSound = .none
    @Published var soundVolume: Float = 0.5
    @Published var isAudioPlaying: Bool = false
    @Published var showingSessionComplete: Bool = false
    @Published var sessionFeedback: SessionFeedback?
    
    // MARK: - Private Properties
    
    private var timer: Timer?
    private var audioPlayer: AVAudioPlayer?
    private var backgroundAudioPlayer: AVAudioPlayer?
    private var sessionStartTime: Date?
    private var totalDuration: TimeInterval = 0
    private let coreDataManager = CoreDataManager.shared
    private let hapticManager = HapticManager()
    
    // MARK: - Initialization
    
    init() {
        setupAudioSession()
    }
    
    deinit {
        stopSession()
        audioPlayer?.stop()
        backgroundAudioPlayer?.stop()
    }
    
    // MARK: - Session Management
    
    /// Start a new meditation session
    func startSession(exercise: MeditationExercise, duration: TimeInterval, user: User) {
        guard sessionState == .idle else { return }
        
        // Create new session
        let context = coreDataManager.viewContext
        let session = MeditationSession.create(
            in: context,
            user: user,
            exercise: exercise,
            plannedDuration: Int(duration)
        )
        
        currentSession = session
        totalDuration = duration
        timeRemaining = duration
        timeElapsed = 0
        progress = 0.0
        sessionState = .active
        currentPhase = .preparation
        sessionStartTime = Date()
        
        // Setup audio
        setupSessionAudio(exercise: exercise)
        
        // Start timer
        startTimer()
        
        // Trigger haptic feedback
        triggerHapticFeedback(.medium)
        
        // Save session
        saveSession()
        
        print("Started meditation session: \(exercise.localizedName) for \(duration/60) minutes")
    }
    
    /// Pause the current session
    func pauseSession() {
        guard sessionState == .active else { return }
        
        sessionState = .paused
        stopTimer()
        audioPlayer?.pause()
        backgroundAudioPlayer?.pause()
        
        triggerHapticFeedback(.light)
        
        print("Meditation session paused")
    }
    
    /// Resume the paused session
    func resumeSession() {
        guard sessionState == .paused else { return }
        
        sessionState = .active
        startTimer()
        audioPlayer?.play()
        backgroundAudioPlayer?.play()
        
        triggerHapticFeedback(.light)
        
        print("Meditation session resumed")
    }
    
    /// Stop the current session
    func stopSession() {
        guard sessionState != .idle else { return }
        
        stopTimer()
        audioPlayer?.stop()
        backgroundAudioPlayer?.stop()
        
        if let session = currentSession {
            if sessionState == .completed {
                session.complete()
            } else {
                session.cancel(reason: "User stopped session")
            }
            saveSession()
        }
        
        resetSession()
        
        print("Meditation session stopped")
    }
    
    /// Complete the current session
    func completeSession() {
        guard let session = currentSession else { return }
        
        sessionState = .completed
        stopTimer()
        
        // Create feedback object
        sessionFeedback = SessionFeedback(session: session)
        
        // Show completion screen
        showingSessionComplete = true
        
        // Trigger completion haptic
        triggerHapticFeedback(.success)
        
        print("Meditation session completed")
    }
    
    /// Submit session feedback
    func submitFeedback(rating: Int, focusLevel: Int, calmLevel: Int, notes: String?, insights: String?) {
        guard let session = currentSession else { return }
        
        session.complete(
            with: rating,
            focusLevel: focusLevel,
            calmLevel: calmLevel,
            notes: notes,
            insights: insights
        )
        
        saveSession()
        resetSession()
        
        showingSessionComplete = false
        sessionFeedback = nil
        
        print("Session feedback submitted")
    }
    
    // MARK: - Timer Management
    
    private func startTimer() {
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateTimer()
            }
        }
    }
    
    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }
    
    private func updateTimer() {
        guard sessionState == .active else { return }
        
        timeElapsed += 1
        timeRemaining = max(0, totalDuration - timeElapsed)
        progress = timeElapsed / totalDuration
        
        // Update meditation phase
        updateMeditationPhase()
        
        // Check if session is complete
        if timeRemaining <= 0 {
            completeSession()
        }
        
        // Trigger interval haptics (every 5 minutes)
        if Int(timeElapsed) % 300 == 0 && timeElapsed > 0 {
            triggerHapticFeedback(.light)
        }
    }
    
    // MARK: - Audio Management
    
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [.mixWithOthers])
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("Error setting up audio session: \(error)")
        }
    }
    
    private func setupSessionAudio(exercise: MeditationExercise) {
        // Setup guided audio if available
        if let audioUrl = exercise.audioUrl,
           let url = Bundle.main.url(forResource: audioUrl.replacingOccurrences(of: ".mp3", with: ""), withExtension: "mp3") {
            do {
                audioPlayer = try AVAudioPlayer(contentsOf: url)
                audioPlayer?.prepareToPlay()
                audioPlayer?.volume = 0.8
            } catch {
                print("Error setting up guided audio: \(error)")
            }
        }
        
        // Setup background sound
        setupBackgroundSound()
    }
    
    private func setupBackgroundSound() {
        guard backgroundSound != .none,
              let fileName = backgroundSound.audioFileName,
              let url = Bundle.main.url(forResource: fileName.replacingOccurrences(of: ".mp3", with: ""), withExtension: "mp3") else {
            backgroundAudioPlayer?.stop()
            backgroundAudioPlayer = nil
            return
        }
        
        do {
            backgroundAudioPlayer = try AVAudioPlayer(contentsOf: url)
            backgroundAudioPlayer?.numberOfLoops = -1 // Loop indefinitely
            backgroundAudioPlayer?.volume = soundVolume
            backgroundAudioPlayer?.prepareToPlay()
            
            if sessionState == .active {
                backgroundAudioPlayer?.play()
            }
        } catch {
            print("Error setting up background sound: \(error)")
        }
    }
    
    /// Update background sound
    func setBackgroundSound(_ sound: BackgroundSound) {
        backgroundSound = sound
        setupBackgroundSound()
        
        // Update current session
        currentSession?.backgroundSound = sound.rawValue
        saveSession()
    }
    
    /// Update sound volume
    func setSoundVolume(_ volume: Float) {
        soundVolume = volume
        backgroundAudioPlayer?.volume = volume
        
        // Update current session
        currentSession?.soundVolume = volume
        saveSession()
    }
    
    // MARK: - Phase Management
    
    private func updateMeditationPhase() {
        let progressPercentage = progress * 100
        
        switch progressPercentage {
        case 0..<5:
            currentPhase = .preparation
        case 5..<15:
            currentPhase = .settling
        case 15..<85:
            currentPhase = .practice
        case 85..<95:
            currentPhase = .deepening
        default:
            currentPhase = .completion
        }
    }
    
    // MARK: - Haptic Feedback
    
    private func triggerHapticFeedback(_ type: UIImpactFeedbackGenerator.FeedbackStyle) {
        hapticManager.triggerImpact(style: type)
    }
    
    // MARK: - Data Management
    
    private func saveSession() {
        do {
            try coreDataManager.viewContext.save()
        } catch {
            print("Error saving meditation session: \(error)")
        }
    }
    
    private func resetSession() {
        currentSession = nil
        sessionState = .idle
        timeRemaining = 0
        timeElapsed = 0
        progress = 0.0
        currentPhase = .preparation
        sessionStartTime = nil
        totalDuration = 0
        isAudioPlaying = false
        
        audioPlayer?.stop()
        backgroundAudioPlayer?.stop()
        audioPlayer = nil
        backgroundAudioPlayer = nil
    }
    
    // MARK: - Session Progress
    
    /// Get current session progress as percentage
    func getSessionProgress() -> Double {
        return progress * 100
    }
    
    /// Get formatted time remaining
    func getFormattedTimeRemaining() -> String {
        let minutes = Int(timeRemaining) / 60
        let seconds = Int(timeRemaining) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    /// Get formatted time elapsed
    func getFormattedTimeElapsed() -> String {
        let minutes = Int(timeElapsed) / 60
        let seconds = Int(timeElapsed) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    /// Check if session can be paused
    func canPauseSession() -> Bool {
        return sessionState == .active
    }
    
    /// Check if session can be resumed
    func canResumeSession() -> Bool {
        return sessionState == .paused
    }
}

// MARK: - Meditation Phase

enum MeditationPhase: String, CaseIterable {
    case preparation = "preparation"
    case settling = "settling"
    case practice = "practice"
    case deepening = "deepening"
    case completion = "completion"
    
    var displayName: String {
        switch self {
        case .preparation:
            return NSLocalizedString("Preparation", comment: "Meditation preparation phase")
        case .settling:
            return NSLocalizedString("Settling In", comment: "Meditation settling phase")
        case .practice:
            return NSLocalizedString("Practice", comment: "Meditation practice phase")
        case .deepening:
            return NSLocalizedString("Deepening", comment: "Meditation deepening phase")
        case .completion:
            return NSLocalizedString("Completion", comment: "Meditation completion phase")
        }
    }
    
    var description: String {
        switch self {
        case .preparation:
            return NSLocalizedString("Getting comfortable and ready", comment: "Preparation phase description")
        case .settling:
            return NSLocalizedString("Settling into the practice", comment: "Settling phase description")
        case .practice:
            return NSLocalizedString("Main meditation practice", comment: "Practice phase description")
        case .deepening:
            return NSLocalizedString("Deepening the experience", comment: "Deepening phase description")
        case .completion:
            return NSLocalizedString("Preparing to finish", comment: "Completion phase description")
        }
    }
}

// MARK: - Session Feedback

struct SessionFeedback {
    let session: MeditationSession
    var rating: Int = 0
    var focusLevel: Int = 0
    var calmLevel: Int = 0
    var notes: String = ""
    var insights: String = ""
    var moodAfter: Mood?
    
    init(session: MeditationSession) {
        self.session = session
    }
}

// MARK: - Haptic Manager

private class HapticManager {
    
    func triggerImpact(style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let impactGenerator = UIImpactFeedbackGenerator(style: style)
        impactGenerator.impactOccurred()
    }
    
    func triggerNotification(type: UINotificationFeedbackGenerator.FeedbackType) {
        let notificationGenerator = UINotificationFeedbackGenerator()
        notificationGenerator.notificationOccurred(type)
    }
    
    func triggerSelection() {
        let selectionGenerator = UISelectionFeedbackGenerator()
        selectionGenerator.selectionChanged()
    }
}
