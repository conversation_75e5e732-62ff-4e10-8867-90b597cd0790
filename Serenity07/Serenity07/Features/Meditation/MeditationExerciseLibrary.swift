//
//  MeditationExerciseLibrary.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData

/// Library of predefined meditation exercises
class MeditationExerciseLibrary {
    
    /// Load default meditation exercises into Core Data
    static func loadDefaultExercises(into context: NSManagedObjectContext) {
        // Check if exercises already exist
        let request: NSFetchRequest<MeditationExercise> = MeditationExercise.fetchRequest()
        
        do {
            let existingExercises = try context.fetch(request)
            if !existingExercises.isEmpty {
                print("Meditation exercises already loaded")
                return
            }
        } catch {
            print("Error checking existing meditation exercises: \(error)")
        }
        
        // Create default exercises
        let exercises = createDefaultExercises()
        
        for (index, exerciseData) in exercises.enumerated() {
            let exercise = MeditationExercise(context: context)
            exercise.exerciseId = exerciseData.id
            exercise.name = exerciseData.name
            exercise.localizedName = NSLocalizedString(exerciseData.name, comment: "Meditation exercise name")
            exercise.exerciseDescription = NSLocalizedString(exerciseData.description, comment: "Meditation exercise description")
            exercise.instructions = exerciseData.instructions
            exercise.benefits = exerciseData.benefits
            exercise.difficulty = exerciseData.difficulty.rawValue
            exercise.category = exerciseData.category.rawValue
            exercise.guidanceType = exerciseData.guidanceType.rawValue
            exercise.durationOptions = exerciseData.durationOptionsJSON
            exercise.audioUrl = exerciseData.audioUrl
            exercise.backgroundSoundUrl = exerciseData.backgroundSoundUrl
            exercise.imageUrl = exerciseData.imageUrl
            exercise.isActive = true
            exercise.sortOrder = Int32(index)
            exercise.isPremium = exerciseData.isPremium
            exercise.createdDate = Date()
            exercise.updatedDate = Date()
            exercise.tags = exerciseData.tagsJSON
        }
        
        // Save context
        do {
            try context.save()
            print("Successfully loaded \(exercises.count) meditation exercises")
        } catch {
            print("Error saving meditation exercises: \(error)")
        }
    }
    
    /// Create default meditation exercises
    private static func createDefaultExercises() -> [MeditationExerciseData] {
        return [
            // Mindfulness Meditations
            MeditationExerciseData(
                id: "mindful_breathing",
                name: "Mindful Breathing",
                description: "Focus on your breath to cultivate present-moment awareness",
                instructions: "Sit comfortably with your back straight\nClose your eyes or soften your gaze\nBring attention to your natural breath\nNotice the sensation of breathing in and out\nWhen your mind wanders, gently return to the breath\nContinue for the duration of the session",
                benefits: "Reduces stress, Improves focus, Increases self-awareness, Promotes relaxation",
                difficulty: .beginner,
                category: .mindfulness,
                guidanceType: .guided,
                durationOptions: [300, 600, 900, 1200, 1800], // 5, 10, 15, 20, 30 minutes
                audioUrl: "meditation_mindful_breathing.mp3",
                backgroundSoundUrl: "nature_sounds.mp3",
                imageUrl: "meditation_breathing.jpg",
                isPremium: false,
                tags: ["breathing", "mindfulness", "beginner", "stress-relief"]
            ),
            
            MeditationExerciseData(
                id: "present_moment",
                name: "Present Moment Awareness",
                description: "Develop awareness of the present moment through mindful observation",
                instructions: "Find a comfortable seated position\nTake three deep breaths to center yourself\nNotice what you can hear around you\nObserve any physical sensations in your body\nNotice thoughts as they arise without judgment\nReturn attention to the present moment\nRest in this awareness",
                benefits: "Enhances mindfulness, Reduces anxiety, Improves emotional regulation, Increases clarity",
                difficulty: .beginner,
                category: .mindfulness,
                guidanceType: .guided,
                durationOptions: [600, 900, 1200, 1800],
                audioUrl: "meditation_present_moment.mp3",
                backgroundSoundUrl: "forest_sounds.mp3",
                imageUrl: "meditation_present.jpg",
                isPremium: false,
                tags: ["mindfulness", "awareness", "present", "beginner"]
            ),
            
            // Body Scan Meditations
            MeditationExerciseData(
                id: "full_body_scan",
                name: "Full Body Scan",
                description: "Systematically relax your entire body from head to toe",
                instructions: "Lie down comfortably on your back\nClose your eyes and take several deep breaths\nStart by focusing on the top of your head\nSlowly move your attention down through your body\nNotice each part without trying to change anything\nSpend time with areas of tension or discomfort\nEnd by feeling your whole body as one unified sensation",
                benefits: "Deep relaxation, Tension release, Body awareness, Better sleep",
                difficulty: .beginner,
                category: .bodyScan,
                guidanceType: .guided,
                durationOptions: [900, 1200, 1800, 2400], // 15, 20, 30, 40 minutes
                audioUrl: "meditation_body_scan.mp3",
                backgroundSoundUrl: "rain_sounds.mp3",
                imageUrl: "meditation_body_scan.jpg",
                isPremium: false,
                tags: ["body-scan", "relaxation", "tension-relief", "sleep"]
            ),
            
            MeditationExerciseData(
                id: "progressive_relaxation",
                name: "Progressive Muscle Relaxation",
                description: "Tense and release muscle groups to achieve deep relaxation",
                instructions: "Lie down in a comfortable position\nStart with your toes - tense them for 5 seconds\nRelease and notice the relaxation\nMove up to your calves, thighs, and so on\nTense each muscle group then release\nNotice the contrast between tension and relaxation\nEnd with your whole body completely relaxed",
                benefits: "Muscle tension relief, Stress reduction, Improved sleep, Physical relaxation",
                difficulty: .intermediate,
                category: .bodyScan,
                guidanceType: .guided,
                durationOptions: [900, 1200, 1800],
                audioUrl: "meditation_progressive_relaxation.mp3",
                backgroundSoundUrl: "ocean_sounds.mp3",
                imageUrl: "meditation_relaxation.jpg",
                isPremium: true,
                tags: ["progressive", "muscle", "relaxation", "tension", "sleep"]
            ),
            
            // Loving-Kindness Meditations
            MeditationExerciseData(
                id: "loving_kindness_basic",
                name: "Loving-Kindness for Self",
                description: "Cultivate compassion and kindness toward yourself",
                instructions: "Sit comfortably and close your eyes\nBring yourself to mind with kindness\nRepeat these phrases silently:\n'May I be happy'\n'May I be healthy'\n'May I be at peace'\n'May I live with ease'\nFeel the intention behind each phrase\nNotice any resistance with compassion",
                benefits: "Self-compassion, Emotional healing, Reduced self-criticism, Inner peace",
                difficulty: .beginner,
                category: .lovingKindness,
                guidanceType: .guided,
                durationOptions: [600, 900, 1200],
                audioUrl: "meditation_loving_kindness_self.mp3",
                backgroundSoundUrl: "chimes_sounds.mp3",
                imageUrl: "meditation_loving_kindness.jpg",
                isPremium: false,
                tags: ["loving-kindness", "self-compassion", "healing", "peace"]
            ),
            
            MeditationExerciseData(
                id: "loving_kindness_others",
                name: "Loving-Kindness for Others",
                description: "Extend compassion and goodwill to loved ones and all beings",
                instructions: "Begin with loving-kindness for yourself\nBring a loved one to mind\nOffer them the same kind wishes\nExtend to a neutral person\nInclude someone you have difficulty with\nFinally, extend to all beings everywhere\nRest in the feeling of universal compassion",
                benefits: "Compassion cultivation, Relationship healing, Emotional balance, Connection",
                difficulty: .intermediate,
                category: .lovingKindness,
                guidanceType: .guided,
                durationOptions: [900, 1200, 1800, 2400],
                audioUrl: "meditation_loving_kindness_others.mp3",
                backgroundSoundUrl: "tibetan_bowls.mp3",
                imageUrl: "meditation_compassion.jpg",
                isPremium: true,
                tags: ["loving-kindness", "compassion", "relationships", "healing"]
            ),
            
            // Visualization Meditations
            MeditationExerciseData(
                id: "peaceful_place",
                name: "Peaceful Place Visualization",
                description: "Create a mental sanctuary for deep relaxation and peace",
                instructions: "Close your eyes and take several deep breaths\nImagine a place where you feel completely safe and peaceful\nThis could be real or imaginary\nNotice the colors, sounds, and sensations\nExplore this place with all your senses\nFeel the peace and safety it provides\nKnow you can return here anytime",
                benefits: "Stress relief, Mental escape, Creativity enhancement, Emotional regulation",
                difficulty: .beginner,
                category: .visualization,
                guidanceType: .guided,
                durationOptions: [600, 900, 1200, 1800],
                audioUrl: "meditation_peaceful_place.mp3",
                backgroundSoundUrl: "nature_sounds.mp3",
                imageUrl: "meditation_peaceful_place.jpg",
                isPremium: false,
                tags: ["visualization", "peaceful", "sanctuary", "stress-relief"]
            ),
            
            // Focus Meditations
            MeditationExerciseData(
                id: "single_point_focus",
                name: "Single-Point Focus",
                description: "Develop concentration by focusing on a single object or sensation",
                instructions: "Choose a focus point - breath, candle flame, or sound\nSit with good posture and relax your body\nDirect your full attention to your chosen object\nWhen your mind wanders, gently return to the focus\nDon't judge the wandering - simply return\nWith practice, your focus will become steadier",
                benefits: "Improved concentration, Mental clarity, Reduced mind-wandering, Enhanced focus",
                difficulty: .intermediate,
                category: .focus,
                guidanceType: .mixed,
                durationOptions: [600, 900, 1200, 1800, 2400],
                audioUrl: "meditation_single_point.mp3",
                backgroundSoundUrl: "white_noise.mp3",
                imageUrl: "meditation_focus.jpg",
                isPremium: true,
                tags: ["focus", "concentration", "clarity", "attention"]
            ),
            
            // Stress Relief Meditations
            MeditationExerciseData(
                id: "stress_release",
                name: "Stress Release Meditation",
                description: "Let go of tension and stress through mindful awareness",
                instructions: "Sit or lie down comfortably\nTake several deep, cleansing breaths\nNotice where you hold stress in your body\nBreathe into these areas with compassion\nImagine stress leaving with each exhale\nReplace tension with calm, peaceful energy\nEnd feeling refreshed and renewed",
                benefits: "Stress reduction, Tension relief, Emotional balance, Mental clarity",
                difficulty: .beginner,
                category: .stress,
                guidanceType: .guided,
                durationOptions: [300, 600, 900, 1200],
                audioUrl: "meditation_stress_release.mp3",
                backgroundSoundUrl: "rain_sounds.mp3",
                imageUrl: "meditation_stress_relief.jpg",
                isPremium: false,
                tags: ["stress", "tension", "relief", "calm"]
            ),
            
            // Sleep Meditations
            MeditationExerciseData(
                id: "sleep_preparation",
                name: "Sleep Preparation",
                description: "Prepare your mind and body for restful sleep",
                instructions: "Lie down in your bed comfortably\nTake slow, deep breaths to relax\nTense and release each part of your body\nLet go of the day's thoughts and worries\nFocus on the sensation of sinking into your bed\nAllow your breathing to become natural and slow\nDrift peacefully toward sleep",
                benefits: "Better sleep quality, Faster sleep onset, Reduced insomnia, Deep relaxation",
                difficulty: .beginner,
                category: .sleep,
                guidanceType: .guided,
                durationOptions: [900, 1200, 1800, 2400],
                audioUrl: "meditation_sleep_prep.mp3",
                backgroundSoundUrl: "brown_noise.mp3",
                imageUrl: "meditation_sleep.jpg",
                isPremium: false,
                tags: ["sleep", "bedtime", "insomnia", "relaxation"]
            )
        ]
    }
}

// MARK: - Meditation Exercise Data Structure

private struct MeditationExerciseData {
    let id: String
    let name: String
    let description: String
    let instructions: String
    let benefits: String
    let difficulty: MeditationDifficulty
    let category: MeditationCategory
    let guidanceType: GuidanceType
    let durationOptions: [Int]
    let audioUrl: String?
    let backgroundSoundUrl: String?
    let imageUrl: String?
    let isPremium: Bool
    let tags: [String]
    
    var durationOptionsJSON: String {
        guard let data = try? JSONEncoder().encode(durationOptions),
              let json = String(data: data, encoding: .utf8) else {
            return "[]"
        }
        return json
    }
    
    var tagsJSON: String {
        guard let data = try? JSONEncoder().encode(tags),
              let json = String(data: data, encoding: .utf8) else {
            return "[]"
        }
        return json
    }
}
