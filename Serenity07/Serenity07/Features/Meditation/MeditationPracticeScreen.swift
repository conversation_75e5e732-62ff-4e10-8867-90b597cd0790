//
//  MeditationPracticeScreen.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct MeditationPracticeScreen: View {
    
    // MARK: - Properties
    
    let exercise: MeditationExercise
    let selectedDuration: Int
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var authManager: AuthenticationManager
    @StateObject private var sessionManager = MeditationSessionManager()
    @State private var showingSettings = false
    @State private var showingExitConfirmation = false
    @State private var selectedBackgroundSound: BackgroundSound = .none
    @State private var soundVolume: Float = 0.5
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            // Background
            Color.appBackground
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // Header
                headerView
                
                Spacer()
                
                // Main content based on session state
                mainContentView
                
                Spacer()
                
                // Controls
                controlsView
                
                // Bottom padding
                Rectangle()
                    .fill(Color.clear)
                    .frame(height: 50)
            }
        }
        .navigationBarHidden(true)
        .preferredColorScheme(.dark)
        .onAppear {
            setupSession()
        }
        .sheet(isPresented: $showingSettings) {
            MeditationSettingsSheet(
                backgroundSound: $selectedBackgroundSound,
                soundVolume: $soundVolume,
                onSoundChange: { sound in
                    sessionManager.setBackgroundSound(sound)
                },
                onVolumeChange: { volume in
                    sessionManager.setSoundVolume(volume)
                }
            )
        }
        .sheet(isPresented: $sessionManager.showingSessionComplete) {
            MeditationCompletionSheet(
                sessionManager: sessionManager,
                onDismiss: {
                    dismiss()
                }
            )
        }
        .alert("End Session?", isPresented: $showingExitConfirmation) {
            Button("Continue Session", role: .cancel) { }
            Button("End Session", role: .destructive) {
                sessionManager.stopSession()
                dismiss()
            }
        } message: {
            Text("Are you sure you want to end your meditation session? Your progress will be saved.")
        }
    }
    
    // MARK: - Header View
    
    private var headerView: some View {
        HStack {
            // Back button
            Button(action: {
                if sessionManager.sessionState == .active || sessionManager.sessionState == .paused {
                    showingExitConfirmation = true
                } else {
                    dismiss()
                }
            }) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .foregroundColor(.textPrimary)
            }
            
            Spacer()
            
            // Exercise info
            VStack(spacing: 4) {
                Text(exercise.localizedName)
                    .font(.headline)
                    .foregroundColor(.textPrimary)
                
                Text(exercise.meditationCategory.displayName)
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }
            
            Spacer()
            
            // Settings button
            Button(action: {
                showingSettings = true
            }) {
                Image(systemName: "slider.horizontal.3")
                    .font(.title2)
                    .foregroundColor(.textPrimary)
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    // MARK: - Main Content View
    
    private var mainContentView: some View {
        VStack(spacing: 40) {
            // Session phase indicator
            if sessionManager.sessionState != .idle {
                phaseIndicatorView
            }
            
            // Central meditation visual
            meditationVisualView
            
            // Timer display
            timerDisplayView
            
            // Progress indicator
            if sessionManager.sessionState != .idle {
                progressIndicatorView
            }
        }
        .padding(.horizontal, 30)
    }
    
    // MARK: - Phase Indicator
    
    private var phaseIndicatorView: some View {
        VStack(spacing: 8) {
            Text(sessionManager.currentPhase.displayName)
                .font(.title3)
                .fontWeight(.medium)
                .foregroundColor(.primaryTeal)
            
            Text(sessionManager.currentPhase.description)
                .font(.caption)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)
        }
        .opacity(sessionManager.sessionState == .active ? 1.0 : 0.6)
        .animation(.easeInOut(duration: 0.3), value: sessionManager.currentPhase)
    }
    
    // MARK: - Meditation Visual
    
    private var meditationVisualView: some View {
        ZStack {
            // Outer ring
            Circle()
                .stroke(Color.primaryTeal.opacity(0.2), lineWidth: 2)
                .frame(width: 280, height: 280)
            
            // Progress ring
            Circle()
                .trim(from: 0, to: sessionManager.progress)
                .stroke(
                    LinearGradient(
                        colors: [.primaryTeal, .primaryPurple],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    style: StrokeStyle(lineWidth: 4, lineCap: .round)
                )
                .frame(width: 280, height: 280)
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1.0), value: sessionManager.progress)
            
            // Inner breathing circle
            MeditationBreathingCircle(
                isAnimating: sessionManager.sessionState == .active,
                phase: sessionManager.currentPhase
            )
            .frame(width: 200, height: 200)
            
            // Center icon
            Image(systemName: exercise.meditationCategory.iconName)
                .font(.system(size: 40))
                .foregroundColor(.primaryTeal)
                .opacity(0.8)
        }
    }
    
    // MARK: - Timer Display
    
    private var timerDisplayView: some View {
        VStack(spacing: 8) {
            if sessionManager.sessionState == .idle {
                Text("\(selectedDuration / 60) min")
                    .font(.system(size: 48, weight: .light, design: .rounded))
                    .foregroundColor(.textPrimary)
            } else {
                Text(sessionManager.getFormattedTimeRemaining())
                    .font(.system(size: 48, weight: .light, design: .rounded))
                    .foregroundColor(.textPrimary)
                    .contentTransition(.numericText())
                    .animation(.easeInOut(duration: 0.3), value: sessionManager.timeRemaining)
            }
            
            Text(sessionManager.sessionState == .idle ? "Duration" : "Remaining")
                .font(.caption)
                .foregroundColor(.textSecondary)
        }
    }
    
    // MARK: - Progress Indicator
    
    private var progressIndicatorView: some View {
        VStack(spacing: 12) {
            // Progress bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(height: 4)
                        .cornerRadius(2)
                    
                    Rectangle()
                        .fill(LinearGradient(
                            colors: [.primaryTeal, .primaryPurple],
                            startPoint: .leading,
                            endPoint: .trailing
                        ))
                        .frame(width: geometry.size.width * sessionManager.progress, height: 4)
                        .cornerRadius(2)
                        .animation(.easeInOut(duration: 0.5), value: sessionManager.progress)
                }
            }
            .frame(height: 4)
            
            // Progress percentage
            Text("\(Int(sessionManager.getSessionProgress()))% Complete")
                .font(.caption)
                .foregroundColor(.textSecondary)
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - Controls View
    
    private var controlsView: some View {
        HStack(spacing: 40) {
            if sessionManager.sessionState == .idle {
                // Start button
                SerenityButton(
                    title: "Start Meditation",
                    style: .primary,
                    size: .large,
                    action: {
                        startMeditation()
                    }
                )
            } else {
                // Pause/Resume button
                Button(action: {
                    if sessionManager.canPauseSession() {
                        sessionManager.pauseSession()
                    } else if sessionManager.canResumeSession() {
                        sessionManager.resumeSession()
                    }
                }) {
                    Image(systemName: sessionManager.sessionState == .active ? "pause.fill" : "play.fill")
                        .font(.title)
                        .foregroundColor(.textPrimary)
                        .frame(width: 60, height: 60)
                        .background(Color.primaryTeal.opacity(0.2))
                        .clipShape(Circle())
                }
                .disabled(sessionManager.sessionState == .completed)
                
                // Stop button
                Button(action: {
                    showingExitConfirmation = true
                }) {
                    Image(systemName: "stop.fill")
                        .font(.title)
                        .foregroundColor(.error)
                        .frame(width: 60, height: 60)
                        .background(Color.error.opacity(0.2))
                        .clipShape(Circle())
                }
                .disabled(sessionManager.sessionState == .completed)
            }
        }
        .padding(.horizontal, 30)
    }
    
    // MARK: - Methods
    
    private func setupSession() {
        selectedBackgroundSound = sessionManager.backgroundSound
        soundVolume = sessionManager.soundVolume
    }
    
    private func startMeditation() {
        guard let user = authManager.currentUser else { return }
        
        sessionManager.setBackgroundSound(selectedBackgroundSound)
        sessionManager.setSoundVolume(soundVolume)
        sessionManager.startSession(
            exercise: exercise,
            duration: TimeInterval(selectedDuration),
            user: user
        )
    }
}

// MARK: - Meditation Breathing Circle

struct MeditationBreathingCircle: View {
    let isAnimating: Bool
    let phase: MeditationPhase
    
    @State private var scale: CGFloat = 1.0
    @State private var opacity: Double = 0.6
    
    var body: some View {
        ZStack {
            // Outer glow
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            .primaryTeal.opacity(opacity * 0.3),
                            .primaryPurple.opacity(opacity * 0.1),
                            .clear
                        ],
                        center: .center,
                        startRadius: 50,
                        endRadius: 100
                    )
                )
                .scaleEffect(scale * 1.2)
            
            // Main circle
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            .primaryTeal.opacity(opacity),
                            .primaryPurple.opacity(opacity * 0.7)
                        ],
                        center: .center,
                        startRadius: 20,
                        endRadius: 80
                    )
                )
                .scaleEffect(scale)
        }
        .onChange(of: isAnimating) { oldValue, animating in
            if animating {
                startBreathingAnimation()
            } else {
                stopBreathingAnimation()
            }
        }
        .onChange(of: phase) { oldValue, newPhase in
            updateAnimationForPhase(newPhase)
        }
    }
    
    private func startBreathingAnimation() {
        withAnimation(.easeInOut(duration: 4.0).repeatForever(autoreverses: true)) {
            scale = 1.3
            opacity = 0.8
        }
    }
    
    private func stopBreathingAnimation() {
        withAnimation(.easeInOut(duration: 1.0)) {
            scale = 1.0
            opacity = 0.6
        }
    }
    
    private func updateAnimationForPhase(_ phase: MeditationPhase) {
        let duration: Double
        let maxScale: CGFloat
        let maxOpacity: Double
        
        switch phase {
        case .preparation:
            duration = 6.0
            maxScale = 1.2
            maxOpacity = 0.7
        case .settling:
            duration = 5.0
            maxScale = 1.25
            maxOpacity = 0.75
        case .practice:
            duration = 4.0
            maxScale = 1.3
            maxOpacity = 0.8
        case .deepening:
            duration = 3.5
            maxScale = 1.35
            maxOpacity = 0.85
        case .completion:
            duration = 5.0
            maxScale = 1.2
            maxOpacity = 0.7
        }
        
        if isAnimating {
            withAnimation(.easeInOut(duration: duration).repeatForever(autoreverses: true)) {
                scale = maxScale
                opacity = maxOpacity
            }
        }
    }
}

// MARK: - Meditation Settings Sheet

struct MeditationSettingsSheet: View {
    @Binding var backgroundSound: BackgroundSound
    @Binding var soundVolume: Float
    let onSoundChange: (BackgroundSound) -> Void
    let onVolumeChange: (Float) -> Void
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Background Sound Selection
                VStack(alignment: .leading, spacing: 16) {
                    Text("Background Sound")
                        .font(.headline)
                        .foregroundColor(.textPrimary)

                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                        ForEach(BackgroundSound.allCases, id: \.self) { sound in
                            Button(action: {
                                backgroundSound = sound
                                onSoundChange(sound)
                            }) {
                                VStack(spacing: 8) {
                                    Image(systemName: sound.iconName)
                                        .font(.title2)
                                        .foregroundColor(backgroundSound == sound ? .primaryTeal : .textSecondary)

                                    Text(sound.displayName)
                                        .font(.caption)
                                        .foregroundColor(backgroundSound == sound ? .primaryTeal : .textSecondary)
                                }
                                .frame(height: 60)
                                .frame(maxWidth: .infinity)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(backgroundSound == sound ? Color.primaryTeal.opacity(0.2) : Color.cardBackground)
                                )
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(backgroundSound == sound ? Color.primaryTeal : Color.clear, lineWidth: 2)
                                )
                            }
                        }
                    }
                }

                // Volume Control
                if backgroundSound != .none {
                    VStack(alignment: .leading, spacing: 16) {
                        Text("Volume")
                            .font(.headline)
                            .foregroundColor(.textPrimary)

                        HStack {
                            Image(systemName: "speaker.fill")
                                .foregroundColor(.textSecondary)

                            Slider(value: $soundVolume, in: 0...1) { _ in
                                onVolumeChange(soundVolume)
                            }
                            .accentColor(.primaryTeal)

                            Image(systemName: "speaker.wave.3.fill")
                                .foregroundColor(.textSecondary)
                        }

                        Text("\(Int(soundVolume * 100))%")
                            .font(.caption)
                            .foregroundColor(.textSecondary)
                    }
                }

                Spacer()
            }
            .padding(20)
            .background(Color.appBackground)
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(.primaryTeal)
                }
            }
        }
        .preferredColorScheme(.dark)
    }
}

// MARK: - Meditation Completion Sheet

struct MeditationCompletionSheet: View {
    @ObservedObject var sessionManager: MeditationSessionManager
    let onDismiss: () -> Void

    @State private var rating: Int = 0
    @State private var focusLevel: Int = 5
    @State private var calmLevel: Int = 5
    @State private var notes: String = ""
    @State private var insights: String = ""
    @State private var showingCelebration = true

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 30) {
                    // Celebration Animation
                    if showingCelebration {
                        celebrationView
                    }

                    // Session Summary
                    sessionSummaryView

                    // Feedback Form
                    feedbackFormView

                    // Action Buttons
                    actionButtonsView
                }
                .padding(20)
            }
            .background(Color.appBackground)
            .navigationTitle("Session Complete")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
        }
        .preferredColorScheme(.dark)
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                withAnimation(.easeOut(duration: 0.5)) {
                    showingCelebration = false
                }
            }
        }
    }

    private var celebrationView: some View {
        VStack(spacing: 20) {
            // Success icon with animation
            ZStack {
                Circle()
                    .fill(Color.success.opacity(0.2))
                    .frame(width: 120, height: 120)

                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.success)
            }
            .scaleEffect(showingCelebration ? 1.2 : 1.0)
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: showingCelebration)

            Text("Well Done!")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.textPrimary)

            Text("You've completed your meditation session")
                .font(.body)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)
        }
        .transition(.scale.combined(with: .opacity))
    }

    private var sessionSummaryView: some View {
        SerenityCard {
            VStack(spacing: 16) {
                Text("Session Summary")
                    .font(.headline)
                    .foregroundColor(.textPrimary)

                HStack(spacing: 30) {
                    VStack {
                        Text(sessionManager.getFormattedTimeElapsed())
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primaryTeal)
                        Text("Duration")
                            .font(.caption)
                            .foregroundColor(.textSecondary)
                    }

                    VStack {
                        Text("\(Int(sessionManager.getSessionProgress()))%")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primaryPurple)
                        Text("Completed")
                            .font(.caption)
                            .foregroundColor(.textSecondary)
                    }

                    if let exercise = sessionManager.currentSession?.exercise {
                        VStack {
                            Text(exercise.meditationCategory.displayName)
                                .font(.title3)
                                .fontWeight(.medium)
                                .foregroundColor(.textPrimary)
                            Text("Type")
                                .font(.caption)
                                .foregroundColor(.textSecondary)
                        }
                    }
                }
            }
        }
    }

    private var feedbackFormView: some View {
        VStack(spacing: 24) {
            // Rating
            VStack(alignment: .leading, spacing: 12) {
                Text("How was your session?")
                    .font(.headline)
                    .foregroundColor(.textPrimary)

                HStack(spacing: 8) {
                    ForEach(1...5, id: \.self) { star in
                        Button(action: {
                            rating = star
                        }) {
                            Image(systemName: star <= rating ? "star.fill" : "star")
                                .font(.title2)
                                .foregroundColor(star <= rating ? .warning : .textSecondary)
                        }
                    }
                }
            }

            // Focus Level
            VStack(alignment: .leading, spacing: 12) {
                Text("Focus Level (1-10)")
                    .font(.headline)
                    .foregroundColor(.textPrimary)

                Slider(value: Binding(
                    get: { Double(focusLevel) },
                    set: { focusLevel = Int($0) }
                ), in: 1...10, step: 1)
                .accentColor(.primaryTeal)

                Text("\(focusLevel)")
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }

            // Calm Level
            VStack(alignment: .leading, spacing: 12) {
                Text("Calm Level (1-10)")
                    .font(.headline)
                    .foregroundColor(.textPrimary)

                Slider(value: Binding(
                    get: { Double(calmLevel) },
                    set: { calmLevel = Int($0) }
                ), in: 1...10, step: 1)
                .accentColor(.primaryPurple)

                Text("\(calmLevel)")
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }

            // Notes
            VStack(alignment: .leading, spacing: 12) {
                Text("Notes (Optional)")
                    .font(.headline)
                    .foregroundColor(.textPrimary)

                SerenityTextField(
                    title: "",
                    placeholder: "How did you feel during the session?",
                    text: $notes,
                    style: .standard
                )
            }
        }
    }

    private var actionButtonsView: some View {
        VStack(spacing: 16) {
            SerenityButton(
                title: "Save & Continue",
                style: .primary,
                size: .large,
                action: {
                    sessionManager.submitFeedback(
                        rating: rating,
                        focusLevel: focusLevel,
                        calmLevel: calmLevel,
                        notes: notes.isEmpty ? nil : notes,
                        insights: insights.isEmpty ? nil : insights
                    )
                    onDismiss()
                }
            )

            Button("Skip Feedback") {
                sessionManager.submitFeedback(rating: 0, focusLevel: 0, calmLevel: 0, notes: nil, insights: nil)
                onDismiss()
            }
            .foregroundColor(.textSecondary)
        }
    }
}

// MARK: - Preview

#Preview {
    let sampleExercise = MeditationExercise()
    sampleExercise.exerciseId = "sample"
    sampleExercise.name = "Mindful Breathing"
    sampleExercise.localizedName = "Mindful Breathing"
    sampleExercise.category = MeditationCategory.mindfulness.rawValue
    sampleExercise.difficulty = MeditationDifficulty.beginner.rawValue

    return NavigationView {
        MeditationPracticeScreen(exercise: sampleExercise, selectedDuration: 600)
            .environmentObject(AuthenticationManager())
    }
}
