//
//  ContentView.swift
//  Serenity07
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/7/17.
//  Updated by Serenity Team on 2025/7/18.
//

import SwiftUI

struct ContentView: View {

    @EnvironmentObject var coreDataManager: CoreDataManager
    @State private var isLoading = true
    @State private var showOnboarding = false

    var body: some View {
        Group {
            if isLoading {
                LaunchScreenView()
            } else if showOnboarding {
                OnboardingView {
                    showOnboarding = false
                }
            } else {
                TabBarView()
            }
        }
        .onAppear {
            checkAppState()
        }
    }

    private func checkAppState() {
        // Simulate loading time and check if user needs onboarding
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            let user = coreDataManager.getCurrentUser()
            showOnboarding = user == nil
            isLoading = false
        }
    }
}

// MARK: - Launch Screen

struct LaunchScreenView: View {
    @State private var animateGradient = false

    var body: some View {
        ZStack {
            // Background gradient
            LinearGradient(
                colors: [Color.primaryBlue, Color.primaryPurple],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()

            VStack(spacing: Spacing.xl) {
                // App logo/icon
                ZStack {
                    Circle()
                        .fill(Color.primaryTeal.opacity(0.2))
                        .frame(width: 120, height: 120)
                        .scaleEffect(animateGradient ? 1.1 : 1.0)

                    Image(systemName: "wind")
                        .font(.system(size: 48, weight: .light))
                        .foregroundColor(.primaryTeal)
                }
                .animation(
                    .easeInOut(duration: 2.0).repeatForever(autoreverses: true),
                    value: animateGradient
                )

                // App name
                VStack(spacing: Spacing.xs) {
                    Text("静息")
                        .font(.system(size: 36, weight: .light, design: .serif))
                        .foregroundColor(.white)

                    Text("Serenity")
                        .font(.system(size: 24, weight: .light))
                        .foregroundColor(.primaryTeal)
                        .opacity(0.8)
                }

                // Loading indicator
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .primaryTeal))
                    .scaleEffect(1.2)
            }
        }
        .onAppear {
            animateGradient = true
        }
    }
}

// MARK: - Onboarding View

struct OnboardingView: View {
    let onComplete: () -> Void
    @State private var currentPage = 0

    private let pages = [
        OnboardingPage(
            title: "Welcome to Serenity",
            subtitle: "Your personal breathing coach",
            description: "Learn scientifically-proven breathing techniques to reduce stress, improve sleep, and enhance focus.",
            imageName: "wind",
            color: .primaryTeal
        ),
        OnboardingPage(
            title: "Guided Breathing",
            subtitle: "Visual and audio guidance",
            description: "Follow our beautiful animations and soothing sounds to master different breathing patterns.",
            imageName: "heart.fill",
            color: .primaryPurple
        ),
        OnboardingPage(
            title: "Track Your Progress",
            subtitle: "Build healthy habits",
            description: "Monitor your practice sessions, build streaks, and see your improvement over time.",
            imageName: "chart.line.uptrend.xyaxis",
            color: .success
        )
    ]

    var body: some View {
        ZStack {
            Color.appBackground.ignoresSafeArea()

            VStack(spacing: Spacing.xl) {
                // Page content
                TabView(selection: $currentPage) {
                    ForEach(0..<pages.count, id: \.self) { index in
                        OnboardingPageView(page: pages[index])
                            .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .frame(maxHeight: 500)

                // Page indicator
                HStack(spacing: Spacing.xs) {
                    ForEach(0..<pages.count, id: \.self) { index in
                        Circle()
                            .fill(currentPage == index ? Color.primaryTeal : Color.mediumGray)
                            .frame(width: 8, height: 8)
                            .animation(.easeInOut, value: currentPage)
                    }
                }

                // Navigation buttons
                VStack(spacing: Spacing.md) {
                    if currentPage < pages.count - 1 {
                        SerenityButton.primary("Continue") {
                            withAnimation {
                                currentPage += 1
                            }
                        }

                        SerenityButton.ghost("Skip") {
                            onComplete()
                        }
                    } else {
                        SerenityButton.primary("Get Started") {
                            onComplete()
                        }
                    }
                }
                .containerPadding()
            }
        }
    }
}

// MARK: - Onboarding Models

struct OnboardingPage {
    let title: String
    let subtitle: String
    let description: String
    let imageName: String
    let color: Color
}

struct OnboardingPageView: View {
    let page: OnboardingPage

    var body: some View {
        VStack(spacing: Spacing.xl) {
            // Icon
            Image(systemName: page.imageName)
                .font(.system(size: 80, weight: .light))
                .foregroundColor(page.color)
                .frame(height: 120)

            // Content
            VStack(spacing: Spacing.md) {
                Text(page.title)
                    .textStyle(.primaryHeading)
                    .multilineTextAlignment(.center)

                Text(page.subtitle)
                    .textStyle(.cardTitle)
                    .foregroundColor(page.color)
                    .multilineTextAlignment(.center)

                Text(page.description)
                    .textStyle(.primaryBody)
                    .multilineTextAlignment(.center)
                    .opacity(0.8)
            }
            .containerPadding()
        }
    }
}

// MARK: - Preview

#Preview {
    ContentView()
        .environmentObject(CoreDataManager.shared)
}
