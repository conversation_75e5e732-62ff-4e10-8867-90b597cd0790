//
//  Serenity07App.swift
//  Serenity07
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/7/17.
//  Updated by Serenity Team on 2025/7/18.
//

import SwiftUI

@main
struct Serenity07App: App {

    // MARK: - Core Data Manager

    let coreDataManager = CoreDataManager.shared

    // MARK: - App Body

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(coreDataManager)
                .preferredColorScheme(.dark) // Force dark mode for now
                .onAppear {
                    setupAppAppearance()
                }
        }
    }

    // MARK: - App Setup

    private func setupAppAppearance() {
        // Configure navigation bar appearance
        let navigationBarAppearance = UINavigationBarAppearance()
        navigationBarAppearance.configureWithTransparentBackground()
        navigationBarAppearance.backgroundColor = UIColor.primaryBlue
        navigationBarAppearance.titleTextAttributes = [
            .foregroundColor: UIColor.white,
            .font: UIFont.systemFont(ofSize: 20, weight: .bold)
        ]
        navigationBarAppearance.largeTitleTextAttributes = [
            .foregroundColor: UIColor.white,
            .font: UIFont.systemFont(ofSize: 32, weight: .bold)
        ]

        UINavigationBar.appearance().standardAppearance = navigationBarAppearance
        UINavigationBar.appearance().compactAppearance = navigationBarAppearance
        UINavigationBar.appearance().scrollEdgeAppearance = navigationBarAppearance

        // Configure status bar
        UIApplication.shared.statusBarStyle = .lightContent
    }
}
