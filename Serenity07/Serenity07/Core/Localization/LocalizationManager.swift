//
//  LocalizationManager.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import SwiftUI

/// Localization manager for handling multi-language support
class LocalizationManager: ObservableObject {
    
    static let shared = LocalizationManager()
    
    // MARK: - Properties
    
    @Published var currentLanguage: SupportedLanguage {
        didSet {
            UserDefaults.standard.set(currentLanguage.code, forKey: "selected_language")
            updateBundle()
        }
    }
    
    private var bundle: Bundle = Bundle.main
    
    // MARK: - Supported Languages
    
    enum SupportedLanguage: String, CaseIterable {
        case english = "en"
        case simplifiedChinese = "zh-Hans"
        case traditionalChinese = "zh-Hant"
        
        var code: String {
            return self.rawValue
        }
        
        var displayName: String {
            switch self {
            case .english:
                return "English"
            case .simplifiedChinese:
                return "简体中文"
            case .traditionalChinese:
                return "繁體中文"
            }
        }
        
        var nativeName: String {
            switch self {
            case .english:
                return "English"
            case .simplifiedChinese:
                return "简体中文"
            case .traditionalChinese:
                return "繁體中文"
            }
        }
        
        var flag: String {
            switch self {
            case .english:
                return "🇺🇸"
            case .simplifiedChinese:
                return "🇨🇳"
            case .traditionalChinese:
                return "🇹🇼"
            }
        }
    }
    
    // MARK: - Initialization
    
    private init() {
        // Get saved language or use system default
        let savedLanguageCode = UserDefaults.standard.string(forKey: "selected_language")
        
        if let savedCode = savedLanguageCode,
           let savedLanguage = SupportedLanguage(rawValue: savedCode) {
            self.currentLanguage = savedLanguage
        } else {
            // Determine system language
            let systemLanguage = Locale.current.languageCode ?? "en"
            self.currentLanguage = SupportedLanguage(rawValue: systemLanguage) ?? .english
        }
        
        updateBundle()
    }
    
    // MARK: - Language Management
    
    func setLanguage(_ language: SupportedLanguage) {
        currentLanguage = language
    }
    
    private func updateBundle() {
        guard let path = Bundle.main.path(forResource: currentLanguage.code, ofType: "lproj"),
              let bundle = Bundle(path: path) else {
            self.bundle = Bundle.main
            return
        }
        self.bundle = bundle
    }
    
    // MARK: - Localization Methods
    
    func localizedString(_ key: String, comment: String = "") -> String {
        return NSLocalizedString(key, bundle: bundle, comment: comment)
    }
    
    func localizedString(_ key: LocalizationKey) -> String {
        return localizedString(key.rawValue, comment: key.comment)
    }
    
    // MARK: - Formatted Strings
    
    func localizedStringWithFormat(_ key: String, _ arguments: CVarArg...) -> String {
        let format = localizedString(key)
        return String(format: format, arguments: arguments)
    }
    
    func localizedStringWithFormat(_ key: LocalizationKey, _ arguments: CVarArg...) -> String {
        let format = localizedString(key)
        return String(format: format, arguments: arguments)
    }
}

// MARK: - Localization Keys

enum LocalizationKey: String, CaseIterable {
    
    // MARK: - General
    case appName = "app_name"
    case ok = "ok"
    case cancel = "cancel"
    case done = "done"
    case save = "save"
    case delete = "delete"
    case edit = "edit"
    case close = "close"
    case next = "next"
    case previous = "previous"
    case skip = "skip"
    case continue = "continue"
    case getStarted = "get_started"
    case loading = "loading"
    case error = "error"
    case success = "success"
    case warning = "warning"
    case info = "info"
    
    // MARK: - Navigation
    case home = "home"
    case breathing = "breathing"
    case meditation = "meditation"
    case sleep = "sleep"
    case profile = "profile"
    case settings = "settings"
    
    // MARK: - Onboarding
    case welcomeTitle = "welcome_title"
    case welcomeSubtitle = "welcome_subtitle"
    case welcomeDescription = "welcome_description"
    case guidedBreathingTitle = "guided_breathing_title"
    case guidedBreathingSubtitle = "guided_breathing_subtitle"
    case guidedBreathingDescription = "guided_breathing_description"
    case trackProgressTitle = "track_progress_title"
    case trackProgressSubtitle = "track_progress_subtitle"
    case trackProgressDescription = "track_progress_description"
    
    // MARK: - Home Screen
    case welcomeBack = "welcome_back"
    case readyForPractice = "ready_for_practice"
    case quickBreathing = "quick_breathing"
    case sleepPreparation = "sleep_preparation"
    case currentStreak = "current_streak"
    case todayTime = "today_time"
    case totalTime = "total_time"
    case days = "days"
    case minutes = "minutes"
    
    // MARK: - Breathing Exercises
    case breathingExercises = "breathing_exercises"
    case chooseYourPractice = "choose_your_practice"
    case startPractice = "start_practice"
    case pausePractice = "pause_practice"
    case stopPractice = "stop_practice"
    case completePractice = "complete_practice"
    case inhale = "inhale"
    case exhale = "exhale"
    case hold = "hold"
    case pause = "pause"
    case breatheIn = "breathe_in"
    case breatheOut = "breathe_out"
    case holdBreath = "hold_breath"
    case pauseBriefly = "pause_briefly"
    
    // MARK: - Exercise Types
    case equalBreathing = "equal_breathing"
    case boxBreathing = "box_breathing"
    case fourSevenEightBreathing = "four_seven_eight_breathing"
    case alternateNostrilBreathing = "alternate_nostril_breathing"
    case diaphragmaticBreathing = "diaphragmatic_breathing"
    
    // MARK: - Categories
    case general = "general"
    case stressRelief = "stress_relief"
    case sleepCategory = "sleep_category"
    case focus = "focus"
    case energy = "energy"
    case anxiety = "anxiety"
    case meditationCategory = "meditation_category"
    
    // MARK: - Difficulty Levels
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    
    // MARK: - Breathing Speeds
    case slow = "slow"
    case normal = "normal"
    case fast = "fast"
    
    // MARK: - Session Status
    case incomplete = "incomplete"
    case completed = "completed"
    case cancelled = "cancelled"
    case paused = "paused"
    
    // MARK: - Statistics
    case statistics = "statistics"
    case streak = "streak"
    case totalSessions = "total_sessions"
    case averageRating = "average_rating"
    case completionRate = "completion_rate"
    case longestStreak = "longest_streak"
    
    // MARK: - Personal Plans
    case personalPlan = "personal_plan"
    case createPlan = "create_plan"
    case planGoal = "plan_goal"
    case planDuration = "plan_duration"
    case dailyGoal = "daily_goal"
    case planProgress = "plan_progress"
    
    // MARK: - Settings
    case language = "language"
    case notifications = "notifications"
    case soundSettings = "sound_settings"
    case hapticFeedback = "haptic_feedback"
    case darkMode = "dark_mode"
    case autoPlay = "auto_play"
    case backgroundVolume = "background_volume"
    
    // MARK: - Subscription
    case premium = "premium"
    case subscribe = "subscribe"
    case freeTrial = "free_trial"
    case monthlyPlan = "monthly_plan"
    case yearlyPlan = "yearly_plan"
    case lifetimePlan = "lifetime_plan"
    case restorePurchases = "restore_purchases"
    
    // MARK: - Error Messages
    case networkError = "network_error"
    case dataError = "data_error"
    case permissionError = "permission_error"
    case subscriptionError = "subscription_error"
    
    var comment: String {
        switch self {
        case .appName:
            return "Application name"
        case .welcomeTitle:
            return "Welcome screen title"
        case .breathingExercises:
            return "Breathing exercises section title"
        case .currentStreak:
            return "Current practice streak"
        case .equalBreathing:
            return "Equal breathing exercise name"
        case .stressRelief:
            return "Stress relief category"
        case .beginner:
            return "Beginner difficulty level"
        case .statistics:
            return "Statistics section title"
        case .personalPlan:
            return "Personal plan feature"
        case .premium:
            return "Premium subscription"
        case .networkError:
            return "Network connection error"
        default:
            return "Localized string"
        }
    }
}

// MARK: - SwiftUI Extensions

extension Text {
    init(_ key: LocalizationKey) {
        self.init(LocalizationManager.shared.localizedString(key))
    }
    
    init(_ key: LocalizationKey, _ arguments: CVarArg...) {
        let format = LocalizationManager.shared.localizedString(key)
        self.init(String(format: format, arguments: arguments))
    }
}

extension String {
    init(_ key: LocalizationKey) {
        self = LocalizationManager.shared.localizedString(key)
    }
    
    static func localized(_ key: LocalizationKey, _ arguments: CVarArg...) -> String {
        return LocalizationManager.shared.localizedStringWithFormat(key, arguments)
    }
}

// MARK: - View Modifier for Language Changes

struct LocalizedView: ViewModifier {
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    func body(content: Content) -> some View {
        content
            .environment(\.locale, Locale(identifier: localizationManager.currentLanguage.code))
            .id(localizationManager.currentLanguage.code) // Force view refresh on language change
    }
}

extension View {
    func localized() -> some View {
        modifier(LocalizedView())
    }
}

// MARK: - Language Picker Component

struct LanguagePickerView: View {
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text(LocalizationKey.language)
                .textStyle(.cardTitle)
            
            ForEach(LocalizationManager.SupportedLanguage.allCases, id: \.self) { language in
                Button(action: {
                    localizationManager.setLanguage(language)
                }) {
                    HStack {
                        Text(language.flag)
                            .font(.title2)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text(language.displayName)
                                .textStyle(.primaryBody)
                                .foregroundColor(.primaryText)
                            
                            Text(language.nativeName)
                                .textStyle(.caption)
                                .foregroundColor(.secondaryText)
                        }
                        
                        Spacer()
                        
                        if language == localizationManager.currentLanguage {
                            Image(systemName: "checkmark")
                                .foregroundColor(.primaryTeal)
                        }
                    }
                    .padding(.vertical, Spacing.sm)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
}
