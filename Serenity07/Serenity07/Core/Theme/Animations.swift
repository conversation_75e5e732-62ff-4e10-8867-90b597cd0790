//
//  Animations.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

/// Animation system for consistent motion design throughout the app
struct SerenityAnimations {
    
    // MARK: - Duration Constants
    
    /// Quick animations (0.15s) - for button presses, small state changes
    static let quick: Double = 0.15
    
    /// Standard animations (0.3s) - for most UI transitions
    static let standard: Double = 0.3
    
    /// Slow animations (0.5s) - for major state changes, page transitions
    static let slow: Double = 0.5
    
    /// Breathing cycle duration (8s) - for breathing animations
    static let breathingCycle: Double = 8.0
    
    // MARK: - Easing Functions
    
    /// Quick ease in-out for button interactions
    static let quickEase = Animation.easeInOut(duration: quick)
    
    /// Standard ease in-out for general transitions
    static let standardEase = Animation.easeInOut(duration: standard)
    
    /// Slow ease in-out for major transitions
    static let slowEase = Animation.easeInOut(duration: slow)
    
    /// Spring animation for bouncy effects
    static let spring = Animation.spring(response: 0.5, dampingFraction: 0.8, blendDuration: 0)
    
    /// Gentle spring for subtle bounces
    static let gentleSpring = Animation.spring(response: 0.3, dampingFraction: 0.9, blendDuration: 0)
    
    /// Breathing animation with custom timing
    static let breathing = Animation.easeInOut(duration: breathingCycle / 4)
    
    // MARK: - Transition Animations
    
    /// Fade transition
    static let fade = AnyTransition.opacity.animation(standardEase)
    
    /// Slide from bottom
    static let slideUp = AnyTransition.move(edge: .bottom).animation(standardEase)
    
    /// Slide from top
    static let slideDown = AnyTransition.move(edge: .top).animation(standardEase)
    
    /// Slide from right
    static let slideLeft = AnyTransition.move(edge: .trailing).animation(standardEase)
    
    /// Slide from left
    static let slideRight = AnyTransition.move(edge: .leading).animation(standardEase)
    
    /// Scale transition
    static let scale = AnyTransition.scale.animation(standardEase)
    
    /// Combined scale and fade
    static let scaleAndFade = AnyTransition.scale.combined(with: .opacity).animation(standardEase)
    
    /// Modal presentation transition
    static let modal = AnyTransition.asymmetric(
        insertion: .move(edge: .bottom).combined(with: .opacity),
        removal: .move(edge: .bottom).combined(with: .opacity)
    ).animation(slowEase)
}

// MARK: - View Modifiers for Animations

/// Breathing animation modifier
struct BreathingAnimation: ViewModifier {
    let isAnimating: Bool
    let speed: BreathingSpeed
    
    @State private var scale: CGFloat = 1.0
    @State private var opacity: Double = 0.6
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(scale)
            .opacity(opacity)
            .onAppear {
                if isAnimating {
                    startBreathingAnimation()
                }
            }
            .onChange(of: isAnimating) { animating in
                if animating {
                    startBreathingAnimation()
                } else {
                    stopBreathingAnimation()
                }
            }
    }
    
    private func startBreathingAnimation() {
        let duration = SerenityAnimations.breathingCycle * speed.multiplier
        
        withAnimation(.easeInOut(duration: duration / 2).repeatForever(autoreverses: true)) {
            scale = 1.3
            opacity = 1.0
        }
    }
    
    private func stopBreathingAnimation() {
        withAnimation(SerenityAnimations.standardEase) {
            scale = 1.0
            opacity = 0.6
        }
    }
}

/// Pulse animation modifier
struct PulseAnimation: ViewModifier {
    let isAnimating: Bool
    let duration: Double
    let minScale: CGFloat
    let maxScale: CGFloat
    
    @State private var scale: CGFloat = 1.0
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(scale)
            .onAppear {
                if isAnimating {
                    startPulse()
                }
            }
            .onChange(of: isAnimating) { animating in
                if animating {
                    startPulse()
                } else {
                    stopPulse()
                }
            }
    }
    
    private func startPulse() {
        withAnimation(.easeInOut(duration: duration).repeatForever(autoreverses: true)) {
            scale = maxScale
        }
    }
    
    private func stopPulse() {
        withAnimation(SerenityAnimations.standardEase) {
            scale = minScale
        }
    }
}

/// Shake animation modifier
struct ShakeAnimation: ViewModifier {
    let trigger: Bool
    
    @State private var offset: CGFloat = 0
    
    func body(content: Content) -> some View {
        content
            .offset(x: offset)
            .onChange(of: trigger) { _ in
                shake()
            }
    }
    
    private func shake() {
        let animation = Animation.easeInOut(duration: 0.1)
        
        withAnimation(animation) {
            offset = -5
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(animation) {
                offset = 5
            }
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(animation) {
                offset = -3
            }
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            withAnimation(animation) {
                offset = 3
            }
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(animation) {
                offset = 0
            }
        }
    }
}

/// Floating animation modifier
struct FloatingAnimation: ViewModifier {
    let isAnimating: Bool
    
    @State private var yOffset: CGFloat = 0
    
    func body(content: Content) -> some View {
        content
            .offset(y: yOffset)
            .onAppear {
                if isAnimating {
                    startFloating()
                }
            }
            .onChange(of: isAnimating) { animating in
                if animating {
                    startFloating()
                } else {
                    stopFloating()
                }
            }
    }
    
    private func startFloating() {
        withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
            yOffset = -10
        }
    }
    
    private func stopFloating() {
        withAnimation(SerenityAnimations.standardEase) {
            yOffset = 0
        }
    }
}

/// Shimmer loading animation modifier
struct ShimmerAnimation: ViewModifier {
    let isAnimating: Bool
    
    @State private var phase: CGFloat = 0
    
    func body(content: Content) -> some View {
        content
            .overlay(
                Rectangle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.clear,
                                Color.white.opacity(0.3),
                                Color.clear
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .rotationEffect(.degrees(30))
                    .offset(x: phase)
                    .opacity(isAnimating ? 1 : 0)
            )
            .clipped()
            .onAppear {
                if isAnimating {
                    startShimmer()
                }
            }
            .onChange(of: isAnimating) { animating in
                if animating {
                    startShimmer()
                } else {
                    stopShimmer()
                }
            }
    }
    
    private func startShimmer() {
        withAnimation(.linear(duration: 1.5).repeatForever(autoreverses: false)) {
            phase = 300
        }
    }
    
    private func stopShimmer() {
        withAnimation(SerenityAnimations.standardEase) {
            phase = 0
        }
    }
}

// MARK: - View Extensions

extension View {
    
    /// Apply breathing animation
    func breathingAnimation(isAnimating: Bool, speed: BreathingSpeed = .normal) -> some View {
        modifier(BreathingAnimation(isAnimating: isAnimating, speed: speed))
    }
    
    /// Apply pulse animation
    func pulseAnimation(
        isAnimating: Bool,
        duration: Double = 1.0,
        minScale: CGFloat = 1.0,
        maxScale: CGFloat = 1.1
    ) -> some View {
        modifier(PulseAnimation(
            isAnimating: isAnimating,
            duration: duration,
            minScale: minScale,
            maxScale: maxScale
        ))
    }
    
    /// Apply shake animation
    func shakeAnimation(trigger: Bool) -> some View {
        modifier(ShakeAnimation(trigger: trigger))
    }
    
    /// Apply floating animation
    func floatingAnimation(isAnimating: Bool) -> some View {
        modifier(FloatingAnimation(isAnimating: isAnimating))
    }
    
    /// Apply shimmer loading animation
    func shimmerAnimation(isAnimating: Bool) -> some View {
        modifier(ShimmerAnimation(isAnimating: isAnimating))
    }
    
    /// Apply button press animation
    func buttonPressAnimation() -> some View {
        scaleEffect(1.0)
        .animation(SerenityAnimations.quickEase, value: UUID())
    }
    
    /// Apply card hover animation
    func cardHoverAnimation() -> some View {
        scaleEffect(1.0)
        .animation(SerenityAnimations.gentleSpring, value: UUID())
    }
    
    /// Apply fade in animation
    func fadeIn(delay: Double = 0) -> some View {
        opacity(0)
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                withAnimation(SerenityAnimations.standardEase) {
                    // This would need to be implemented with state management
                }
            }
        }
    }
    
    /// Apply slide in animation
    func slideIn(from edge: Edge, delay: Double = 0) -> some View {
        let offset: CGSize
        switch edge {
        case .top:
            offset = CGSize(width: 0, height: -100)
        case .bottom:
            offset = CGSize(width: 0, height: 100)
        case .leading:
            offset = CGSize(width: -100, height: 0)
        case .trailing:
            offset = CGSize(width: 100, height: 0)
        }
        
        return self
            .offset(offset)
            .onAppear {
                DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                    withAnimation(SerenityAnimations.standardEase) {
                        // This would need to be implemented with state management
                    }
                }
            }
    }
}

// MARK: - Animated Components

/// Animated checkmark
struct AnimatedCheckmark: View {
    let isVisible: Bool
    let color: Color
    let size: CGFloat
    
    @State private var trimEnd: CGFloat = 0
    
    var body: some View {
        Path { path in
            path.move(to: CGPoint(x: size * 0.2, y: size * 0.5))
            path.addLine(to: CGPoint(x: size * 0.45, y: size * 0.75))
            path.addLine(to: CGPoint(x: size * 0.8, y: size * 0.25))
        }
        .trim(from: 0, to: trimEnd)
        .stroke(color, style: StrokeStyle(lineWidth: 3, lineCap: .round, lineJoin: .round))
        .frame(width: size, height: size)
        .onChange(of: isVisible) { visible in
            if visible {
                withAnimation(.easeInOut(duration: 0.5)) {
                    trimEnd = 1.0
                }
            } else {
                trimEnd = 0
            }
        }
    }
}

/// Animated progress ring
struct AnimatedProgressRing: View {
    let progress: Double
    let color: Color
    let lineWidth: CGFloat
    let size: CGFloat
    
    @State private var animatedProgress: Double = 0
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(color.opacity(0.2), lineWidth: lineWidth)
            
            Circle()
                .trim(from: 0, to: animatedProgress)
                .stroke(
                    color,
                    style: StrokeStyle(lineWidth: lineWidth, lineCap: .round)
                )
                .rotationEffect(.degrees(-90))
        }
        .frame(width: size, height: size)
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0)) {
                animatedProgress = progress
            }
        }
        .onChange(of: progress) { newProgress in
            withAnimation(.easeInOut(duration: 0.5)) {
                animatedProgress = newProgress
            }
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: Spacing.xl) {
        // Breathing animation
        Circle()
            .fill(Color.primaryTeal.opacity(0.3))
            .frame(width: 100, height: 100)
            .breathingAnimation(isAnimating: true)
        
        // Pulse animation
        RoundedRectangle(cornerRadius: CornerRadius.medium)
            .fill(Color.primaryPurple)
            .frame(width: 80, height: 80)
            .pulseAnimation(isAnimating: true)
        
        // Floating animation
        Image(systemName: "leaf.fill")
            .font(.system(size: 40))
            .foregroundColor(.success)
            .floatingAnimation(isAnimating: true)
        
        // Animated checkmark
        AnimatedCheckmark(isVisible: true, color: .success, size: 40)
        
        // Animated progress ring
        AnimatedProgressRing(
            progress: 0.75,
            color: .primaryTeal,
            lineWidth: 8,
            size: 80
        )
    }
    .padding()
    .background(Color.appBackground)
}
