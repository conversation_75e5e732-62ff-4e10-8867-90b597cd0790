//
//  Typography.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

/// Typography system based on design specifications
extension Font {
    
    // MARK: - Title Hierarchy
    
    /// H1 Large title - 32pt, Bold - Used for page main titles
    static let h1 = Font.system(size: 32, weight: .bold, design: .default)
    
    /// H2 Medium title - 24pt, Bold - Used for section titles
    static let h2 = Font.system(size: 24, weight: .bold, design: .default)
    
    /// H3 Small title - 20pt, Medium - Used for card titles
    static let h3 = Font.system(size: 20, weight: .medium, design: .default)
    
    /// H4 Subtitle - 18pt, Medium - Used for list item titles
    static let h4 = Font.system(size: 18, weight: .medium, design: .default)
    
    // MARK: - Body Text Hierarchy
    
    /// Large body text - 16pt, Regular - Used for main content
    static let bodyLarge = Font.system(size: 16, weight: .regular, design: .default)
    
    /// Medium body text - 14pt, Regular - Used for secondary content
    static let bodyMedium = Font.system(size: 14, weight: .regular, design: .default)
    
    /// Small body text - 12pt, Regular - Used for auxiliary information
    static let bodySmall = Font.system(size: 12, weight: .regular, design: .default)
    
    // MARK: - Special Purpose Fonts
    
    /// Button text - 16pt, Medium
    static let button = Font.system(size: 16, weight: .medium, design: .default)
    
    /// Navigation tab text - 10pt, Medium
    static let navigationTab = Font.system(size: 10, weight: .medium, design: .default)
    
    /// Timer display - 48pt, Bold - Used for practice timing
    static let timer = Font.system(size: 48, weight: .bold, design: .default)
    
    /// Statistics numbers - 24pt, Bold - Used for data display
    static let statistics = Font.system(size: 24, weight: .bold, design: .default)
    
    /// Caption text - 11pt, Regular - Used for small labels
    static let caption = Font.system(size: 11, weight: .regular, design: .default)
    
    // MARK: - Dynamic Type Support
    
    /// Large title with dynamic type support
    static let dynamicH1 = Font.custom("SF Pro Display", size: 32, relativeTo: .largeTitle)
    
    /// Medium title with dynamic type support
    static let dynamicH2 = Font.custom("SF Pro Display", size: 24, relativeTo: .title)
    
    /// Body text with dynamic type support
    static let dynamicBody = Font.custom("SF Pro Text", size: 16, relativeTo: .body)
}

// MARK: - Text Styles

struct TextStyle {
    let font: Font
    let color: Color
    let lineSpacing: CGFloat
    
    // MARK: - Predefined Styles
    
    /// Primary heading style
    static let primaryHeading = TextStyle(
        font: .h1,
        color: .primaryText,
        lineSpacing: 4
    )
    
    /// Secondary heading style
    static let secondaryHeading = TextStyle(
        font: .h2,
        color: .primaryText,
        lineSpacing: 3
    )
    
    /// Card title style
    static let cardTitle = TextStyle(
        font: .h3,
        color: .primaryText,
        lineSpacing: 2
    )
    
    /// Primary body style
    static let primaryBody = TextStyle(
        font: .bodyLarge,
        color: .primaryText,
        lineSpacing: 6
    )
    
    /// Secondary body style
    static let secondaryBody = TextStyle(
        font: .bodyMedium,
        color: .secondaryText,
        lineSpacing: 5
    )
    
    /// Caption style
    static let caption = TextStyle(
        font: .caption,
        color: .secondaryText,
        lineSpacing: 2
    )
    
    /// Button text style
    static let button = TextStyle(
        font: .button,
        color: .white,
        lineSpacing: 0
    )
    
    /// Timer display style
    static let timer = TextStyle(
        font: .timer,
        color: .primaryTeal,
        lineSpacing: 0
    )
    
    /// Statistics style
    static let statistics = TextStyle(
        font: .statistics,
        color: .primaryTeal,
        lineSpacing: 0
    )
}

// MARK: - Text View Modifier

struct StyledText: ViewModifier {
    let style: TextStyle
    
    func body(content: Content) -> some View {
        content
            .font(style.font)
            .foregroundColor(style.color)
            .lineSpacing(style.lineSpacing)
    }
}

extension View {
    /// Apply a text style to any view
    func textStyle(_ style: TextStyle) -> some View {
        modifier(StyledText(style: style))
    }
}

// MARK: - Line Height Calculations

extension TextStyle {
    /// Calculate line height based on font size
    var lineHeight: CGFloat {
        switch font {
        case .h1, .h2, .h3, .h4:
            return fontSize * 1.2
        case .button:
            return fontSize * 1.3
        default:
            return fontSize * 1.5
        }
    }
    
    /// Extract font size from Font (approximation)
    private var fontSize: CGFloat {
        // This is a simplified approach - in practice you might want to use UIFont
        switch font {
        case .h1: return 32
        case .h2: return 24
        case .h3: return 20
        case .h4: return 18
        case .bodyLarge: return 16
        case .bodyMedium: return 14
        case .bodySmall: return 12
        case .button: return 16
        case .navigationTab: return 10
        case .timer: return 48
        case .statistics: return 24
        case .caption: return 11
        default: return 16
        }
    }
}
