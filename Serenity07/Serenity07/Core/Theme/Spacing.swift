//
//  Spacing.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

/// Spacing system based on 8pt grid system from design specifications
struct Spacing {
    
    // MARK: - Base Unit
    
    /// Base spacing unit (8pt)
    static let base: CGFloat = 8
    
    // MARK: - Standard Spacing Values
    
    /// Extra small spacing (8pt)
    static let xs: CGFloat = base * 1  // 8pt
    
    /// Small spacing (12pt)
    static let sm: CGFloat = base * 1.5  // 12pt
    
    /// Medium spacing (16pt)
    static let md: CGFloat = base * 2  // 16pt
    
    /// Large spacing (24pt)
    static let lg: CGFloat = base * 3  // 24pt
    
    /// Extra large spacing (32pt)
    static let xl: CGFloat = base * 4  // 32pt
    
    /// Extra extra large spacing (48pt)
    static let xxl: CGFloat = base * 6  // 48pt
    
    // MARK: - Layout Specific Spacing
    
    /// Container edge margins (24pt)
    static let containerMargin: CGFloat = lg
    
    /// Card internal padding (16pt)
    static let cardPadding: CGFloat = md
    
    /// Large card internal padding (24pt)
    static let largeCardPadding: CGFloat = lg
    
    /// Vertical spacing between cards (16pt)
    static let cardSpacing: CGFloat = md
    
    /// Button internal padding (16pt)
    static let buttonPadding: CGFloat = md
    
    /// Small button internal padding (12pt)
    static let smallButtonPadding: CGFloat = sm
    
    /// Section spacing (32pt)
    static let sectionSpacing: CGFloat = xl
    
    /// List item spacing (8pt)
    static let listItemSpacing: CGFloat = xs
    
    // MARK: - Component Specific Spacing
    
    /// Navigation bar padding
    static let navigationPadding: CGFloat = md
    
    /// Tab bar padding
    static let tabBarPadding: CGFloat = sm
    
    /// Form field spacing
    static let formFieldSpacing: CGFloat = md
    
    /// Icon spacing from text
    static let iconTextSpacing: CGFloat = xs
    
    /// Breathing circle margin
    static let breathingCircleMargin: CGFloat = xl
}

// MARK: - Corner Radius Values

struct CornerRadius {
    
    /// Small corner radius (8pt)
    static let small: CGFloat = 8
    
    /// Medium corner radius (16pt)
    static let medium: CGFloat = 16
    
    /// Large corner radius (24pt)
    static let large: CGFloat = 24
    
    /// Extra large corner radius (32pt)
    static let extraLarge: CGFloat = 32
    
    // MARK: - Component Specific Corner Radius
    
    /// Button corner radius (24pt for 48pt height buttons)
    static let button: CGFloat = large
    
    /// Small button corner radius (20pt for 40pt height buttons)
    static let smallButton: CGFloat = 20
    
    /// Card corner radius (24pt)
    static let card: CGFloat = large
    
    /// Large card corner radius (32pt)
    static let largeCard: CGFloat = extraLarge
    
    /// Input field corner radius (16pt)
    static let inputField: CGFloat = medium
    
    /// Search field corner radius (20pt)
    static let searchField: CGFloat = 20
    
    /// List item corner radius (16pt)
    static let listItem: CGFloat = medium
    
    /// Icon button corner radius (20pt for 40x40pt buttons)
    static let iconButton: CGFloat = 20
    
    /// Floating button corner radius (28pt for 56x56pt buttons)
    static let floatingButton: CGFloat = 28
}

// MARK: - Component Sizes

struct ComponentSize {
    
    // MARK: - Button Sizes
    
    /// Primary button height (48pt)
    static let primaryButtonHeight: CGFloat = 48
    
    /// Secondary button height (40pt)
    static let secondaryButtonHeight: CGFloat = 40
    
    /// Icon button size (40x40pt)
    static let iconButtonSize: CGFloat = 40
    
    /// Floating button size (56x56pt)
    static let floatingButtonSize: CGFloat = 56
    
    /// Minimum button width (120pt)
    static let minimumButtonWidth: CGFloat = 120
    
    // MARK: - Input Field Sizes
    
    /// Text input field height (48pt)
    static let textFieldHeight: CGFloat = 48
    
    /// Search field height (40pt)
    static let searchFieldHeight: CGFloat = 40
    
    // MARK: - Card Sizes
    
    /// Standard list item height (64pt)
    static let listItemHeight: CGFloat = 64
    
    /// Minimum card height (120pt)
    static let minimumCardHeight: CGFloat = 120
    
    /// Statistics card height (80pt)
    static let statisticsCardHeight: CGFloat = 80
    
    // MARK: - Navigation Sizes
    
    /// Navigation bar height (44pt)
    static let navigationBarHeight: CGFloat = 44
    
    /// Tab bar height (49pt + safe area)
    static let tabBarHeight: CGFloat = 49
    
    /// Tab bar total height including safe area (83pt)
    static let tabBarTotalHeight: CGFloat = 83
    
    // MARK: - Breathing Animation Sizes
    
    /// Maximum breathing circle size (320pt)
    static let breathingCircleMaxSize: CGFloat = 320
    
    /// Minimum breathing circle size (240pt)
    static let breathingCircleMinSize: CGFloat = 240
    
    // MARK: - Image Sizes
    
    /// Card thumbnail size (200x200pt)
    static let cardThumbnailSize: CGFloat = 200
    
    /// User avatar size (100x100pt)
    static let userAvatarSize: CGFloat = 100
    
    /// Small avatar size (40x40pt)
    static let smallAvatarSize: CGFloat = 40
    
    /// Icon size for navigation (24pt)
    static let navigationIconSize: CGFloat = 24
    
    /// Icon size for buttons (20pt)
    static let buttonIconSize: CGFloat = 20
    
    /// Small icon size (16pt)
    static let smallIconSize: CGFloat = 16
}

// MARK: - Safe Area Values

struct SafeArea {
    
    /// Top safe area (44pt for status bar)
    static let top: CGFloat = 44
    
    /// Bottom safe area (34pt for home indicator)
    static let bottom: CGFloat = 34
    
    /// Minimum touch target size (44x44pt)
    static let minimumTouchTarget: CGFloat = 44
}

// MARK: - View Extensions for Spacing

extension View {
    
    /// Apply standard container padding
    func containerPadding() -> some View {
        padding(.horizontal, Spacing.containerMargin)
    }
    
    /// Apply card padding
    func cardPadding() -> some View {
        padding(Spacing.cardPadding)
    }
    
    /// Apply large card padding
    func largeCardPadding() -> some View {
        padding(Spacing.largeCardPadding)
    }
    
    /// Apply button padding
    func buttonPadding() -> some View {
        padding(.horizontal, Spacing.buttonPadding)
            .padding(.vertical, Spacing.sm)
    }
}
