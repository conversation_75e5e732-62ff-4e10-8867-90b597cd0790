//
//  Colors.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

/// Design system colors based on the design specifications
extension Color {
    
    // MARK: - Primary Colors
    
    /// Primary blue color (#1A2151) - Used for main backgrounds, navigation bars, card backgrounds
    static let primaryBlue = Color(red: 0.102, green: 0.129, blue: 0.318)
    
    /// Primary teal color (#2EC4B6) - Used for emphasis, buttons, progress indicators, icon highlights
    static let primaryTeal = Color(red: 0.180, green: 0.769, blue: 0.714)
    
    /// Primary purple color (#4D4C7D) - Used for gradients, decorative elements, secondary emphasis
    static let primaryPurple = Color(red: 0.302, green: 0.298, blue: 0.490)
    
    // MARK: - Neutral Colors
    
    /// Dark gray color (#2E2E3A) - Used for secondary backgrounds, card separators
    static let darkGray = Color(red: 0.180, green: 0.180, blue: 0.227)
    
    /// Medium gray color (#6B7280) - Used for secondary text, icons
    static let mediumGray = Color(red: 0.420, green: 0.447, blue: 0.502)
    
    /// Light gray color (#F3F4F6) - Used for light mode backgrounds (future expansion)
    static let lightGray = Color(red: 0.953, green: 0.957, blue: 0.965)
    
    // MARK: - Functional Colors
    
    /// Success color (#10B981) - Used for completion states, success messages
    static let success = Color(red: 0.063, green: 0.725, blue: 0.506)
    
    /// Warning color (#F59E0B) - Used for reminders, attention items
    static let warning = Color(red: 0.961, green: 0.620, blue: 0.043)
    
    /// Error color (#EF4444) - Used for error messages, delete operations
    static let error = Color(red: 0.937, green: 0.267, blue: 0.267)
    
    /// Info color (#3B82F6) - Used for information messages, links
    static let info = Color(red: 0.231, green: 0.510, blue: 0.965)
    
    // MARK: - Gradient Colors
    
    /// Primary gradient from teal to purple
    static let primaryGradient = LinearGradient(
        colors: [primaryTeal, primaryPurple],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    /// Breathing animation gradient
    static let breathingGradient = RadialGradient(
        colors: [primaryTeal.opacity(0.8), primaryPurple.opacity(0.6)],
        center: .center,
        startRadius: 50,
        endRadius: 200
    )
    
    // MARK: - Semantic Colors
    
    /// Background color for the main app
    static let appBackground = primaryBlue
    
    /// Card background with transparency
    static let cardBackground = Color.white.opacity(0.1)
    
    /// Text color for primary content
    static let primaryText = Color.white
    
    /// Text color for secondary content
    static let secondaryText = mediumGray
    
    /// Button background color
    static let buttonBackground = primaryTeal
    
    /// Disabled button background
    static let disabledButtonBackground = mediumGray.opacity(0.3)
}

// MARK: - Color Extensions for UIKit Compatibility

extension UIColor {
    
    /// Convert SwiftUI Color to UIColor
    static let primaryBlue = UIColor(Color.primaryBlue)
    static let primaryTeal = UIColor(Color.primaryTeal)
    static let primaryPurple = UIColor(Color.primaryPurple)
    static let darkGray = UIColor(Color.darkGray)
    static let mediumGray = UIColor(Color.mediumGray)
    static let lightGray = UIColor(Color.lightGray)
}
