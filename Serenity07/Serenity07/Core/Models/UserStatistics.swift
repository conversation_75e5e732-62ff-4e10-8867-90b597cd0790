//
//  UserStatistics.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData

/// User statistics model for tracking practice progress and achievements
@objc(UserStatistics)
public class UserStatistics: NSManagedObject {
    
    // MARK: - Core Data Properties
    
    @NSManaged public var userId: String
    @NSManaged public var totalSessions: Int32
    @NSManaged public var totalMinutes: Double
    @NSManaged public var currentStreak: Int32
    @NSManaged public var longestStreak: Int32
    @NSManaged public var breathingSessions: Int32
    @NSManaged public var breathingMinutes: Double
    @NSManaged public var meditationSessions: Int32
    @NSManaged public var meditationMinutes: Double
    @NSManaged public var sleepSessions: Int32
    @NSManaged public var sleepMinutes: Double
    @NSManaged public var averageSessionLength: Double
    @NSManaged public var favoriteExerciseType: String?
    @NSManaged public var preferredSessionTime: String? // morning, afternoon, evening, night
    @NSManaged public var weeklyGoal: Int32 // minutes per week
    @NSManaged public var monthlyGoal: Int32 // minutes per month
    @NSManaged public var lastSessionDate: Date?
    @NSManaged public var firstSessionDate: Date?
    @NSManaged public var createdDate: Date
    @NSManaged public var updatedDate: Date
    @NSManaged public var weeklyStats: String? // JSON data for weekly breakdown
    @NSManaged public var monthlyStats: String? // JSON data for monthly breakdown
    
    // MARK: - Relationships
    
    @NSManaged public var user: User?
    @NSManaged public var achievements: NSSet?
    
    // MARK: - Computed Properties
    
    /// Total practice time in hours
    var totalHours: Double {
        return totalMinutes / 60.0
    }
    
    /// Average sessions per week
    var averageSessionsPerWeek: Double {
        guard let firstSession = firstSessionDate else { return 0 }
        let daysSinceFirst = Date().timeIntervalSince(firstSession) / (24 * 60 * 60)
        let weeksSinceFirst = max(1, daysSinceFirst / 7)
        return Double(totalSessions) / weeksSinceFirst
    }
    
    /// Completion rate for current week
    var weeklyCompletionRate: Double {
        guard weeklyGoal > 0 else { return 0 }
        let weeklyMinutes = getWeeklyMinutes()
        return min(100, (weeklyMinutes / Double(weeklyGoal)) * 100)
    }
    
    /// Completion rate for current month
    var monthlyCompletionRate: Double {
        guard monthlyGoal > 0 else { return 0 }
        let monthlyMinutes = getMonthlyMinutes()
        return min(100, (monthlyMinutes / Double(monthlyGoal)) * 100)
    }
    
    /// Days since last session
    var daysSinceLastSession: Int {
        guard let lastSession = lastSessionDate else { return 0 }
        return Calendar.current.dateComponents([.day], from: lastSession, to: Date()).day ?? 0
    }
    
    /// Whether user practiced today
    var practicedToday: Bool {
        guard let lastSession = lastSessionDate else { return false }
        return Calendar.current.isDateInToday(lastSession)
    }
    
    /// Practice consistency score (0-100)
    var consistencyScore: Int {
        let streakScore = min(50, Int(currentStreak) * 2)
        let frequencyScore = min(30, Int(averageSessionsPerWeek) * 5)
        let recentActivityScore = daysSinceLastSession <= 1 ? 20 : max(0, 20 - daysSinceLastSession)
        
        return streakScore + frequencyScore + recentActivityScore
    }
    
    /// Weekly practice data
    var weeklyData: [String: Double] {
        guard let weeklyStats = weeklyStats,
              let data = weeklyStats.data(using: .utf8),
              let dict = try? JSONDecoder().decode([String: Double].self, from: data) else {
            return [:]
        }
        return dict
    }
    
    /// Monthly practice data
    var monthlyData: [String: Double] {
        guard let monthlyStats = monthlyStats,
              let data = monthlyStats.data(using: .utf8),
              let dict = try? JSONDecoder().decode([String: Double].self, from: data) else {
            return [:]
        }
        return dict
    }
    
    /// Preferred session time as enum
    var preferredTime: PreferredSessionTime? {
        guard let timeString = preferredSessionTime else { return nil }
        return PreferredSessionTime(rawValue: timeString)
    }
    
    /// Favorite exercise type as enum
    var favoriteType: FavoriteExerciseType? {
        guard let typeString = favoriteExerciseType else { return nil }
        return FavoriteExerciseType(rawValue: typeString)
    }
}

// MARK: - Preferred Session Time

enum PreferredSessionTime: String, CaseIterable {
    case morning = "morning"
    case afternoon = "afternoon"
    case evening = "evening"
    case night = "night"
    
    var displayName: String {
        switch self {
        case .morning:
            return NSLocalizedString("Morning", comment: "Morning session time")
        case .afternoon:
            return NSLocalizedString("Afternoon", comment: "Afternoon session time")
        case .evening:
            return NSLocalizedString("Evening", comment: "Evening session time")
        case .night:
            return NSLocalizedString("Night", comment: "Night session time")
        }
    }
    
    var iconName: String {
        switch self {
        case .morning:
            return "sunrise.fill"
        case .afternoon:
            return "sun.max.fill"
        case .evening:
            return "sunset.fill"
        case .night:
            return "moon.fill"
        }
    }
    
    var timeRange: String {
        switch self {
        case .morning:
            return "6:00 - 12:00"
        case .afternoon:
            return "12:00 - 17:00"
        case .evening:
            return "17:00 - 21:00"
        case .night:
            return "21:00 - 6:00"
        }
    }
}

// MARK: - Favorite Exercise Type

enum FavoriteExerciseType: String, CaseIterable {
    case breathing = "breathing"
    case meditation = "meditation"
    case sleep = "sleep"
    case mixed = "mixed"
    
    var displayName: String {
        switch self {
        case .breathing:
            return NSLocalizedString("Breathing", comment: "Breathing exercises")
        case .meditation:
            return NSLocalizedString("Meditation", comment: "Meditation exercises")
        case .sleep:
            return NSLocalizedString("Sleep", comment: "Sleep content")
        case .mixed:
            return NSLocalizedString("Mixed", comment: "Mixed practice types")
        }
    }
    
    var iconName: String {
        switch self {
        case .breathing:
            return "lungs.fill"
        case .meditation:
            return "brain.head.profile"
        case .sleep:
            return "moon.fill"
        case .mixed:
            return "star.fill"
        }
    }
}

// MARK: - Core Data Extensions

extension UserStatistics {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<UserStatistics> {
        return NSFetchRequest<UserStatistics>(entityName: "UserStatistics")
    }
    
    /// Create or fetch user statistics
    static func getOrCreate(for user: User, in context: NSManagedObjectContext) -> UserStatistics {
        let request: NSFetchRequest<UserStatistics> = UserStatistics.fetchRequest()
        request.predicate = NSPredicate(format: "user == %@", user)
        
        do {
            if let existing = try context.fetch(request).first {
                return existing
            }
        } catch {
            print("Error fetching user statistics: \(error)")
        }
        
        // Create new statistics
        let stats = UserStatistics(context: context)
        stats.userId = user.userId
        stats.user = user
        stats.totalSessions = 0
        stats.totalMinutes = 0
        stats.currentStreak = 0
        stats.longestStreak = 0
        stats.breathingSessions = 0
        stats.breathingMinutes = 0
        stats.meditationSessions = 0
        stats.meditationMinutes = 0
        stats.sleepSessions = 0
        stats.sleepMinutes = 0
        stats.averageSessionLength = 0
        stats.weeklyGoal = 150 // 2.5 hours per week default
        stats.monthlyGoal = 600 // 10 hours per month default
        stats.createdDate = Date()
        stats.updatedDate = Date()
        
        return stats
    }
    
    /// Update statistics after a session
    func updateAfterSession(type: SessionType, duration: Double, date: Date = Date()) {
        // Update totals
        totalSessions += 1
        totalMinutes += duration
        
        // Update by type
        switch type {
        case .breathing:
            breathingSessions += 1
            breathingMinutes += duration
        case .meditation:
            meditationSessions += 1
            meditationMinutes += duration
        case .sleep:
            sleepSessions += 1
            sleepMinutes += duration
        }
        
        // Update average session length
        averageSessionLength = totalMinutes / Double(totalSessions)
        
        // Update dates
        if firstSessionDate == nil {
            firstSessionDate = date
        }
        lastSessionDate = date
        
        // Update streak
        updateStreak(sessionDate: date)
        
        // Update weekly/monthly stats
        updateWeeklyStats(duration: duration, date: date)
        updateMonthlyStats(duration: duration, date: date)
        
        // Update preferred time and type
        updatePreferences()
        
        updatedDate = Date()
    }
    
    /// Update streak based on session date
    private func updateStreak(sessionDate: Date) {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let sessionDay = calendar.startOfDay(for: sessionDate)
        
        if calendar.isDate(sessionDay, inSameDayAs: today) {
            // Session today - maintain or start streak
            if !practicedToday {
                currentStreak += 1
            }
        } else if let yesterday = calendar.date(byAdding: .day, value: -1, to: today),
                  calendar.isDate(sessionDay, inSameDayAs: yesterday) {
            // Session yesterday - continue streak
            currentStreak += 1
        } else {
            // Session on different day - reset streak
            currentStreak = 1
        }
        
        // Update longest streak
        if currentStreak > longestStreak {
            longestStreak = currentStreak
        }
    }
    
    /// Update weekly statistics
    private func updateWeeklyStats(duration: Double, date: Date) {
        let calendar = Calendar.current
        let weekOfYear = calendar.component(.weekOfYear, from: date)
        let year = calendar.component(.year, from: date)
        let key = "\(year)-W\(weekOfYear)"
        
        var weekly = weeklyData
        weekly[key] = (weekly[key] ?? 0) + duration
        
        if let data = try? JSONEncoder().encode(weekly),
           let json = String(data: data, encoding: .utf8) {
            weeklyStats = json
        }
    }
    
    /// Update monthly statistics
    private func updateMonthlyStats(duration: Double, date: Date) {
        let calendar = Calendar.current
        let month = calendar.component(.month, from: date)
        let year = calendar.component(.year, from: date)
        let key = "\(year)-\(String(format: "%02d", month))"
        
        var monthly = monthlyData
        monthly[key] = (monthly[key] ?? 0) + duration
        
        if let data = try? JSONEncoder().encode(monthly),
           let json = String(data: data, encoding: .utf8) {
            monthlyStats = json
        }
    }
    
    /// Update user preferences based on usage patterns
    private func updatePreferences() {
        // Update favorite exercise type
        if breathingMinutes > meditationMinutes && breathingMinutes > sleepMinutes {
            favoriteExerciseType = FavoriteExerciseType.breathing.rawValue
        } else if meditationMinutes > sleepMinutes {
            favoriteExerciseType = FavoriteExerciseType.meditation.rawValue
        } else if sleepMinutes > 0 {
            favoriteExerciseType = FavoriteExerciseType.sleep.rawValue
        } else {
            favoriteExerciseType = FavoriteExerciseType.mixed.rawValue
        }
        
        // Update preferred session time based on last session
        if let lastSession = lastSessionDate {
            let hour = Calendar.current.component(.hour, from: lastSession)
            switch hour {
            case 6..<12:
                preferredSessionTime = PreferredSessionTime.morning.rawValue
            case 12..<17:
                preferredSessionTime = PreferredSessionTime.afternoon.rawValue
            case 17..<21:
                preferredSessionTime = PreferredSessionTime.evening.rawValue
            default:
                preferredSessionTime = PreferredSessionTime.night.rawValue
            }
        }
    }
    
    /// Get minutes practiced this week
    func getWeeklyMinutes() -> Double {
        let calendar = Calendar.current
        let now = Date()
        let weekOfYear = calendar.component(.weekOfYear, from: now)
        let year = calendar.component(.year, from: now)
        let key = "\(year)-W\(weekOfYear)"
        
        return weeklyData[key] ?? 0
    }
    
    /// Get minutes practiced this month
    func getMonthlyMinutes() -> Double {
        let calendar = Calendar.current
        let now = Date()
        let month = calendar.component(.month, from: now)
        let year = calendar.component(.year, from: now)
        let key = "\(year)-\(String(format: "%02d", month))"
        
        return monthlyData[key] ?? 0
    }
    
    /// Set weekly goal
    func setWeeklyGoal(_ minutes: Int) {
        weeklyGoal = Int32(minutes)
        updatedDate = Date()
    }
    
    /// Set monthly goal
    func setMonthlyGoal(_ minutes: Int) {
        monthlyGoal = Int32(minutes)
        updatedDate = Date()
    }
    
    /// Reset streak (for testing or manual adjustment)
    func resetStreak() {
        currentStreak = 0
        updatedDate = Date()
    }
    
    /// Get practice summary for date range
    func getPracticeSummary(from startDate: Date, to endDate: Date) -> PracticeSummary {
        // This would typically query actual session data
        // For now, return a summary based on stored statistics
        
        let calendar = Calendar.current
        let days = calendar.dateComponents([.day], from: startDate, to: endDate).day ?? 0
        
        return PracticeSummary(
            totalSessions: Int(totalSessions),
            totalMinutes: totalMinutes,
            averageSessionLength: averageSessionLength,
            daysWithPractice: min(days, Int(totalSessions)),
            totalDays: days,
            breathingPercentage: totalMinutes > 0 ? (breathingMinutes / totalMinutes) * 100 : 0,
            meditationPercentage: totalMinutes > 0 ? (meditationMinutes / totalMinutes) * 100 : 0,
            sleepPercentage: totalMinutes > 0 ? (sleepMinutes / totalMinutes) * 100 : 0
        )
    }
}

// MARK: - Session Type

enum SessionType {
    case breathing
    case meditation
    case sleep
}

// MARK: - Practice Summary

struct PracticeSummary {
    let totalSessions: Int
    let totalMinutes: Double
    let averageSessionLength: Double
    let daysWithPractice: Int
    let totalDays: Int
    let breathingPercentage: Double
    let meditationPercentage: Double
    let sleepPercentage: Double
    
    var practiceFrequency: Double {
        guard totalDays > 0 else { return 0 }
        return Double(daysWithPractice) / Double(totalDays) * 100
    }
    
    var totalHours: Double {
        return totalMinutes / 60.0
    }
}
