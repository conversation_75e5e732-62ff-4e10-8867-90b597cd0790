//
//  BreathingExercise.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData

/// Breathing exercise model representing different breathing techniques
@objc(BreathingExercise)
public class BreathingExercise: NSManagedObject {
    
    // MARK: - Core Data Properties
    
    @NSManaged public var exerciseId: String
    @NSManaged public var name: String
    @NSManaged public var localizedName: String
    @NSManaged public var exerciseDescription: String
    @NSManaged public var instructions: String
    @NSManaged public var benefits: String
    @NSManaged public var difficulty: String
    @NSManaged public var category: String
    @NSManaged public var durationOptions: String // JSON array of durations in seconds
    @NSManaged public var breathingPattern: String // JSON representation of breathing phases
    @NSManaged public var imageUrl: String?
    @NSManaged public var audioUrl: String?
    @NSManaged public var isActive: Bool
    @NSManaged public var sortOrder: Int32
    @NSManaged public var isPremium: Bool
    @NSManaged public var createdDate: Date
    @NSManaged public var updatedDate: Date
    
    // MARK: - Relationships
    
    @NSManaged public var practiceSessions: NSSet?
    
    // MARK: - Computed Properties
    
    /// Exercise difficulty as enum
    var difficultyLevel: ExerciseDifficulty {
        get {
            return ExerciseDifficulty(rawValue: difficulty) ?? .beginner
        }
        set {
            difficulty = newValue.rawValue
        }
    }
    
    /// Exercise category as enum
    var exerciseCategory: ExerciseCategory {
        get {
            return ExerciseCategory(rawValue: category) ?? .general
        }
        set {
            category = newValue.rawValue
        }
    }
    
    /// Available duration options in seconds
    var availableDurations: [Int] {
        guard let data = durationOptions.data(using: .utf8),
              let durations = try? JSONDecoder().decode([Int].self, from: data) else {
            return [300, 600, 900] // Default: 5, 10, 15 minutes
        }
        return durations
    }
    
    /// Breathing pattern phases
    var breathingPhases: [BreathingPhase] {
        guard let data = breathingPattern.data(using: .utf8),
              let phases = try? JSONDecoder().decode([BreathingPhase].self, from: data) else {
            return BreathingPhase.defaultPattern
        }
        return phases
    }
    
    /// Benefits as array
    var benefitsList: [String] {
        return benefits.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }
    }
    
    /// Instructions as array
    var instructionsList: [String] {
        return instructions.components(separatedBy: "\n").filter { !$0.isEmpty }
    }
}

// MARK: - Exercise Difficulty

enum ExerciseDifficulty: String, CaseIterable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    
    var displayName: String {
        switch self {
        case .beginner:
            return NSLocalizedString("Beginner", comment: "Beginner difficulty")
        case .intermediate:
            return NSLocalizedString("Intermediate", comment: "Intermediate difficulty")
        case .advanced:
            return NSLocalizedString("Advanced", comment: "Advanced difficulty")
        }
    }
    
    var color: String {
        switch self {
        case .beginner:
            return "success"
        case .intermediate:
            return "warning"
        case .advanced:
            return "error"
        }
    }
}

// MARK: - Exercise Category

enum ExerciseCategory: String, CaseIterable {
    case general = "general"
    case stress = "stress"
    case sleep = "sleep"
    case focus = "focus"
    case energy = "energy"
    case anxiety = "anxiety"
    case meditation = "meditation"
    
    var displayName: String {
        switch self {
        case .general:
            return NSLocalizedString("General", comment: "General category")
        case .stress:
            return NSLocalizedString("Stress Relief", comment: "Stress relief category")
        case .sleep:
            return NSLocalizedString("Sleep", comment: "Sleep category")
        case .focus:
            return NSLocalizedString("Focus", comment: "Focus category")
        case .energy:
            return NSLocalizedString("Energy", comment: "Energy category")
        case .anxiety:
            return NSLocalizedString("Anxiety", comment: "Anxiety category")
        case .meditation:
            return NSLocalizedString("Meditation", comment: "Meditation category")
        }
    }
    
    var iconName: String {
        switch self {
        case .general:
            return "wind"
        case .stress:
            return "leaf"
        case .sleep:
            return "moon"
        case .focus:
            return "target"
        case .energy:
            return "bolt"
        case .anxiety:
            return "heart"
        case .meditation:
            return "spa"
        }
    }
}

// MARK: - Breathing Phase

struct BreathingPhase: Codable {
    let type: PhaseType
    let duration: Int // Duration in seconds
    let instructions: String
    
    enum PhaseType: String, Codable, CaseIterable {
        case inhale = "inhale"
        case hold = "hold"
        case exhale = "exhale"
        case pause = "pause"
        
        var displayName: String {
            switch self {
            case .inhale:
                return NSLocalizedString("Inhale", comment: "Inhale phase")
            case .hold:
                return NSLocalizedString("Hold", comment: "Hold phase")
            case .exhale:
                return NSLocalizedString("Exhale", comment: "Exhale phase")
            case .pause:
                return NSLocalizedString("Pause", comment: "Pause phase")
            }
        }
        
        var animationScale: Double {
            switch self {
            case .inhale:
                return 1.3
            case .hold:
                return 1.3
            case .exhale:
                return 1.0
            case .pause:
                return 1.0
            }
        }
    }
    
    /// Default breathing pattern (4-4-4-4 equal breathing)
    static let defaultPattern: [BreathingPhase] = [
        BreathingPhase(type: .inhale, duration: 4, instructions: "Breathe in slowly"),
        BreathingPhase(type: .hold, duration: 4, instructions: "Hold your breath"),
        BreathingPhase(type: .exhale, duration: 4, instructions: "Breathe out slowly"),
        BreathingPhase(type: .pause, duration: 4, instructions: "Pause briefly")
    ]
}

// MARK: - Predefined Breathing Exercises

extension BreathingExercise {
    
    /// Create predefined breathing exercises
    static func createPredefinedExercises(in context: NSManagedObjectContext) {
        let exercises = [
            (
                id: "equal_breathing",
                name: "Equal Breathing",
                localizedName: NSLocalizedString("Equal Breathing", comment: "Equal breathing exercise"),
                description: "A simple breathing technique where inhale and exhale are of equal length",
                instructions: "Breathe in for 4 counts\nHold for 4 counts\nBreathe out for 4 counts\nPause for 4 counts",
                benefits: "Reduces stress, Improves focus, Balances nervous system",
                difficulty: ExerciseDifficulty.beginner,
                category: ExerciseCategory.general,
                pattern: BreathingPhase.defaultPattern,
                durations: [300, 600, 900, 1200] // 5, 10, 15, 20 minutes
            ),
            (
                id: "box_breathing",
                name: "Box Breathing",
                localizedName: NSLocalizedString("Box Breathing", comment: "Box breathing exercise"),
                description: "A powerful stress-relief technique used by Navy SEALs",
                instructions: "Inhale for 4 counts\nHold for 4 counts\nExhale for 4 counts\nHold empty for 4 counts",
                benefits: "Reduces stress, Improves concentration, Enhances performance",
                difficulty: ExerciseDifficulty.intermediate,
                category: ExerciseCategory.stress,
                pattern: [
                    BreathingPhase(type: .inhale, duration: 4, instructions: "Breathe in slowly"),
                    BreathingPhase(type: .hold, duration: 4, instructions: "Hold your breath"),
                    BreathingPhase(type: .exhale, duration: 4, instructions: "Breathe out completely"),
                    BreathingPhase(type: .pause, duration: 4, instructions: "Hold empty")
                ],
                durations: [300, 600, 900]
            ),
            (
                id: "four_seven_eight",
                name: "4-7-8 Breathing",
                localizedName: NSLocalizedString("4-7-8 Breathing", comment: "4-7-8 breathing exercise"),
                description: "A natural tranquilizer for the nervous system",
                instructions: "Inhale for 4 counts\nHold for 7 counts\nExhale for 8 counts",
                benefits: "Promotes sleep, Reduces anxiety, Calms mind",
                difficulty: ExerciseDifficulty.intermediate,
                category: ExerciseCategory.sleep,
                pattern: [
                    BreathingPhase(type: .inhale, duration: 4, instructions: "Breathe in through nose"),
                    BreathingPhase(type: .hold, duration: 7, instructions: "Hold your breath"),
                    BreathingPhase(type: .exhale, duration: 8, instructions: "Exhale through mouth")
                ],
                durations: [180, 300, 600] // 3, 5, 10 minutes
            )
        ]
        
        for exercise in exercises {
            let breathingExercise = BreathingExercise(context: context)
            breathingExercise.exerciseId = exercise.id
            breathingExercise.name = exercise.name
            breathingExercise.localizedName = exercise.localizedName
            breathingExercise.exerciseDescription = exercise.description
            breathingExercise.instructions = exercise.instructions
            breathingExercise.benefits = exercise.benefits
            breathingExercise.difficultyLevel = exercise.difficulty
            breathingExercise.exerciseCategory = exercise.category
            breathingExercise.isActive = true
            breathingExercise.isPremium = false
            breathingExercise.createdDate = Date()
            breathingExercise.updatedDate = Date()
            
            // Encode pattern and durations as JSON
            if let patternData = try? JSONEncoder().encode(exercise.pattern),
               let patternString = String(data: patternData, encoding: .utf8) {
                breathingExercise.breathingPattern = patternString
            }
            
            if let durationData = try? JSONEncoder().encode(exercise.durations),
               let durationString = String(data: durationData, encoding: .utf8) {
                breathingExercise.durationOptions = durationString
            }
        }
    }
}

// MARK: - Core Data Extensions

extension BreathingExercise {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<BreathingExercise> {
        return NSFetchRequest<BreathingExercise>(entityName: "BreathingExercise")
    }
    
    /// Fetch active exercises
    static func fetchActiveExercises(in context: NSManagedObjectContext) -> [BreathingExercise] {
        let request: NSFetchRequest<BreathingExercise> = BreathingExercise.fetchRequest()
        request.predicate = NSPredicate(format: "isActive == true")
        request.sortDescriptors = [NSSortDescriptor(key: "sortOrder", ascending: true)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching breathing exercises: \(error)")
            return []
        }
    }
    
    /// Fetch exercises by category
    static func fetchExercises(category: ExerciseCategory, in context: NSManagedObjectContext) -> [BreathingExercise] {
        let request: NSFetchRequest<BreathingExercise> = BreathingExercise.fetchRequest()
        request.predicate = NSPredicate(format: "isActive == true AND category == %@", category.rawValue)
        request.sortDescriptors = [NSSortDescriptor(key: "sortOrder", ascending: true)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching exercises by category: \(error)")
            return []
        }
    }
}
