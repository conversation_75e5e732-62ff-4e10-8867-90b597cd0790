//
//  PersonalPlan.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData

/// Personal plan model for user's customized practice plans
@objc(PersonalPlan)
public class PersonalPlan: NSManagedObject {
    
    // MARK: - Core Data Properties
    
    @NSManaged public var planId: String
    @NSManaged public var goal: String
    @NSManaged public var startDate: Date
    @NSManaged public var endDate: Date
    @NSManaged public var dailyExercises: String // JSON representation of daily exercises
    @NSManaged public var completionRate: Float
    @NSManaged public var isActive: Bool
    @NSManaged public var createdDate: Date
    
    // MARK: - Relationships
    
    @NSManaged public var user: User?
    
    // MARK: - Computed Properties
    
    /// Plan goal as enum
    var planGoal: PlanGoal {
        get {
            return PlanGoal(rawValue: goal) ?? .general
        }
        set {
            goal = newValue.rawValue
        }
    }
    
    /// Daily exercises as structured data
    var dailyExerciseList: [DailyExercise] {
        guard let data = dailyExercises.data(using: .utf8),
              let exercises = try? JSONDecoder().decode([DailyExercise].self, from: data) else {
            return []
        }
        return exercises
    }
    
    /// Plan duration in days
    var durationInDays: Int {
        Calendar.current.dateComponents([.day], from: startDate, to: endDate).day ?? 0
    }
    
    /// Days remaining in plan
    var daysRemaining: Int {
        let remaining = Calendar.current.dateComponents([.day], from: Date(), to: endDate).day ?? 0
        return max(0, remaining)
    }
    
    /// Days completed in plan
    var daysCompleted: Int {
        let completed = Calendar.current.dateComponents([.day], from: startDate, to: Date()).day ?? 0
        return max(0, min(completed, durationInDays))
    }
    
    /// Progress percentage (0-100)
    var progressPercentage: Double {
        guard durationInDays > 0 else { return 0 }
        return Double(daysCompleted) / Double(durationInDays) * 100
    }
    
    /// Whether plan is currently active and within date range
    var isCurrentlyActive: Bool {
        let now = Date()
        return isActive && now >= startDate && now <= endDate
    }
    
    /// Whether plan is completed
    var isCompleted: Bool {
        return Date() > endDate || completionRate >= 100.0
    }
}

// MARK: - Plan Goals

enum PlanGoal: String, CaseIterable {
    case general = "general"
    case stressReduction = "stress_reduction"
    case sleepImprovement = "sleep_improvement"
    case focusEnhancement = "focus_enhancement"
    case anxietyManagement = "anxiety_management"
    case energyBoost = "energy_boost"
    case mindfulness = "mindfulness"
    
    var displayName: String {
        switch self {
        case .general:
            return NSLocalizedString("General Wellness", comment: "General wellness goal")
        case .stressReduction:
            return NSLocalizedString("Stress Reduction", comment: "Stress reduction goal")
        case .sleepImprovement:
            return NSLocalizedString("Sleep Improvement", comment: "Sleep improvement goal")
        case .focusEnhancement:
            return NSLocalizedString("Focus Enhancement", comment: "Focus enhancement goal")
        case .anxietyManagement:
            return NSLocalizedString("Anxiety Management", comment: "Anxiety management goal")
        case .energyBoost:
            return NSLocalizedString("Energy Boost", comment: "Energy boost goal")
        case .mindfulness:
            return NSLocalizedString("Mindfulness", comment: "Mindfulness goal")
        }
    }
    
    var description: String {
        switch self {
        case .general:
            return NSLocalizedString("Improve overall well-being through regular breathing practice", comment: "General wellness description")
        case .stressReduction:
            return NSLocalizedString("Learn techniques to manage and reduce daily stress", comment: "Stress reduction description")
        case .sleepImprovement:
            return NSLocalizedString("Develop better sleep habits and relaxation techniques", comment: "Sleep improvement description")
        case .focusEnhancement:
            return NSLocalizedString("Enhance concentration and mental clarity", comment: "Focus enhancement description")
        case .anxietyManagement:
            return NSLocalizedString("Build tools to cope with anxiety and worry", comment: "Anxiety management description")
        case .energyBoost:
            return NSLocalizedString("Increase energy levels and vitality", comment: "Energy boost description")
        case .mindfulness:
            return NSLocalizedString("Cultivate present-moment awareness and inner peace", comment: "Mindfulness description")
        }
    }
    
    var iconName: String {
        switch self {
        case .general:
            return "heart.fill"
        case .stressReduction:
            return "leaf.fill"
        case .sleepImprovement:
            return "moon.fill"
        case .focusEnhancement:
            return "target"
        case .anxietyManagement:
            return "shield.fill"
        case .energyBoost:
            return "bolt.fill"
        case .mindfulness:
            return "brain.head.profile"
        }
    }
    
    var color: String {
        switch self {
        case .general:
            return "primaryTeal"
        case .stressReduction:
            return "success"
        case .sleepImprovement:
            return "primaryPurple"
        case .focusEnhancement:
            return "info"
        case .anxietyManagement:
            return "warning"
        case .energyBoost:
            return "error"
        case .mindfulness:
            return "primaryTeal"
        }
    }
}

// MARK: - Daily Exercise

struct DailyExercise: Codable {
    let day: Int // Day number in the plan (1-based)
    let exerciseId: String
    let duration: Int // Duration in seconds
    let isCompleted: Bool
    let completedDate: Date?
    let notes: String?
    
    init(day: Int, exerciseId: String, duration: Int) {
        self.day = day
        self.exerciseId = exerciseId
        self.duration = duration
        self.isCompleted = false
        self.completedDate = nil
        self.notes = nil
    }
}

// MARK: - Plan Templates

struct PlanTemplate {
    let goal: PlanGoal
    let duration: Int // Duration in days
    let exercises: [PlanExercise]
    
    struct PlanExercise {
        let exerciseId: String
        let duration: Int
        let frequency: ExerciseFrequency // How often in the plan
    }
    
    enum ExerciseFrequency {
        case daily
        case everyOtherDay
        case twiceWeek
        case weekly
        case custom(days: [Int]) // Specific days
    }
}

// MARK: - Core Data Extensions

extension PersonalPlan {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<PersonalPlan> {
        return NSFetchRequest<PersonalPlan>(entityName: "PersonalPlan")
    }
    
    /// Create a new personal plan
    static func create(
        in context: NSManagedObjectContext,
        user: User,
        goal: PlanGoal,
        duration: Int,
        exercises: [DailyExercise]
    ) -> PersonalPlan {
        let plan = PersonalPlan(context: context)
        plan.planId = UUID().uuidString
        plan.user = user
        plan.planGoal = goal
        plan.startDate = Date()
        plan.endDate = Calendar.current.date(byAdding: .day, value: duration, to: Date()) ?? Date()
        plan.completionRate = 0.0
        plan.isActive = true
        plan.createdDate = Date()
        
        // Encode exercises as JSON
        if let exerciseData = try? JSONEncoder().encode(exercises),
           let exerciseString = String(data: exerciseData, encoding: .utf8) {
            plan.dailyExercises = exerciseString
        } else {
            plan.dailyExercises = "[]"
        }
        
        return plan
    }
    
    /// Get active plan for user
    static func getActivePlan(for user: User, in context: NSManagedObjectContext) -> PersonalPlan? {
        let request: NSFetchRequest<PersonalPlan> = PersonalPlan.fetchRequest()
        request.predicate = NSPredicate(format: "user == %@ AND isActive == true", user)
        request.sortDescriptors = [NSSortDescriptor(key: "startDate", ascending: false)]
        request.fetchLimit = 1
        
        do {
            return try context.fetch(request).first
        } catch {
            print("Error fetching active plan: \(error)")
            return nil
        }
    }
    
    /// Get today's exercise for the plan
    func getTodaysExercise() -> DailyExercise? {
        let daysSinceStart = Calendar.current.dateComponents([.day], from: startDate, to: Date()).day ?? 0
        let currentDay = daysSinceStart + 1
        
        return dailyExerciseList.first { $0.day == currentDay }
    }
    
    /// Mark today's exercise as completed
    func completeTodaysExercise(in context: NSManagedObjectContext, notes: String? = nil) {
        var exercises = dailyExerciseList
        let daysSinceStart = Calendar.current.dateComponents([.day], from: startDate, to: Date()).day ?? 0
        let currentDay = daysSinceStart + 1
        
        if let index = exercises.firstIndex(where: { $0.day == currentDay }) {
            exercises[index] = DailyExercise(
                day: exercises[index].day,
                exerciseId: exercises[index].exerciseId,
                duration: exercises[index].duration
            )
            // Note: In a real implementation, you'd need to properly handle the completed state
            
            // Update completion rate
            let completedCount = exercises.filter { $0.isCompleted }.count
            completionRate = Float(completedCount) / Float(exercises.count) * 100
            
            // Encode back to JSON
            if let exerciseData = try? JSONEncoder().encode(exercises),
               let exerciseString = String(data: exerciseData, encoding: .utf8) {
                dailyExercises = exerciseString
            }
        }
    }
    
    /// Generate plan templates
    static func getDefaultTemplates() -> [PlanTemplate] {
        return [
            // 7-day stress reduction plan
            PlanTemplate(
                goal: .stressReduction,
                duration: 7,
                exercises: [
                    PlanTemplate.PlanExercise(exerciseId: "equal_breathing", duration: 300, frequency: .daily),
                    PlanTemplate.PlanExercise(exerciseId: "box_breathing", duration: 600, frequency: .everyOtherDay)
                ]
            ),
            
            // 14-day sleep improvement plan
            PlanTemplate(
                goal: .sleepImprovement,
                duration: 14,
                exercises: [
                    PlanTemplate.PlanExercise(exerciseId: "four_seven_eight", duration: 300, frequency: .daily),
                    PlanTemplate.PlanExercise(exerciseId: "equal_breathing", duration: 600, frequency: .twiceWeek)
                ]
            ),
            
            // 30-day mindfulness plan
            PlanTemplate(
                goal: .mindfulness,
                duration: 30,
                exercises: [
                    PlanTemplate.PlanExercise(exerciseId: "equal_breathing", duration: 300, frequency: .daily),
                    PlanTemplate.PlanExercise(exerciseId: "box_breathing", duration: 600, frequency: .everyOtherDay),
                    PlanTemplate.PlanExercise(exerciseId: "four_seven_eight", duration: 900, frequency: .twiceWeek)
                ]
            )
        ]
    }
    
    /// Create plan from template
    static func createFromTemplate(
        _ template: PlanTemplate,
        for user: User,
        in context: NSManagedObjectContext
    ) -> PersonalPlan {
        var dailyExercises: [DailyExercise] = []
        
        // Generate daily exercises based on template
        for day in 1...template.duration {
            for exercise in template.exercises {
                if shouldIncludeExercise(exercise, onDay: day) {
                    dailyExercises.append(
                        DailyExercise(
                            day: day,
                            exerciseId: exercise.exerciseId,
                            duration: exercise.duration
                        )
                    )
                }
            }
        }
        
        return create(
            in: context,
            user: user,
            goal: template.goal,
            duration: template.duration,
            exercises: dailyExercises
        )
    }
    
    private static func shouldIncludeExercise(_ exercise: PlanTemplate.PlanExercise, onDay day: Int) -> Bool {
        switch exercise.frequency {
        case .daily:
            return true
        case .everyOtherDay:
            return day % 2 == 1
        case .twiceWeek:
            return day % 7 == 1 || day % 7 == 4
        case .weekly:
            return day % 7 == 1
        case .custom(let days):
            return days.contains(day % 7)
        }
    }
}
