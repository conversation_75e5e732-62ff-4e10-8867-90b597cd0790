//
//  PracticeSession.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData

/// Practice session model representing user's breathing practice sessions
@objc(PracticeSession)
public class PracticeSession: NSManagedObject {
    
    // MARK: - Core Data Properties
    
    @NSManaged public var sessionId: String
    @NSManaged public var startTime: Date
    @NSManaged public var endTime: Date?
    @NSManaged public var plannedDuration: Int32 // Duration in seconds
    @NSManaged public var actualDuration: Int32 // Actual duration in seconds
    @NSManaged public var completionStatus: String
    @NSManaged public var feedback: Int16 // 1-5 star rating
    @NSManaged public var notes: String?
    @NSManaged public var breathingSpeed: String // slow, normal, fast
    @NSManaged public var backgroundSound: String?
    @NSManaged public var soundVolume: Float
    @NSManaged public var wasInterrupted: Bool
    @NSManaged public var interruptionReason: String?
    @NSManaged public var heartRateBefore: Int16 // Optional heart rate data
    @NSManaged public var heartRateAfter: Int16
    @NSManaged public var moodBefore: String? // User's mood before session
    @NSManaged public var moodAfter: String? // User's mood after session
    @NSManaged public var createdDate: Date
    
    // MARK: - Relationships
    
    @NSManaged public var user: User?
    @NSManaged public var exercise: BreathingExercise?
    
    // MARK: - Computed Properties
    
    /// Session completion status as enum
    var status: SessionStatus {
        get {
            return SessionStatus(rawValue: completionStatus) ?? .incomplete
        }
        set {
            completionStatus = newValue.rawValue
        }
    }
    
    /// Breathing speed as enum
    var speed: BreathingSpeed {
        get {
            return BreathingSpeed(rawValue: breathingSpeed) ?? .normal
        }
        set {
            breathingSpeed = newValue.rawValue
        }
    }
    
    /// Session duration in minutes
    var durationInMinutes: Double {
        return Double(actualDuration) / 60.0
    }
    
    /// Completion percentage
    var completionPercentage: Double {
        guard plannedDuration > 0 else { return 0 }
        return Double(actualDuration) / Double(plannedDuration) * 100
    }
    
    /// Whether session was completed successfully
    var isCompleted: Bool {
        return status == .completed
    }
    
    /// Session quality score (0-100)
    var qualityScore: Int {
        var score = 0
        
        // Base score from completion
        if isCompleted {
            score += 50
        } else {
            score += Int(completionPercentage / 2)
        }
        
        // Bonus for feedback
        if feedback > 0 {
            score += Int(feedback) * 10
        }
        
        // Penalty for interruptions
        if wasInterrupted {
            score -= 20
        }
        
        return max(0, min(100, score))
    }
}

// MARK: - Session Status

enum SessionStatus: String, CaseIterable {
    case incomplete = "incomplete"
    case completed = "completed"
    case cancelled = "cancelled"
    case paused = "paused"
    
    var displayName: String {
        switch self {
        case .incomplete:
            return NSLocalizedString("Incomplete", comment: "Incomplete session")
        case .completed:
            return NSLocalizedString("Completed", comment: "Completed session")
        case .cancelled:
            return NSLocalizedString("Cancelled", comment: "Cancelled session")
        case .paused:
            return NSLocalizedString("Paused", comment: "Paused session")
        }
    }
    
    var color: String {
        switch self {
        case .incomplete:
            return "warning"
        case .completed:
            return "success"
        case .cancelled:
            return "error"
        case .paused:
            return "info"
        }
    }
}

// MARK: - Breathing Speed

enum BreathingSpeed: String, CaseIterable {
    case slow = "slow"
    case normal = "normal"
    case fast = "fast"
    
    var displayName: String {
        switch self {
        case .slow:
            return NSLocalizedString("Slow", comment: "Slow breathing speed")
        case .normal:
            return NSLocalizedString("Normal", comment: "Normal breathing speed")
        case .fast:
            return NSLocalizedString("Fast", comment: "Fast breathing speed")
        }
    }
    
    var multiplier: Double {
        switch self {
        case .slow:
            return 1.5
        case .normal:
            return 1.0
        case .fast:
            return 0.75
        }
    }
}

// MARK: - Mood Tracking

enum Mood: String, CaseIterable {
    case veryBad = "very_bad"
    case bad = "bad"
    case neutral = "neutral"
    case good = "good"
    case veryGood = "very_good"
    
    var displayName: String {
        switch self {
        case .veryBad:
            return NSLocalizedString("Very Bad", comment: "Very bad mood")
        case .bad:
            return NSLocalizedString("Bad", comment: "Bad mood")
        case .neutral:
            return NSLocalizedString("Neutral", comment: "Neutral mood")
        case .good:
            return NSLocalizedString("Good", comment: "Good mood")
        case .veryGood:
            return NSLocalizedString("Very Good", comment: "Very good mood")
        }
    }
    
    var emoji: String {
        switch self {
        case .veryBad:
            return "😢"
        case .bad:
            return "😞"
        case .neutral:
            return "😐"
        case .good:
            return "😊"
        case .veryGood:
            return "😄"
        }
    }
    
    var value: Int {
        switch self {
        case .veryBad:
            return 1
        case .bad:
            return 2
        case .neutral:
            return 3
        case .good:
            return 4
        case .veryGood:
            return 5
        }
    }
}

// MARK: - Session Statistics

struct SessionStatistics {
    let totalSessions: Int
    let completedSessions: Int
    let totalMinutes: Double
    let averageRating: Double
    let currentStreak: Int
    let longestStreak: Int
    let completionRate: Double
    let averageSessionLength: Double
    
    var completionPercentage: Double {
        guard totalSessions > 0 else { return 0 }
        return Double(completedSessions) / Double(totalSessions) * 100
    }
}

// MARK: - Core Data Extensions

extension PracticeSession {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<PracticeSession> {
        return NSFetchRequest<PracticeSession>(entityName: "PracticeSession")
    }
    
    /// Create a new practice session
    static func create(in context: NSManagedObjectContext,
                      user: User,
                      exercise: BreathingExercise,
                      plannedDuration: Int) -> PracticeSession {
        let session = PracticeSession(context: context)
        session.sessionId = UUID().uuidString
        session.user = user
        session.exercise = exercise
        session.startTime = Date()
        session.plannedDuration = Int32(plannedDuration)
        session.actualDuration = 0
        session.status = .incomplete
        session.feedback = 0
        session.speed = .normal
        session.soundVolume = user.backgroundSoundVolume
        session.wasInterrupted = false
        session.createdDate = Date()
        
        return session
    }
    
    /// Complete the session
    func complete(with feedback: Int? = nil, notes: String? = nil) {
        self.endTime = Date()
        self.status = .completed
        
        if let startTime = self.endTime {
            self.actualDuration = Int32(startTime.timeIntervalSince(self.startTime))
        }
        
        if let feedback = feedback {
            self.feedback = Int16(feedback)
        }
        
        if let notes = notes {
            self.notes = notes
        }
    }
    
    /// Cancel the session
    func cancel(reason: String? = nil) {
        self.endTime = Date()
        self.status = .cancelled
        self.interruptionReason = reason
        self.wasInterrupted = true
        
        if let startTime = self.endTime {
            self.actualDuration = Int32(startTime.timeIntervalSince(self.startTime))
        }
    }
    
    /// Fetch user's sessions for a date range
    static func fetchSessions(for user: User,
                             from startDate: Date,
                             to endDate: Date,
                             in context: NSManagedObjectContext) -> [PracticeSession] {
        let request: NSFetchRequest<PracticeSession> = PracticeSession.fetchRequest()
        request.predicate = NSPredicate(format: "user == %@ AND startTime >= %@ AND startTime <= %@",
                                       user, startDate as NSDate, endDate as NSDate)
        request.sortDescriptors = [NSSortDescriptor(key: "startTime", ascending: false)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching practice sessions: \(error)")
            return []
        }
    }
    
    /// Calculate statistics for user
    static func calculateStatistics(for user: User, in context: NSManagedObjectContext) -> SessionStatistics {
        let request: NSFetchRequest<PracticeSession> = PracticeSession.fetchRequest()
        request.predicate = NSPredicate(format: "user == %@", user)
        
        do {
            let sessions = try context.fetch(request)
            let completedSessions = sessions.filter { $0.isCompleted }
            
            let totalMinutes = sessions.reduce(0) { $0 + $1.durationInMinutes }
            let averageRating = completedSessions.isEmpty ? 0 :
                Double(completedSessions.reduce(0) { $0 + Int($1.feedback) }) / Double(completedSessions.count)
            
            let averageSessionLength = sessions.isEmpty ? 0 : totalMinutes / Double(sessions.count)
            
            // Calculate streaks
            let sortedSessions = sessions.sorted { $0.startTime > $1.startTime }
            let currentStreak = calculateCurrentStreak(sessions: sortedSessions)
            let longestStreak = calculateLongestStreak(sessions: sortedSessions)
            
            return SessionStatistics(
                totalSessions: sessions.count,
                completedSessions: completedSessions.count,
                totalMinutes: totalMinutes,
                averageRating: averageRating,
                currentStreak: currentStreak,
                longestStreak: longestStreak,
                completionRate: sessions.isEmpty ? 0 : Double(completedSessions.count) / Double(sessions.count) * 100,
                averageSessionLength: averageSessionLength
            )
        } catch {
            print("Error calculating statistics: \(error)")
            return SessionStatistics(totalSessions: 0, completedSessions: 0, totalMinutes: 0,
                                   averageRating: 0, currentStreak: 0, longestStreak: 0,
                                   completionRate: 0, averageSessionLength: 0)
        }
    }
    
    /// Calculate current streak of consecutive days with completed sessions
    private static func calculateCurrentStreak(sessions: [PracticeSession]) -> Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        var streak = 0
        var currentDate = today
        
        let completedSessions = sessions.filter { $0.isCompleted }
        let sessionsByDate = Dictionary(grouping: completedSessions) { session in
            calendar.startOfDay(for: session.startTime)
        }
        
        while sessionsByDate[currentDate] != nil {
            streak += 1
            currentDate = calendar.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
        }
        
        return streak
    }
    
    /// Calculate longest streak of consecutive days
    private static func calculateLongestStreak(sessions: [PracticeSession]) -> Int {
        let calendar = Calendar.current
        let completedSessions = sessions.filter { $0.isCompleted }
        let sessionDates = Set(completedSessions.map { calendar.startOfDay(for: $0.startTime) })
        let sortedDates = sessionDates.sorted()
        
        var longestStreak = 0
        var currentStreak = 0
        var previousDate: Date?
        
        for date in sortedDates {
            if let prev = previousDate,
               calendar.dateComponents([.day], from: prev, to: date).day == 1 {
                currentStreak += 1
            } else {
                currentStreak = 1
            }
            
            longestStreak = max(longestStreak, currentStreak)
            previousDate = date
        }
        
        return longestStreak
    }
}
