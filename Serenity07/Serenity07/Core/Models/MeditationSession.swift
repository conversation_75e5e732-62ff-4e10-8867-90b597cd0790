//
//  MeditationSession.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData

/// Meditation session model representing user's meditation practice sessions
@objc(MeditationSession)
public class MeditationSession: NSManagedObject {
    
    // MARK: - Core Data Properties
    
    @NSManaged public var sessionId: String
    @NSManaged public var startTime: Date
    @NSManaged public var endTime: Date?
    @NSManaged public var plannedDuration: Int32 // Duration in seconds
    @NSManaged public var actualDuration: Int32 // Actual duration in seconds
    @NSManaged public var completionStatus: String
    @NSManaged public var feedback: Int16 // 1-5 star rating
    @NSManaged public var notes: String?
    @NSManaged public var backgroundSound: String?
    @NSManaged public var soundVolume: Float
    @NSManaged public var wasInterrupted: Bool
    @NSManaged public var interruptionReason: String?
    @NSManaged public var moodBefore: String? // User's mood before session
    @NSManaged public var moodAfter: String? // User's mood after session
    @NSManaged public var focusLevel: Int16 // 1-10 focus level during session
    @NSManaged public var calmLevel: Int16 // 1-10 calm level after session
    @NSManaged public var insights: String? // User's insights or reflections
    @NSManaged public var createdDate: Date
    
    // MARK: - Relationships
    
    @NSManaged public var user: User?
    @NSManaged public var exercise: MeditationExercise?
    
    // MARK: - Computed Properties
    
    /// Session completion status as enum
    var status: SessionStatus {
        get {
            return SessionStatus(rawValue: completionStatus) ?? .incomplete
        }
        set {
            completionStatus = newValue.rawValue
        }
    }
    
    /// Session duration in minutes
    var durationInMinutes: Double {
        return Double(actualDuration) / 60.0
    }
    
    /// Completion percentage
    var completionPercentage: Double {
        guard plannedDuration > 0 else { return 0 }
        return Double(actualDuration) / Double(plannedDuration) * 100
    }
    
    /// Whether session was completed successfully
    var isCompleted: Bool {
        return status == .completed
    }
    
    /// Session quality score (0-100)
    var qualityScore: Int {
        var score = 0
        
        // Base score from completion
        if isCompleted {
            score += 40
        } else {
            score += Int(completionPercentage / 2.5)
        }
        
        // Bonus for feedback
        if feedback > 0 {
            score += Int(feedback) * 8
        }
        
        // Bonus for focus level
        if focusLevel > 0 {
            score += Int(focusLevel) * 2
        }
        
        // Bonus for calm level
        if calmLevel > 0 {
            score += Int(calmLevel) * 2
        }
        
        // Penalty for interruptions
        if wasInterrupted {
            score -= 15
        }
        
        return max(0, min(100, score))
    }
    
    /// Mood improvement (if both moods are recorded)
    var moodImprovement: Int? {
        guard let beforeMood = moodBefore,
              let afterMood = moodAfter,
              let beforeValue = Mood(rawValue: beforeMood)?.value,
              let afterValue = Mood(rawValue: afterMood)?.value else {
            return nil
        }
        return afterValue - beforeValue
    }
}

// MARK: - Background Sound Options

enum BackgroundSound: String, CaseIterable {
    case none = "none"
    case rain = "rain"
    case ocean = "ocean"
    case forest = "forest"
    case birds = "birds"
    case whiteNoise = "white_noise"
    case brownNoise = "brown_noise"
    case pinkNoise = "pink_noise"
    case tibetanBowls = "tibetan_bowls"
    case chimes = "chimes"
    case nature = "nature"
    
    var displayName: String {
        switch self {
        case .none:
            return NSLocalizedString("None", comment: "No background sound")
        case .rain:
            return NSLocalizedString("Rain", comment: "Rain sound")
        case .ocean:
            return NSLocalizedString("Ocean", comment: "Ocean sound")
        case .forest:
            return NSLocalizedString("Forest", comment: "Forest sound")
        case .birds:
            return NSLocalizedString("Birds", comment: "Birds sound")
        case .whiteNoise:
            return NSLocalizedString("White Noise", comment: "White noise")
        case .brownNoise:
            return NSLocalizedString("Brown Noise", comment: "Brown noise")
        case .pinkNoise:
            return NSLocalizedString("Pink Noise", comment: "Pink noise")
        case .tibetanBowls:
            return NSLocalizedString("Tibetan Bowls", comment: "Tibetan bowls sound")
        case .chimes:
            return NSLocalizedString("Chimes", comment: "Chimes sound")
        case .nature:
            return NSLocalizedString("Nature", comment: "Nature sounds")
        }
    }
    
    var iconName: String {
        switch self {
        case .none:
            return "speaker.slash"
        case .rain:
            return "cloud.rain"
        case .ocean:
            return "water.waves"
        case .forest:
            return "tree"
        case .birds:
            return "bird"
        case .whiteNoise, .brownNoise, .pinkNoise:
            return "waveform"
        case .tibetanBowls:
            return "circle"
        case .chimes:
            return "bell"
        case .nature:
            return "leaf"
        }
    }
    
    var audioFileName: String? {
        switch self {
        case .none:
            return nil
        default:
            return "\(rawValue).mp3"
        }
    }
}

// MARK: - Core Data Extensions

extension MeditationSession {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<MeditationSession> {
        return NSFetchRequest<MeditationSession>(entityName: "MeditationSession")
    }
    
    /// Create a new meditation session
    static func create(
        in context: NSManagedObjectContext,
        user: User,
        exercise: MeditationExercise,
        plannedDuration: Int
    ) -> MeditationSession {
        let session = MeditationSession(context: context)
        session.sessionId = UUID().uuidString
        session.user = user
        session.exercise = exercise
        session.startTime = Date()
        session.plannedDuration = Int32(plannedDuration)
        session.actualDuration = 0
        session.status = .incomplete
        session.feedback = 0
        session.soundVolume = user.backgroundSoundVolume
        session.wasInterrupted = false
        session.focusLevel = 0
        session.calmLevel = 0
        session.createdDate = Date()
        
        return session
    }
    
    /// Complete the session
    func complete(with feedback: Int? = nil, 
                  focusLevel: Int? = nil, 
                  calmLevel: Int? = nil, 
                  notes: String? = nil,
                  insights: String? = nil) {
        self.endTime = Date()
        self.status = .completed
        
        if let startTime = self.endTime {
            self.actualDuration = Int32(startTime.timeIntervalSince(self.startTime))
        }
        
        if let feedback = feedback {
            self.feedback = Int16(feedback)
        }
        
        if let focusLevel = focusLevel {
            self.focusLevel = Int16(focusLevel)
        }
        
        if let calmLevel = calmLevel {
            self.calmLevel = Int16(calmLevel)
        }
        
        if let notes = notes {
            self.notes = notes
        }
        
        if let insights = insights {
            self.insights = insights
        }
    }
    
    /// Cancel the session
    func cancel(reason: String? = nil) {
        self.endTime = Date()
        self.status = .cancelled
        self.interruptionReason = reason
        self.wasInterrupted = true
        
        if let startTime = self.endTime {
            self.actualDuration = Int32(startTime.timeIntervalSince(self.startTime))
        }
    }
    
    /// Fetch user's meditation sessions for a date range
    static func fetchSessions(for user: User,
                             from startDate: Date,
                             to endDate: Date,
                             in context: NSManagedObjectContext) -> [MeditationSession] {
        let request: NSFetchRequest<MeditationSession> = MeditationSession.fetchRequest()
        request.predicate = NSPredicate(format: "user == %@ AND startTime >= %@ AND startTime <= %@",
                                       user, startDate as NSDate, endDate as NSDate)
        request.sortDescriptors = [NSSortDescriptor(key: "startTime", ascending: false)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching meditation sessions: \(error)")
            return []
        }
    }
    
    /// Calculate meditation statistics for user
    static func calculateStatistics(for user: User, in context: NSManagedObjectContext) -> MeditationStatistics {
        let request: NSFetchRequest<MeditationSession> = MeditationSession.fetchRequest()
        request.predicate = NSPredicate(format: "user == %@", user)
        
        do {
            let sessions = try context.fetch(request)
            let completedSessions = sessions.filter { $0.isCompleted }
            
            let totalMinutes = sessions.reduce(0) { $0 + $1.durationInMinutes }
            let averageRating = completedSessions.isEmpty ? 0 :
                Double(completedSessions.reduce(0) { $0 + Int($1.feedback) }) / Double(completedSessions.count)
            
            let averageFocus = completedSessions.isEmpty ? 0 :
                Double(completedSessions.reduce(0) { $0 + Int($1.focusLevel) }) / Double(completedSessions.count)
            
            let averageCalm = completedSessions.isEmpty ? 0 :
                Double(completedSessions.reduce(0) { $0 + Int($1.calmLevel) }) / Double(completedSessions.count)
            
            let averageSessionLength = sessions.isEmpty ? 0 : totalMinutes / Double(sessions.count)
            
            // Calculate streaks
            let sortedSessions = sessions.sorted { $0.startTime > $1.startTime }
            let currentStreak = calculateCurrentStreak(sessions: sortedSessions)
            let longestStreak = calculateLongestStreak(sessions: sortedSessions)
            
            return MeditationStatistics(
                totalSessions: sessions.count,
                completedSessions: completedSessions.count,
                totalMinutes: totalMinutes,
                averageRating: averageRating,
                averageFocus: averageFocus,
                averageCalm: averageCalm,
                currentStreak: currentStreak,
                longestStreak: longestStreak,
                completionRate: sessions.isEmpty ? 0 : Double(completedSessions.count) / Double(sessions.count) * 100,
                averageSessionLength: averageSessionLength
            )
        } catch {
            print("Error calculating meditation statistics: \(error)")
            return MeditationStatistics(totalSessions: 0, completedSessions: 0, totalMinutes: 0,
                                      averageRating: 0, averageFocus: 0, averageCalm: 0,
                                      currentStreak: 0, longestStreak: 0,
                                      completionRate: 0, averageSessionLength: 0)
        }
    }
    
    /// Calculate current streak of consecutive days with completed sessions
    private static func calculateCurrentStreak(sessions: [MeditationSession]) -> Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        var streak = 0
        var currentDate = today
        
        let completedSessions = sessions.filter { $0.isCompleted }
        let sessionsByDate = Dictionary(grouping: completedSessions) { session in
            calendar.startOfDay(for: session.startTime)
        }
        
        while sessionsByDate[currentDate] != nil {
            streak += 1
            currentDate = calendar.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
        }
        
        return streak
    }
    
    /// Calculate longest streak of consecutive days
    private static func calculateLongestStreak(sessions: [MeditationSession]) -> Int {
        let calendar = Calendar.current
        let completedSessions = sessions.filter { $0.isCompleted }
        let sessionDates = Set(completedSessions.map { calendar.startOfDay(for: $0.startTime) })
        let sortedDates = sessionDates.sorted()
        
        var longestStreak = 0
        var currentStreak = 0
        var previousDate: Date?
        
        for date in sortedDates {
            if let prev = previousDate,
               calendar.dateComponents([.day], from: prev, to: date).day == 1 {
                currentStreak += 1
            } else {
                currentStreak = 1
            }
            
            longestStreak = max(longestStreak, currentStreak)
            previousDate = date
        }
        
        return longestStreak
    }
}

// MARK: - Meditation Statistics

struct MeditationStatistics {
    let totalSessions: Int
    let completedSessions: Int
    let totalMinutes: Double
    let averageRating: Double
    let averageFocus: Double
    let averageCalm: Double
    let currentStreak: Int
    let longestStreak: Int
    let completionRate: Double
    let averageSessionLength: Double
    
    var completionPercentage: Double {
        guard totalSessions > 0 else { return 0 }
        return Double(completedSessions) / Double(totalSessions) * 100
    }
}
