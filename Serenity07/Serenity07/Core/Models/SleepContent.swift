//
//  SleepContent.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData

/// Sleep content model representing sleep stories, soundscapes, and audio content
@objc(SleepContent)
public class SleepContent: NSManagedObject {
    
    // MARK: - Core Data Properties
    
    @NSManaged public var contentId: String
    @NSManaged public var title: String
    @NSManaged public var localizedTitle: String
    @NSManaged public var contentDescription: String
    @NSManaged public var narrator: String?
    @NSManaged public var duration: Int32 // Duration in seconds
    @NSManaged public var contentType: String
    @NSManaged public var category: String
    @NSManaged public var audioUrl: String
    @NSManaged public var imageUrl: String?
    @NSManaged public var thumbnailUrl: String?
    @NSManaged public var isActive: Bool
    @NSManaged public var isPremium: Bool
    @NSManaged public var sortOrder: Int32
    @NSManaged public var playCount: Int32
    @NSManaged public var averageRating: Float
    @NSManaged public var tags: String // JSON array of tags
    @NSManaged public var createdDate: Date
    @NSManaged public var updatedDate: Date
    @NSManaged public var metadata: String? // JSON metadata for additional properties
    
    // MARK: - Relationships
    
    @NSManaged public var sleepSessions: NSSet?
    @NSManaged public var userFavorites: NSSet?
    
    // MARK: - Computed Properties
    
    /// Content type as enum
    var sleepContentType: SleepContentType {
        get {
            return SleepContentType(rawValue: contentType) ?? .story
        }
        set {
            contentType = newValue.rawValue
        }
    }
    
    /// Content category as enum
    var sleepCategory: SleepCategory {
        get {
            return SleepCategory(rawValue: category) ?? .bedtimeStories
        }
        set {
            category = newValue.rawValue
        }
    }
    
    /// Duration in minutes
    var durationInMinutes: Double {
        return Double(duration) / 60.0
    }
    
    /// Formatted duration string
    var formattedDuration: String {
        let minutes = Int(durationInMinutes)
        let hours = minutes / 60
        let remainingMinutes = minutes % 60
        
        if hours > 0 {
            return "\(hours)h \(remainingMinutes)m"
        } else {
            return "\(minutes)m"
        }
    }
    
    /// Content tags as array
    var tagsList: [String] {
        guard let data = tags.data(using: .utf8),
              let tagArray = try? JSONDecoder().decode([String].self, from: data) else {
            return []
        }
        return tagArray
    }
    
    /// Metadata as dictionary
    var metadataDict: [String: Any] {
        guard let metadata = metadata,
              let data = metadata.data(using: .utf8),
              let dict = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            return [:]
        }
        return dict
    }
    
    /// Whether content is favorited by user
    func isFavorited(by user: User) -> Bool {
        guard let favorites = userFavorites?.allObjects as? [UserFavorite] else { return false }
        return favorites.contains { $0.user == user }
    }
}

// MARK: - Sleep Content Type

enum SleepContentType: String, CaseIterable {
    case story = "story"
    case soundscape = "soundscape"
    case whiteNoise = "white_noise"
    case natureSound = "nature_sound"
    case meditation = "meditation"
    case music = "music"
    case binaural = "binaural"
    
    var displayName: String {
        switch self {
        case .story:
            return NSLocalizedString("Sleep Story", comment: "Sleep story content type")
        case .soundscape:
            return NSLocalizedString("Soundscape", comment: "Soundscape content type")
        case .whiteNoise:
            return NSLocalizedString("White Noise", comment: "White noise content type")
        case .natureSound:
            return NSLocalizedString("Nature Sounds", comment: "Nature sounds content type")
        case .meditation:
            return NSLocalizedString("Sleep Meditation", comment: "Sleep meditation content type")
        case .music:
            return NSLocalizedString("Sleep Music", comment: "Sleep music content type")
        case .binaural:
            return NSLocalizedString("Binaural Beats", comment: "Binaural beats content type")
        }
    }
    
    var iconName: String {
        switch self {
        case .story:
            return "book.fill"
        case .soundscape:
            return "waveform"
        case .whiteNoise:
            return "speaker.wave.2.fill"
        case .natureSound:
            return "leaf.fill"
        case .meditation:
            return "brain.head.profile"
        case .music:
            return "music.note"
        case .binaural:
            return "headphones"
        }
    }
    
    var color: String {
        switch self {
        case .story:
            return "primaryPurple"
        case .soundscape:
            return "primaryTeal"
        case .whiteNoise:
            return "textSecondary"
        case .natureSound:
            return "success"
        case .meditation:
            return "info"
        case .music:
            return "warning"
        case .binaural:
            return "error"
        }
    }
}

// MARK: - Sleep Category

enum SleepCategory: String, CaseIterable {
    case bedtimeStories = "bedtime_stories"
    case fairyTales = "fairy_tales"
    case adventure = "adventure"
    case fantasy = "fantasy"
    case nature = "nature"
    case ocean = "ocean"
    case rain = "rain"
    case forest = "forest"
    case city = "city"
    case space = "space"
    case classical = "classical"
    case ambient = "ambient"
    case instrumental = "instrumental"
    
    var displayName: String {
        switch self {
        case .bedtimeStories:
            return NSLocalizedString("Bedtime Stories", comment: "Bedtime stories category")
        case .fairyTales:
            return NSLocalizedString("Fairy Tales", comment: "Fairy tales category")
        case .adventure:
            return NSLocalizedString("Adventure", comment: "Adventure category")
        case .fantasy:
            return NSLocalizedString("Fantasy", comment: "Fantasy category")
        case .nature:
            return NSLocalizedString("Nature", comment: "Nature category")
        case .ocean:
            return NSLocalizedString("Ocean", comment: "Ocean category")
        case .rain:
            return NSLocalizedString("Rain", comment: "Rain category")
        case .forest:
            return NSLocalizedString("Forest", comment: "Forest category")
        case .city:
            return NSLocalizedString("City", comment: "City category")
        case .space:
            return NSLocalizedString("Space", comment: "Space category")
        case .classical:
            return NSLocalizedString("Classical", comment: "Classical category")
        case .ambient:
            return NSLocalizedString("Ambient", comment: "Ambient category")
        case .instrumental:
            return NSLocalizedString("Instrumental", comment: "Instrumental category")
        }
    }
    
    var iconName: String {
        switch self {
        case .bedtimeStories:
            return "bed.double.fill"
        case .fairyTales:
            return "sparkles"
        case .adventure:
            return "map.fill"
        case .fantasy:
            return "wand.and.stars"
        case .nature:
            return "leaf.fill"
        case .ocean:
            return "water.waves"
        case .rain:
            return "cloud.rain.fill"
        case .forest:
            return "tree.fill"
        case .city:
            return "building.2.fill"
        case .space:
            return "moon.stars.fill"
        case .classical:
            return "music.note.house.fill"
        case .ambient:
            return "waveform.circle.fill"
        case .instrumental:
            return "guitars.fill"
        }
    }
}

// MARK: - Core Data Extensions

extension SleepContent {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<SleepContent> {
        return NSFetchRequest<SleepContent>(entityName: "SleepContent")
    }
    
    /// Fetch active sleep content
    static func fetchActiveContent(in context: NSManagedObjectContext) -> [SleepContent] {
        let request: NSFetchRequest<SleepContent> = SleepContent.fetchRequest()
        request.predicate = NSPredicate(format: "isActive == true")
        request.sortDescriptors = [NSSortDescriptor(key: "sortOrder", ascending: true)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching sleep content: \(error)")
            return []
        }
    }
    
    /// Fetch content by type
    static func fetchContent(type: SleepContentType, in context: NSManagedObjectContext) -> [SleepContent] {
        let request: NSFetchRequest<SleepContent> = SleepContent.fetchRequest()
        request.predicate = NSPredicate(format: "isActive == true AND contentType == %@", type.rawValue)
        request.sortDescriptors = [NSSortDescriptor(key: "sortOrder", ascending: true)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching content by type: \(error)")
            return []
        }
    }
    
    /// Fetch content by category
    static func fetchContent(category: SleepCategory, in context: NSManagedObjectContext) -> [SleepContent] {
        let request: NSFetchRequest<SleepContent> = SleepContent.fetchRequest()
        request.predicate = NSPredicate(format: "isActive == true AND category == %@", category.rawValue)
        request.sortDescriptors = [NSSortDescriptor(key: "sortOrder", ascending: true)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching content by category: \(error)")
            return []
        }
    }
    
    /// Search content by text
    static func searchContent(query: String, in context: NSManagedObjectContext) -> [SleepContent] {
        let request: NSFetchRequest<SleepContent> = SleepContent.fetchRequest()
        request.predicate = NSPredicate(format: "isActive == true AND (localizedTitle CONTAINS[cd] %@ OR contentDescription CONTAINS[cd] %@ OR narrator CONTAINS[cd] %@)", query, query, query)
        request.sortDescriptors = [NSSortDescriptor(key: "averageRating", ascending: false)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error searching sleep content: \(error)")
            return []
        }
    }
    
    /// Fetch popular content
    static func fetchPopularContent(limit: Int = 10, in context: NSManagedObjectContext) -> [SleepContent] {
        let request: NSFetchRequest<SleepContent> = SleepContent.fetchRequest()
        request.predicate = NSPredicate(format: "isActive == true")
        request.sortDescriptors = [
            NSSortDescriptor(key: "playCount", ascending: false),
            NSSortDescriptor(key: "averageRating", ascending: false)
        ]
        request.fetchLimit = limit
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching popular content: \(error)")
            return []
        }
    }
    
    /// Fetch user's favorite content
    static func fetchFavoriteContent(for user: User, in context: NSManagedObjectContext) -> [SleepContent] {
        let request: NSFetchRequest<SleepContent> = SleepContent.fetchRequest()
        request.predicate = NSPredicate(format: "isActive == true AND ANY userFavorites.user == %@", user)
        request.sortDescriptors = [NSSortDescriptor(key: "localizedTitle", ascending: true)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching favorite content: \(error)")
            return []
        }
    }
    
    /// Increment play count
    func incrementPlayCount() {
        playCount += 1
    }
    
    /// Add to user favorites
    func addToFavorites(user: User, in context: NSManagedObjectContext) {
        guard !isFavorited(by: user) else { return }
        
        let favorite = UserFavorite(context: context)
        favorite.user = user
        favorite.sleepContent = self
        favorite.createdDate = Date()
    }
    
    /// Remove from user favorites
    func removeFromFavorites(user: User, in context: NSManagedObjectContext) {
        guard let favorites = userFavorites?.allObjects as? [UserFavorite] else { return }
        
        if let favorite = favorites.first(where: { $0.user == user }) {
            context.delete(favorite)
        }
    }
}

// MARK: - User Favorite Model

@objc(UserFavorite)
public class UserFavorite: NSManagedObject {
    @NSManaged public var createdDate: Date
    @NSManaged public var user: User?
    @NSManaged public var sleepContent: SleepContent?
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<UserFavorite> {
        return NSFetchRequest<UserFavorite>(entityName: "UserFavorite")
    }
}
