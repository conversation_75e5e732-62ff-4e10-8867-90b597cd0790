//
//  MeditationExercise.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData

/// Meditation exercise model representing different meditation techniques and guided sessions
@objc(MeditationExercise)
public class MeditationExercise: NSManagedObject {
    
    // MARK: - Core Data Properties
    
    @NSManaged public var exerciseId: String
    @NSManaged public var name: String
    @NSManaged public var localizedName: String
    @NSManaged public var exerciseDescription: String
    @NSManaged public var instructions: String
    @NSManaged public var benefits: String
    @NSManaged public var difficulty: String
    @NSManaged public var category: String
    @NSManaged public var durationOptions: String // JSON array of durations in seconds
    @NSManaged public var guidanceType: String // guided, unguided, mixed
    @NSManaged public var audioUrl: String?
    @NSManaged public var backgroundSoundUrl: String?
    @NSManaged public var imageUrl: String?
    @NSManaged public var isActive: Bool
    @NSManaged public var sortOrder: Int32
    @NSManaged public var isPremium: Bool
    @NSManaged public var createdDate: Date
    @NSManaged public var updatedDate: Date
    @NSManaged public var tags: String // JSON array of tags
    
    // MARK: - Relationships
    
    @NSManaged public var meditationSessions: NSSet?
    
    // MARK: - Computed Properties
    
    /// Exercise difficulty as enum
    var difficultyLevel: MeditationDifficulty {
        get {
            return MeditationDifficulty(rawValue: difficulty) ?? .beginner
        }
        set {
            difficulty = newValue.rawValue
        }
    }
    
    /// Exercise category as enum
    var meditationCategory: MeditationCategory {
        get {
            return MeditationCategory(rawValue: category) ?? .mindfulness
        }
        set {
            category = newValue.rawValue
        }
    }
    
    /// Guidance type as enum
    var guidance: GuidanceType {
        get {
            return GuidanceType(rawValue: guidanceType) ?? .guided
        }
        set {
            guidanceType = newValue.rawValue
        }
    }
    
    /// Available duration options in seconds
    var availableDurations: [Int] {
        guard let data = durationOptions.data(using: .utf8),
              let durations = try? JSONDecoder().decode([Int].self, from: data) else {
            return [300, 600, 900, 1200] // Default: 5, 10, 15, 20 minutes
        }
        return durations
    }
    
    /// Exercise tags
    var tagsList: [String] {
        guard let data = tags.data(using: .utf8),
              let tagArray = try? JSONDecoder().decode([String].self, from: data) else {
            return []
        }
        return tagArray
    }
    
    /// Benefits as array
    var benefitsList: [String] {
        return benefits.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }
    }
    
    /// Instructions as array
    var instructionsList: [String] {
        return instructions.components(separatedBy: "\n").filter { !$0.isEmpty }
    }
}

// MARK: - Meditation Difficulty

enum MeditationDifficulty: String, CaseIterable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    
    var displayName: String {
        switch self {
        case .beginner:
            return NSLocalizedString("Beginner", comment: "Beginner difficulty")
        case .intermediate:
            return NSLocalizedString("Intermediate", comment: "Intermediate difficulty")
        case .advanced:
            return NSLocalizedString("Advanced", comment: "Advanced difficulty")
        }
    }
    
    var color: String {
        switch self {
        case .beginner:
            return "success"
        case .intermediate:
            return "warning"
        case .advanced:
            return "error"
        }
    }
}

// MARK: - Meditation Category

enum MeditationCategory: String, CaseIterable {
    case mindfulness = "mindfulness"
    case bodyScan = "body_scan"
    case lovingKindness = "loving_kindness"
    case visualization = "visualization"
    case mantra = "mantra"
    case movement = "movement"
    case sleep = "sleep"
    case focus = "focus"
    case stress = "stress"
    case anxiety = "anxiety"
    
    var displayName: String {
        switch self {
        case .mindfulness:
            return NSLocalizedString("Mindfulness", comment: "Mindfulness category")
        case .bodyScan:
            return NSLocalizedString("Body Scan", comment: "Body scan category")
        case .lovingKindness:
            return NSLocalizedString("Loving-Kindness", comment: "Loving-kindness category")
        case .visualization:
            return NSLocalizedString("Visualization", comment: "Visualization category")
        case .mantra:
            return NSLocalizedString("Mantra", comment: "Mantra category")
        case .movement:
            return NSLocalizedString("Movement", comment: "Movement category")
        case .sleep:
            return NSLocalizedString("Sleep", comment: "Sleep category")
        case .focus:
            return NSLocalizedString("Focus", comment: "Focus category")
        case .stress:
            return NSLocalizedString("Stress Relief", comment: "Stress relief category")
        case .anxiety:
            return NSLocalizedString("Anxiety", comment: "Anxiety category")
        }
    }
    
    var iconName: String {
        switch self {
        case .mindfulness:
            return "brain.head.profile"
        case .bodyScan:
            return "figure.walk"
        case .lovingKindness:
            return "heart.fill"
        case .visualization:
            return "eye.fill"
        case .mantra:
            return "speaker.wave.2.fill"
        case .movement:
            return "figure.yoga"
        case .sleep:
            return "moon.fill"
        case .focus:
            return "target"
        case .stress:
            return "leaf.fill"
        case .anxiety:
            return "shield.fill"
        }
    }
    
    var color: String {
        switch self {
        case .mindfulness:
            return "primaryTeal"
        case .bodyScan:
            return "primaryPurple"
        case .lovingKindness:
            return "error"
        case .visualization:
            return "info"
        case .mantra:
            return "warning"
        case .movement:
            return "success"
        case .sleep:
            return "primaryPurple"
        case .focus:
            return "primaryTeal"
        case .stress:
            return "success"
        case .anxiety:
            return "warning"
        }
    }
}

// MARK: - Guidance Type

enum GuidanceType: String, CaseIterable {
    case guided = "guided"
    case unguided = "unguided"
    case mixed = "mixed"
    
    var displayName: String {
        switch self {
        case .guided:
            return NSLocalizedString("Guided", comment: "Guided meditation")
        case .unguided:
            return NSLocalizedString("Unguided", comment: "Unguided meditation")
        case .mixed:
            return NSLocalizedString("Mixed", comment: "Mixed guidance meditation")
        }
    }
    
    var description: String {
        switch self {
        case .guided:
            return NSLocalizedString("Voice guidance throughout", comment: "Guided meditation description")
        case .unguided:
            return NSLocalizedString("Silent meditation with timer", comment: "Unguided meditation description")
        case .mixed:
            return NSLocalizedString("Guidance at start and end", comment: "Mixed meditation description")
        }
    }
}

// MARK: - Core Data Extensions

extension MeditationExercise {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<MeditationExercise> {
        return NSFetchRequest<MeditationExercise>(entityName: "MeditationExercise")
    }
    
    /// Fetch active meditation exercises
    static func fetchActiveExercises(in context: NSManagedObjectContext) -> [MeditationExercise] {
        let request: NSFetchRequest<MeditationExercise> = MeditationExercise.fetchRequest()
        request.predicate = NSPredicate(format: "isActive == true")
        request.sortDescriptors = [NSSortDescriptor(key: "sortOrder", ascending: true)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching meditation exercises: \(error)")
            return []
        }
    }
    
    /// Fetch exercises by category
    static func fetchExercises(category: MeditationCategory, in context: NSManagedObjectContext) -> [MeditationExercise] {
        let request: NSFetchRequest<MeditationExercise> = MeditationExercise.fetchRequest()
        request.predicate = NSPredicate(format: "isActive == true AND category == %@", category.rawValue)
        request.sortDescriptors = [NSSortDescriptor(key: "sortOrder", ascending: true)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching exercises by category: \(error)")
            return []
        }
    }
    
    /// Fetch exercises by difficulty
    static func fetchExercises(difficulty: MeditationDifficulty, in context: NSManagedObjectContext) -> [MeditationExercise] {
        let request: NSFetchRequest<MeditationExercise> = MeditationExercise.fetchRequest()
        request.predicate = NSPredicate(format: "isActive == true AND difficulty == %@", difficulty.rawValue)
        request.sortDescriptors = [NSSortDescriptor(key: "sortOrder", ascending: true)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching exercises by difficulty: \(error)")
            return []
        }
    }
    
    /// Search exercises by text
    static func searchExercises(query: String, in context: NSManagedObjectContext) -> [MeditationExercise] {
        let request: NSFetchRequest<MeditationExercise> = MeditationExercise.fetchRequest()
        request.predicate = NSPredicate(format: "isActive == true AND (localizedName CONTAINS[cd] %@ OR exerciseDescription CONTAINS[cd] %@ OR benefits CONTAINS[cd] %@)", query, query, query)
        request.sortDescriptors = [NSSortDescriptor(key: "sortOrder", ascending: true)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error searching meditation exercises: \(error)")
            return []
        }
    }
}
