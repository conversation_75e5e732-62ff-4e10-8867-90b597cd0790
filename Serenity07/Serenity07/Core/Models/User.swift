//
//  User.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData

/// User model representing app users
@objc(User)
public class User: NSManagedObject {
    
    // MARK: - Core Data Properties
    
    @NSManaged public var userId: String
    @NSManaged public var name: String
    @NSManaged public var email: String
    @NSManaged public var avatarData: Data?
    @NSManaged public var joinDate: Date
    @NSManaged public var subscriptionStatus: String
    @NSManaged public var subscriptionExpiry: Date?
    @NSManaged public var isActive: Bool
    @NSManaged public var lastLoginDate: Date?
    @NSManaged public var preferredLanguage: String
    @NSManaged public var timeZone: String
    
    // MARK: - Relationships
    
    @NSManaged public var preferences: NSSet?
    @NSManaged public var practiceSessions: NSSet?
    @NSManaged public var personalPlans: NSSet?
    
    // MARK: - Computed Properties
    
    /// User's subscription status as enum
    var subscriptionType: SubscriptionStatus {
        get {
            return SubscriptionStatus(rawValue: subscriptionStatus) ?? .free
        }
        set {
            subscriptionStatus = newValue.rawValue
        }
    }
    
    /// Check if user has active subscription
    var hasActiveSubscription: Bool {
        guard let expiry = subscriptionExpiry else { return false }
        return subscriptionType != .free && expiry > Date()
    }
    
    /// Days since user joined
    var daysSinceJoined: Int {
        Calendar.current.dateComponents([.day], from: joinDate, to: Date()).day ?? 0
    }
    
    /// User's preferred locale
    var locale: Locale {
        return Locale(identifier: preferredLanguage)
    }
}

// MARK: - User Subscription Status

enum SubscriptionStatus: String, CaseIterable {
    case free = "free"
    case monthly = "monthly"
    case yearly = "yearly"
    case lifetime = "lifetime"
    
    var displayName: String {
        switch self {
        case .free:
            return NSLocalizedString("Free", comment: "Free subscription")
        case .monthly:
            return NSLocalizedString("Monthly", comment: "Monthly subscription")
        case .yearly:
            return NSLocalizedString("Yearly", comment: "Yearly subscription")
        case .lifetime:
            return NSLocalizedString("Lifetime", comment: "Lifetime subscription")
        }
    }
    
    var isPremium: Bool {
        return self != .free
    }
}

// MARK: - User Preferences

@objc(UserPreference)
public class UserPreference: NSManagedObject {
    
    @NSManaged public var key: String
    @NSManaged public var value: String
    @NSManaged public var user: User?
    
    // MARK: - Preference Keys
    
    enum PreferenceKey: String, CaseIterable {
        case dailyGoalMinutes = "daily_goal_minutes"
        case preferredBreathingType = "preferred_breathing_type"
        case reminderTime = "reminder_time"
        case enableNotifications = "enable_notifications"
        case backgroundSoundVolume = "background_sound_volume"
        case hapticFeedback = "haptic_feedback"
        case darkMode = "dark_mode"
        case autoPlayNext = "auto_play_next"
        case sessionDuration = "session_duration"
        case breathingSpeed = "breathing_speed"
        
        var defaultValue: String {
            switch self {
            case .dailyGoalMinutes:
                return "10"
            case .preferredBreathingType:
                return "equal_breathing"
            case .reminderTime:
                return "20:00"
            case .enableNotifications:
                return "true"
            case .backgroundSoundVolume:
                return "0.5"
            case .hapticFeedback:
                return "true"
            case .darkMode:
                return "true"
            case .autoPlayNext:
                return "false"
            case .sessionDuration:
                return "300" // 5 minutes in seconds
            case .breathingSpeed:
                return "normal"
            }
        }
        
        var displayName: String {
            switch self {
            case .dailyGoalMinutes:
                return NSLocalizedString("Daily Goal (Minutes)", comment: "Daily goal preference")
            case .preferredBreathingType:
                return NSLocalizedString("Preferred Breathing Type", comment: "Breathing type preference")
            case .reminderTime:
                return NSLocalizedString("Reminder Time", comment: "Reminder time preference")
            case .enableNotifications:
                return NSLocalizedString("Enable Notifications", comment: "Notifications preference")
            case .backgroundSoundVolume:
                return NSLocalizedString("Background Sound Volume", comment: "Volume preference")
            case .hapticFeedback:
                return NSLocalizedString("Haptic Feedback", comment: "Haptic feedback preference")
            case .darkMode:
                return NSLocalizedString("Dark Mode", comment: "Dark mode preference")
            case .autoPlayNext:
                return NSLocalizedString("Auto Play Next", comment: "Auto play preference")
            case .sessionDuration:
                return NSLocalizedString("Default Session Duration", comment: "Session duration preference")
            case .breathingSpeed:
                return NSLocalizedString("Breathing Speed", comment: "Breathing speed preference")
            }
        }
    }
}

// MARK: - Core Data Extensions

extension User {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<User> {
        return NSFetchRequest<User>(entityName: "User")
    }
    
    /// Create a new user with default values
    static func create(in context: NSManagedObjectContext, 
                      userId: String, 
                      name: String, 
                      email: String) -> User {
        let user = User(context: context)
        user.userId = userId
        user.name = name
        user.email = email
        user.joinDate = Date()
        user.subscriptionStatus = SubscriptionStatus.free.rawValue
        user.isActive = true
        user.preferredLanguage = Locale.current.identifier
        user.timeZone = TimeZone.current.identifier
        
        // Create default preferences
        for preferenceKey in UserPreference.PreferenceKey.allCases {
            let preference = UserPreference(context: context)
            preference.key = preferenceKey.rawValue
            preference.value = preferenceKey.defaultValue
            preference.user = user
        }
        
        return user
    }
    
    /// Get user preference value
    func getPreference(_ key: UserPreference.PreferenceKey) -> String {
        let preferences = self.preferences as? Set<UserPreference> ?? []
        return preferences.first { $0.key == key.rawValue }?.value ?? key.defaultValue
    }
    
    /// Set user preference value
    func setPreference(_ key: UserPreference.PreferenceKey, value: String, in context: NSManagedObjectContext) {
        let preferences = self.preferences as? Set<UserPreference> ?? []
        
        if let existingPreference = preferences.first(where: { $0.key == key.rawValue }) {
            existingPreference.value = value
        } else {
            let newPreference = UserPreference(context: context)
            newPreference.key = key.rawValue
            newPreference.value = value
            newPreference.user = self
        }
    }
    
    /// Get daily goal in minutes
    var dailyGoalMinutes: Int {
        return Int(getPreference(.dailyGoalMinutes)) ?? 10
    }
    
    /// Check if notifications are enabled
    var notificationsEnabled: Bool {
        return getPreference(.enableNotifications) == "true"
    }
    
    /// Get background sound volume (0.0 - 1.0)
    var backgroundSoundVolume: Float {
        return Float(getPreference(.backgroundSoundVolume)) ?? 0.5
    }
    
    /// Check if haptic feedback is enabled
    var hapticFeedbackEnabled: Bool {
        return getPreference(.hapticFeedback) == "true"
    }
}

extension UserPreference {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<UserPreference> {
        return NSFetchRequest<UserPreference>(entityName: "UserPreference")
    }
}
