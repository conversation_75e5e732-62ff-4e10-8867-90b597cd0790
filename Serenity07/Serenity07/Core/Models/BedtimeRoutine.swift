//
//  BedtimeRoutine.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData

/// Bedtime routine model representing user's customizable sleep preparation sequences
@objc(BedtimeRoutine)
public class BedtimeRoutine: NSManagedObject {
    
    // MARK: - Core Data Properties
    
    @NSManaged public var routineId: String
    @NSManaged public var name: String
    @NSManaged public var routineDescription: String?
    @NSManaged public var isActive: Bool
    @NSManaged public var isDefault: Bool
    @NSManaged public var estimatedDuration: Int32 // Duration in minutes
    @NSManaged public var reminderTime: Date? // Time to send reminder
    @NSManaged public var isReminderEnabled: Bool
    @NSManaged public var createdDate: Date
    @NSManaged public var updatedDate: Date
    @NSManaged public var lastUsedDate: Date?
    @NSManaged public var usageCount: Int32
    
    // MARK: - Relationships
    
    @NSManaged public var user: User?
    @NSManaged public var steps: NSOrderedSet?
    @NSManaged public var sessions: NSSet?
    
    // MARK: - Computed Properties
    
    /// Routine steps as array
    var routineSteps: [BedtimeRoutineStep] {
        return steps?.array as? [BedtimeRoutineStep] ?? []
    }
    
    /// Total estimated duration in minutes
    var totalDuration: Int {
        return routineSteps.reduce(0) { $0 + Int($1.duration) }
    }
    
    /// Formatted duration string
    var formattedDuration: String {
        let minutes = totalDuration
        let hours = minutes / 60
        let remainingMinutes = minutes % 60
        
        if hours > 0 {
            return "\(hours)h \(remainingMinutes)m"
        } else {
            return "\(minutes)m"
        }
    }
    
    /// Whether routine has been used recently
    var isRecentlyUsed: Bool {
        guard let lastUsed = lastUsedDate else { return false }
        return Calendar.current.isDate(lastUsed, inSameDayAs: Date())
    }
    
    /// Completion rate based on recent sessions
    var completionRate: Double {
        guard let sessions = sessions?.allObjects as? [BedtimeRoutineSession] else { return 0 }
        let recentSessions = sessions.filter { 
            Calendar.current.isDate($0.startTime, inSameDayAs: Date()) ||
            Calendar.current.isDateInYesterday($0.startTime)
        }
        
        guard !recentSessions.isEmpty else { return 0 }
        
        let completedSessions = recentSessions.filter { $0.isCompleted }
        return Double(completedSessions.count) / Double(recentSessions.count) * 100
    }
}

// MARK: - Bedtime Routine Step

@objc(BedtimeRoutineStep)
public class BedtimeRoutineStep: NSManagedObject {
    
    @NSManaged public var stepId: String
    @NSManaged public var title: String
    @NSManaged public var stepDescription: String?
    @NSManaged public var stepType: String
    @NSManaged public var duration: Int32 // Duration in minutes
    @NSManaged public var isOptional: Bool
    @NSManaged public var sortOrder: Int32
    @NSManaged public var iconName: String?
    @NSManaged public var audioUrl: String?
    @NSManaged public var instructions: String?
    @NSManaged public var isCompleted: Bool
    @NSManaged public var completedDate: Date?
    @NSManaged public var createdDate: Date
    
    // MARK: - Relationships
    
    @NSManaged public var routine: BedtimeRoutine?
    
    // MARK: - Computed Properties
    
    /// Step type as enum
    var routineStepType: BedtimeStepType {
        get {
            return BedtimeStepType(rawValue: stepType) ?? .custom
        }
        set {
            stepType = newValue.rawValue
        }
    }
    
    /// Formatted duration
    var formattedDuration: String {
        let minutes = Int(duration)
        return "\(minutes) min"
    }
    
    /// Whether step is overdue
    var isOverdue: Bool {
        guard let completedDate = completedDate else { return false }
        let expectedDuration = TimeInterval(duration * 60)
        return Date().timeIntervalSince(completedDate) > expectedDuration
    }
}

// MARK: - Bedtime Step Type

enum BedtimeStepType: String, CaseIterable {
    case meditation = "meditation"
    case breathing = "breathing"
    case reading = "reading"
    case journaling = "journaling"
    case stretching = "stretching"
    case tea = "tea"
    case bath = "bath"
    case music = "music"
    case aromatherapy = "aromatherapy"
    case dimLights = "dim_lights"
    case noScreens = "no_screens"
    case custom = "custom"
    
    var displayName: String {
        switch self {
        case .meditation:
            return NSLocalizedString("Meditation", comment: "Meditation step")
        case .breathing:
            return NSLocalizedString("Breathing Exercise", comment: "Breathing step")
        case .reading:
            return NSLocalizedString("Reading", comment: "Reading step")
        case .journaling:
            return NSLocalizedString("Journaling", comment: "Journaling step")
        case .stretching:
            return NSLocalizedString("Gentle Stretching", comment: "Stretching step")
        case .tea:
            return NSLocalizedString("Herbal Tea", comment: "Tea step")
        case .bath:
            return NSLocalizedString("Warm Bath", comment: "Bath step")
        case .music:
            return NSLocalizedString("Calming Music", comment: "Music step")
        case .aromatherapy:
            return NSLocalizedString("Aromatherapy", comment: "Aromatherapy step")
        case .dimLights:
            return NSLocalizedString("Dim Lights", comment: "Dim lights step")
        case .noScreens:
            return NSLocalizedString("No Screens", comment: "No screens step")
        case .custom:
            return NSLocalizedString("Custom Activity", comment: "Custom step")
        }
    }
    
    var iconName: String {
        switch self {
        case .meditation:
            return "brain.head.profile"
        case .breathing:
            return "lungs.fill"
        case .reading:
            return "book.fill"
        case .journaling:
            return "pencil"
        case .stretching:
            return "figure.yoga"
        case .tea:
            return "cup.and.saucer.fill"
        case .bath:
            return "bathtub.fill"
        case .music:
            return "music.note"
        case .aromatherapy:
            return "leaf.fill"
        case .dimLights:
            return "lightbulb.fill"
        case .noScreens:
            return "iphone.slash"
        case .custom:
            return "star.fill"
        }
    }
    
    var defaultDuration: Int {
        switch self {
        case .meditation:
            return 10
        case .breathing:
            return 5
        case .reading:
            return 20
        case .journaling:
            return 10
        case .stretching:
            return 15
        case .tea:
            return 10
        case .bath:
            return 20
        case .music:
            return 15
        case .aromatherapy:
            return 5
        case .dimLights:
            return 2
        case .noScreens:
            return 30
        case .custom:
            return 10
        }
    }
    
    var description: String {
        switch self {
        case .meditation:
            return NSLocalizedString("Practice mindfulness or guided meditation", comment: "Meditation description")
        case .breathing:
            return NSLocalizedString("Perform calming breathing exercises", comment: "Breathing description")
        case .reading:
            return NSLocalizedString("Read a book or calming content", comment: "Reading description")
        case .journaling:
            return NSLocalizedString("Write down thoughts or gratitude", comment: "Journaling description")
        case .stretching:
            return NSLocalizedString("Do gentle stretches or yoga", comment: "Stretching description")
        case .tea:
            return NSLocalizedString("Enjoy a warm, caffeine-free beverage", comment: "Tea description")
        case .bath:
            return NSLocalizedString("Take a warm, relaxing bath", comment: "Bath description")
        case .music:
            return NSLocalizedString("Listen to soothing music", comment: "Music description")
        case .aromatherapy:
            return NSLocalizedString("Use essential oils or candles", comment: "Aromatherapy description")
        case .dimLights:
            return NSLocalizedString("Reduce lighting in your space", comment: "Dim lights description")
        case .noScreens:
            return NSLocalizedString("Avoid phones, TV, and computers", comment: "No screens description")
        case .custom:
            return NSLocalizedString("Your personalized bedtime activity", comment: "Custom description")
        }
    }
}

// MARK: - Bedtime Routine Session

@objc(BedtimeRoutineSession)
public class BedtimeRoutineSession: NSManagedObject {
    
    @NSManaged public var sessionId: String
    @NSManaged public var startTime: Date
    @NSManaged public var endTime: Date?
    @NSManaged public var isCompleted: Bool
    @NSManaged public var completedSteps: Int32
    @NSManaged public var totalSteps: Int32
    @NSManaged public var notes: String?
    @NSManaged public var sleepQuality: Int16 // 1-5 rating
    @NSManaged public var createdDate: Date
    
    // MARK: - Relationships
    
    @NSManaged public var user: User?
    @NSManaged public var routine: BedtimeRoutine?
    
    // MARK: - Computed Properties
    
    /// Session duration in minutes
    var durationInMinutes: Double {
        guard let endTime = endTime else {
            return Date().timeIntervalSince(startTime) / 60.0
        }
        return endTime.timeIntervalSince(startTime) / 60.0
    }
    
    /// Completion percentage
    var completionPercentage: Double {
        guard totalSteps > 0 else { return 0 }
        return Double(completedSteps) / Double(totalSteps) * 100
    }
    
    /// Whether session was completed today
    var isToday: Bool {
        return Calendar.current.isDateInToday(startTime)
    }
}

// MARK: - Core Data Extensions

extension BedtimeRoutine {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<BedtimeRoutine> {
        return NSFetchRequest<BedtimeRoutine>(entityName: "BedtimeRoutine")
    }
    
    /// Create a default bedtime routine
    static func createDefaultRoutine(for user: User, in context: NSManagedObjectContext) -> BedtimeRoutine {
        let routine = BedtimeRoutine(context: context)
        routine.routineId = UUID().uuidString
        routine.name = NSLocalizedString("My Bedtime Routine", comment: "Default routine name")
        routine.routineDescription = NSLocalizedString("A personalized routine to help you wind down", comment: "Default routine description")
        routine.user = user
        routine.isActive = true
        routine.isDefault = true
        routine.isReminderEnabled = false
        routine.usageCount = 0
        routine.createdDate = Date()
        routine.updatedDate = Date()
        
        // Add default steps
        let defaultSteps = [
            (BedtimeStepType.dimLights, false),
            (BedtimeStepType.noScreens, false),
            (BedtimeStepType.tea, true),
            (BedtimeStepType.reading, true),
            (BedtimeStepType.breathing, false)
        ]
        
        for (index, (stepType, isOptional)) in defaultSteps.enumerated() {
            let step = BedtimeRoutineStep(context: context)
            step.stepId = UUID().uuidString
            step.title = stepType.displayName
            step.stepDescription = stepType.description
            step.routineStepType = stepType
            step.duration = Int32(stepType.defaultDuration)
            step.isOptional = isOptional
            step.sortOrder = Int32(index)
            step.iconName = stepType.iconName
            step.isCompleted = false
            step.createdDate = Date()
            step.routine = routine
        }
        
        return routine
    }
    
    /// Fetch user's routines
    static func fetchUserRoutines(for user: User, in context: NSManagedObjectContext) -> [BedtimeRoutine] {
        let request: NSFetchRequest<BedtimeRoutine> = BedtimeRoutine.fetchRequest()
        request.predicate = NSPredicate(format: "user == %@ AND isActive == true", user)
        request.sortDescriptors = [
            NSSortDescriptor(key: "isDefault", ascending: false),
            NSSortDescriptor(key: "lastUsedDate", ascending: false),
            NSSortDescriptor(key: "createdDate", ascending: false)
        ]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching user routines: \(error)")
            return []
        }
    }
    
    /// Start a new routine session
    func startSession(for user: User, in context: NSManagedObjectContext) -> BedtimeRoutineSession {
        let session = BedtimeRoutineSession(context: context)
        session.sessionId = UUID().uuidString
        session.user = user
        session.routine = self
        session.startTime = Date()
        session.isCompleted = false
        session.completedSteps = 0
        session.totalSteps = Int32(routineSteps.count)
        session.sleepQuality = 0
        session.createdDate = Date()
        
        // Reset step completion status
        for step in routineSteps {
            step.isCompleted = false
            step.completedDate = nil
        }
        
        // Update routine usage
        self.lastUsedDate = Date()
        self.usageCount += 1
        
        return session
    }
    
    /// Complete a routine step
    func completeStep(at index: Int, session: BedtimeRoutineSession) {
        guard index < routineSteps.count else { return }
        
        let step = routineSteps[index]
        step.isCompleted = true
        step.completedDate = Date()
        
        session.completedSteps += 1
        
        // Check if routine is complete
        if session.completedSteps >= session.totalSteps {
            session.isCompleted = true
            session.endTime = Date()
        }
    }
    
    /// Add a custom step
    func addStep(title: String, description: String?, type: BedtimeStepType, duration: Int, isOptional: Bool, in context: NSManagedObjectContext) {
        let step = BedtimeRoutineStep(context: context)
        step.stepId = UUID().uuidString
        step.title = title
        step.stepDescription = description
        step.routineStepType = type
        step.duration = Int32(duration)
        step.isOptional = isOptional
        step.sortOrder = Int32(routineSteps.count)
        step.iconName = type.iconName
        step.isCompleted = false
        step.createdDate = Date()
        step.routine = self
        
        self.updatedDate = Date()
    }
    
    /// Remove a step
    func removeStep(at index: Int, in context: NSManagedObjectContext) {
        guard index < routineSteps.count else { return }
        
        let step = routineSteps[index]
        context.delete(step)
        
        // Update sort order for remaining steps
        for (newIndex, remainingStep) in routineSteps.enumerated() {
            if newIndex != index {
                remainingStep.sortOrder = Int32(newIndex > index ? newIndex - 1 : newIndex)
            }
        }
        
        self.updatedDate = Date()
    }
}

extension BedtimeRoutineStep {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<BedtimeRoutineStep> {
        return NSFetchRequest<BedtimeRoutineStep>(entityName: "BedtimeRoutineStep")
    }
}

extension BedtimeRoutineSession {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<BedtimeRoutineSession> {
        return NSFetchRequest<BedtimeRoutineSession>(entityName: "BedtimeRoutineSession")
    }
}
