//
//  Achievement.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData

/// Achievement model representing user accomplishments and milestones
@objc(Achievement)
public class Achievement: NSManagedObject {
    
    // MARK: - Core Data Properties
    
    @NSManaged public var achievementId: String
    @NSManaged public var title: String
    @NSManaged public var localizedTitle: String
    @NSManaged public var achievementDescription: String
    @NSManaged public var localizedDescription: String
    @NSManaged public var category: String
    @NSManaged public var type: String
    @NSManaged public var iconName: String
    @NSManaged public var badgeColor: String
    @NSManaged public var targetValue: Int32
    @NSManaged public var currentValue: Int32
    @NSManaged public var isUnlocked: Bool
    @NSManaged public var unlockedDate: Date?
    @NSManaged public var sortOrder: Int32
    @NSManaged public var isHidden: Bool // Hidden until unlocked
    @NSManaged public var points: Int32 // Points awarded for achievement
    @NSManaged public var rarity: String // common, rare, epic, legendary
    @NSManaged public var createdDate: Date
    @NSManaged public var updatedDate: Date
    
    // MARK: - Relationships
    
    @NSManaged public var userAchievements: NSSet?
    
    // MARK: - Computed Properties
    
    /// Achievement category as enum
    var achievementCategory: AchievementCategory {
        get {
            return AchievementCategory(rawValue: category) ?? .practice
        }
        set {
            category = newValue.rawValue
        }
    }
    
    /// Achievement type as enum
    var achievementType: AchievementType {
        get {
            return AchievementType(rawValue: type) ?? .milestone
        }
        set {
            type = newValue.rawValue
        }
    }
    
    /// Achievement rarity as enum
    var achievementRarity: AchievementRarity {
        get {
            return AchievementRarity(rawValue: rarity) ?? .common
        }
        set {
            rarity = newValue.rawValue
        }
    }
    
    /// Progress percentage (0-100)
    var progressPercentage: Double {
        guard targetValue > 0 else { return 0 }
        return min(100, Double(currentValue) / Double(targetValue) * 100)
    }
    
    /// Whether achievement is completed
    var isCompleted: Bool {
        return currentValue >= targetValue
    }
    
    /// Remaining value to complete achievement
    var remainingValue: Int32 {
        return max(0, targetValue - currentValue)
    }
    
    /// Formatted progress string
    var progressString: String {
        return "\(currentValue)/\(targetValue)"
    }
    
    /// Days since unlocked
    var daysSinceUnlocked: Int? {
        guard let unlockedDate = unlockedDate else { return nil }
        return Calendar.current.dateComponents([.day], from: unlockedDate, to: Date()).day
    }
}

// MARK: - Achievement Category

enum AchievementCategory: String, CaseIterable {
    case practice = "practice"
    case streak = "streak"
    case time = "time"
    case exploration = "exploration"
    case social = "social"
    case special = "special"
    
    var displayName: String {
        switch self {
        case .practice:
            return NSLocalizedString("Practice", comment: "Practice achievements")
        case .streak:
            return NSLocalizedString("Consistency", comment: "Streak achievements")
        case .time:
            return NSLocalizedString("Time", comment: "Time-based achievements")
        case .exploration:
            return NSLocalizedString("Exploration", comment: "Exploration achievements")
        case .social:
            return NSLocalizedString("Social", comment: "Social achievements")
        case .special:
            return NSLocalizedString("Special", comment: "Special achievements")
        }
    }
    
    var iconName: String {
        switch self {
        case .practice:
            return "target"
        case .streak:
            return "flame.fill"
        case .time:
            return "clock.fill"
        case .exploration:
            return "map.fill"
        case .social:
            return "person.2.fill"
        case .special:
            return "star.fill"
        }
    }
    
    var color: String {
        switch self {
        case .practice:
            return "primaryTeal"
        case .streak:
            return "warning"
        case .time:
            return "info"
        case .exploration:
            return "success"
        case .social:
            return "primaryPurple"
        case .special:
            return "error"
        }
    }
}

// MARK: - Achievement Type

enum AchievementType: String, CaseIterable {
    case milestone = "milestone"
    case streak = "streak"
    case exploration = "exploration"
    case perfectionist = "perfectionist"
    case dedication = "dedication"
    case variety = "variety"
    
    var displayName: String {
        switch self {
        case .milestone:
            return NSLocalizedString("Milestone", comment: "Milestone achievement")
        case .streak:
            return NSLocalizedString("Streak", comment: "Streak achievement")
        case .exploration:
            return NSLocalizedString("Explorer", comment: "Exploration achievement")
        case .perfectionist:
            return NSLocalizedString("Perfectionist", comment: "Perfectionist achievement")
        case .dedication:
            return NSLocalizedString("Dedication", comment: "Dedication achievement")
        case .variety:
            return NSLocalizedString("Variety", comment: "Variety achievement")
        }
    }
}

// MARK: - Achievement Rarity

enum AchievementRarity: String, CaseIterable {
    case common = "common"
    case rare = "rare"
    case epic = "epic"
    case legendary = "legendary"
    
    var displayName: String {
        switch self {
        case .common:
            return NSLocalizedString("Common", comment: "Common rarity")
        case .rare:
            return NSLocalizedString("Rare", comment: "Rare rarity")
        case .epic:
            return NSLocalizedString("Epic", comment: "Epic rarity")
        case .legendary:
            return NSLocalizedString("Legendary", comment: "Legendary rarity")
        }
    }
    
    var color: String {
        switch self {
        case .common:
            return "textSecondary"
        case .rare:
            return "info"
        case .epic:
            return "primaryPurple"
        case .legendary:
            return "warning"
        }
    }
    
    var points: Int {
        switch self {
        case .common:
            return 10
        case .rare:
            return 25
        case .epic:
            return 50
        case .legendary:
            return 100
        }
    }
}

// MARK: - User Achievement

@objc(UserAchievement)
public class UserAchievement: NSManagedObject {
    
    @NSManaged public var userId: String
    @NSManaged public var achievementId: String
    @NSManaged public var currentProgress: Int32
    @NSManaged public var isUnlocked: Bool
    @NSManaged public var unlockedDate: Date?
    @NSManaged public var notificationSent: Bool
    @NSManaged public var createdDate: Date
    @NSManaged public var updatedDate: Date
    
    // MARK: - Relationships
    
    @NSManaged public var user: User?
    @NSManaged public var achievement: Achievement?
    
    // MARK: - Computed Properties
    
    /// Progress percentage
    var progressPercentage: Double {
        guard let achievement = achievement, achievement.targetValue > 0 else { return 0 }
        return min(100, Double(currentProgress) / Double(achievement.targetValue) * 100)
    }
    
    /// Whether achievement is completed
    var isCompleted: Bool {
        guard let achievement = achievement else { return false }
        return currentProgress >= achievement.targetValue
    }
    
    /// Remaining progress needed
    var remainingProgress: Int32 {
        guard let achievement = achievement else { return 0 }
        return max(0, achievement.targetValue - currentProgress)
    }
}

// MARK: - Core Data Extensions

extension Achievement {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<Achievement> {
        return NSFetchRequest<Achievement>(entityName: "Achievement")
    }
    
    /// Fetch all achievements
    static func fetchAllAchievements(in context: NSManagedObjectContext) -> [Achievement] {
        let request: NSFetchRequest<Achievement> = Achievement.fetchRequest()
        request.sortDescriptors = [
            NSSortDescriptor(key: "category", ascending: true),
            NSSortDescriptor(key: "sortOrder", ascending: true)
        ]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching achievements: \(error)")
            return []
        }
    }
    
    /// Fetch achievements by category
    static func fetchAchievements(category: AchievementCategory, in context: NSManagedObjectContext) -> [Achievement] {
        let request: NSFetchRequest<Achievement> = Achievement.fetchRequest()
        request.predicate = NSPredicate(format: "category == %@", category.rawValue)
        request.sortDescriptors = [NSSortDescriptor(key: "sortOrder", ascending: true)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching achievements by category: \(error)")
            return []
        }
    }
    
    /// Check if user has unlocked this achievement
    func isUnlocked(by user: User, in context: NSManagedObjectContext) -> Bool {
        let request: NSFetchRequest<UserAchievement> = UserAchievement.fetchRequest()
        request.predicate = NSPredicate(format: "user == %@ AND achievement == %@ AND isUnlocked == true", user, self)
        
        do {
            let userAchievements = try context.fetch(request)
            return !userAchievements.isEmpty
        } catch {
            print("Error checking achievement unlock status: \(error)")
            return false
        }
    }
    
    /// Get user's progress for this achievement
    func getProgress(for user: User, in context: NSManagedObjectContext) -> UserAchievement? {
        let request: NSFetchRequest<UserAchievement> = UserAchievement.fetchRequest()
        request.predicate = NSPredicate(format: "user == %@ AND achievement == %@", user, self)
        
        do {
            return try context.fetch(request).first
        } catch {
            print("Error fetching user achievement progress: \(error)")
            return nil
        }
    }
    
    /// Update user's progress for this achievement
    func updateProgress(for user: User, newValue: Int32, in context: NSManagedObjectContext) -> Bool {
        let userAchievement = getProgress(for: user, in: context) ?? {
            let newUserAchievement = UserAchievement(context: context)
            newUserAchievement.user = user
            newUserAchievement.achievement = self
            newUserAchievement.userId = user.userId
            newUserAchievement.achievementId = achievementId
            newUserAchievement.currentProgress = 0
            newUserAchievement.isUnlocked = false
            newUserAchievement.notificationSent = false
            newUserAchievement.createdDate = Date()
            return newUserAchievement
        }()
        
        let oldProgress = userAchievement.currentProgress
        userAchievement.currentProgress = max(userAchievement.currentProgress, newValue)
        userAchievement.updatedDate = Date()
        
        // Check if achievement was just unlocked
        if !userAchievement.isUnlocked && userAchievement.currentProgress >= targetValue {
            userAchievement.isUnlocked = true
            userAchievement.unlockedDate = Date()
            return true // Achievement unlocked
        }
        
        return false // No unlock
    }
}

extension UserAchievement {
    
    @nonobjc public class func fetchRequest() -> NSFetchRequest<UserAchievement> {
        return NSFetchRequest<UserAchievement>(entityName: "UserAchievement")
    }
    
    /// Fetch user's achievements
    static func fetchUserAchievements(for user: User, in context: NSManagedObjectContext) -> [UserAchievement] {
        let request: NSFetchRequest<UserAchievement> = UserAchievement.fetchRequest()
        request.predicate = NSPredicate(format: "user == %@", user)
        request.sortDescriptors = [
            NSSortDescriptor(key: "isUnlocked", ascending: false),
            NSSortDescriptor(key: "unlockedDate", ascending: false)
        ]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching user achievements: \(error)")
            return []
        }
    }
    
    /// Fetch unlocked achievements for user
    static func fetchUnlockedAchievements(for user: User, in context: NSManagedObjectContext) -> [UserAchievement] {
        let request: NSFetchRequest<UserAchievement> = UserAchievement.fetchRequest()
        request.predicate = NSPredicate(format: "user == %@ AND isUnlocked == true", user)
        request.sortDescriptors = [NSSortDescriptor(key: "unlockedDate", ascending: false)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching unlocked achievements: \(error)")
            return []
        }
    }
    
    /// Fetch recent achievements (last 30 days)
    static func fetchRecentAchievements(for user: User, in context: NSManagedObjectContext) -> [UserAchievement] {
        let thirtyDaysAgo = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        
        let request: NSFetchRequest<UserAchievement> = UserAchievement.fetchRequest()
        request.predicate = NSPredicate(format: "user == %@ AND isUnlocked == true AND unlockedDate >= %@", user, thirtyDaysAgo as NSDate)
        request.sortDescriptors = [NSSortDescriptor(key: "unlockedDate", ascending: false)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching recent achievements: \(error)")
            return []
        }
    }
    
    /// Calculate total achievement points for user
    static func calculateTotalPoints(for user: User, in context: NSManagedObjectContext) -> Int {
        let unlockedAchievements = fetchUnlockedAchievements(for: user, in: context)
        return unlockedAchievements.reduce(0) { total, userAchievement in
            return total + Int(userAchievement.achievement?.points ?? 0)
        }
    }
    
    /// Get achievement completion percentage for user
    static func getCompletionPercentage(for user: User, in context: NSManagedObjectContext) -> Double {
        let allAchievements = Achievement.fetchAllAchievements(in: context)
        let unlockedCount = fetchUnlockedAchievements(for: user, in: context).count
        
        guard !allAchievements.isEmpty else { return 0 }
        return Double(unlockedCount) / Double(allAchievements.count) * 100
    }
}
