//
//  CoreDataManager.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData
import Combine

/// Core Data manager for handling data persistence
class CoreDataManager: ObservableObject {
    
    static let shared = CoreDataManager()
    
    // MARK: - Core Data Stack
    
    lazy var persistentContainer: NSPersistentContainer = {
        // Create container with programmatic model
        let model = SerenityDataModel.createModel()
        let container = NSPersistentContainer(name: "SerenityDataModel", managedObjectModel: model)

        container.loadPersistentStores { _, error in
            if let error = error as NSError? {
                // In production, you should handle this error appropriately
                fatalError("Core Data error: \(error), \(error.userInfo)")
            }
        }

        container.viewContext.automaticallyMergesChangesFromParent = true
        container.viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy

        return container
    }()
    
    var viewContext: NSManagedObjectContext {
        return persistentContainer.viewContext
    }
    
    var backgroundContext: NSManagedObjectContext {
        return persistentContainer.newBackgroundContext()
    }
    
    // MARK: - Initialization
    
    private init() {
        setupInitialData()
    }
    
    // MARK: - Core Data Operations
    
    /// Save the view context
    func save() {
        let context = viewContext
        
        if context.hasChanges {
            do {
                try context.save()
            } catch {
                print("Save error: \(error)")
            }
        }
    }
    
    /// Save a background context
    func saveBackground(_ context: NSManagedObjectContext) {
        if context.hasChanges {
            do {
                try context.save()
            } catch {
                print("Background save error: \(error)")
            }
        }
    }
    
    /// Perform a background task
    func performBackgroundTask<T>(_ block: @escaping (NSManagedObjectContext) throws -> T) -> Future<T, Error> {
        return Future { promise in
            let context = self.backgroundContext
            context.perform {
                do {
                    let result = try block(context)
                    self.saveBackground(context)
                    promise(.success(result))
                } catch {
                    promise(.failure(error))
                }
            }
        }
    }
    
    // MARK: - Default Data Loading

    /// Load default data into the database
    func loadDefaultData() {
        let context = viewContext

        // Load default breathing exercises
        BreathingExerciseLibrary.loadDefaultExercises(into: context)

        // Load default meditation exercises
        MeditationExerciseLibrary.loadDefaultExercises(into: context)

        // Load default sleep content
        SleepContentLibrary.loadDefaultContent(into: context)

        // Load default achievements
        AchievementLibrary.loadDefaultAchievements(into: context)

        // Save context
        do {
            try context.save()
            print("Default data loaded successfully")
        } catch {
            print("Error loading default data: \(error)")
        }
    }

    // MARK: - User Management

    /// Get current user (for now, we'll use a single user system)
    func getCurrentUser() -> User? {
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.fetchLimit = 1

        do {
            let users = try viewContext.fetch(request)
            return users.first
        } catch {
            print("Error fetching current user: \(error)")
            return nil
        }
    }
    
    /// Create or get the default user
    func getOrCreateDefaultUser() -> User {
        if let existingUser = getCurrentUser() {
            return existingUser
        }
        
        let user = User.create(
            in: viewContext,
            userId: UUID().uuidString,
            name: "User",
            email: "<EMAIL>"
        )
        
        save()
        return user
    }
    
    // MARK: - Breathing Exercises
    
    /// Fetch all active breathing exercises
    func fetchBreathingExercises() -> [BreathingExercise] {
        return BreathingExercise.fetchActiveExercises(in: viewContext)
    }
    
    /// Fetch breathing exercises by category
    func fetchBreathingExercises(category: ExerciseCategory) -> [BreathingExercise] {
        return BreathingExercise.fetchExercises(category: category, in: viewContext)
    }
    
    /// Get breathing exercise by ID
    func getBreathingExercise(id: String) -> BreathingExercise? {
        let request: NSFetchRequest<BreathingExercise> = BreathingExercise.fetchRequest()
        request.predicate = NSPredicate(format: "exerciseId == %@", id)
        request.fetchLimit = 1
        
        do {
            return try viewContext.fetch(request).first
        } catch {
            print("Error fetching breathing exercise: \(error)")
            return nil
        }
    }
    
    // MARK: - Practice Sessions
    
    /// Create a new practice session
    func createPracticeSession(exerciseId: String, plannedDuration: Int) -> PracticeSession? {
        guard let user = getCurrentUser(),
              let exercise = getBreathingExercise(id: exerciseId) else {
            return nil
        }
        
        let session = PracticeSession.create(
            in: viewContext,
            user: user,
            exercise: exercise,
            plannedDuration: plannedDuration
        )
        
        save()
        return session
    }
    
    /// Fetch recent practice sessions
    func fetchRecentSessions(limit: Int = 10) -> [PracticeSession] {
        guard let user = getCurrentUser() else { return [] }
        
        let request: NSFetchRequest<PracticeSession> = PracticeSession.fetchRequest()
        request.predicate = NSPredicate(format: "user == %@", user)
        request.sortDescriptors = [NSSortDescriptor(key: "startTime", ascending: false)]
        request.fetchLimit = limit
        
        do {
            return try viewContext.fetch(request)
        } catch {
            print("Error fetching recent sessions: \(error)")
            return []
        }
    }
    
    /// Fetch sessions for today
    func fetchTodaySessions() -> [PracticeSession] {
        guard let user = getCurrentUser() else { return [] }
        
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: Date())
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay) ?? Date()
        
        return PracticeSession.fetchSessions(
            for: user,
            from: startOfDay,
            to: endOfDay,
            in: viewContext
        )
    }
    
    /// Get user statistics
    func getUserStatistics() -> SessionStatistics? {
        guard let user = getCurrentUser() else { return nil }
        return PracticeSession.calculateStatistics(for: user, in: viewContext)
    }
    
    // MARK: - Data Setup
    
    /// Setup initial data if needed
    private func setupInitialData() {
        // Check if we already have breathing exercises
        let exerciseCount = fetchBreathingExercises().count

        if exerciseCount == 0 {
            // Create comprehensive breathing exercise library
            BreathingExerciseLibrary.shared.createAllExercises(in: viewContext)
            save()
        }

        // Ensure we have a default user
        _ = getOrCreateDefaultUser()
    }
    
    // MARK: - Data Migration and Cleanup
    
    /// Delete all data (for testing purposes)
    func deleteAllData() {
        let entities = ["User", "UserPreference", "BreathingExercise", "PracticeSession"]
        
        for entity in entities {
            let request = NSFetchRequest<NSFetchRequestResult>(entityName: entity)
            let deleteRequest = NSBatchDeleteRequest(fetchRequest: request)
            
            do {
                try viewContext.execute(deleteRequest)
            } catch {
                print("Error deleting \(entity): \(error)")
            }
        }
        
        save()
    }
    
    /// Export user data (for backup or data portability)
    func exportUserData() -> [String: Any] {
        guard let user = getCurrentUser() else { return [:] }
        
        let sessions = fetchRecentSessions(limit: 1000)
        let statistics = getUserStatistics()
        
        return [
            "user": [
                "name": user.name,
                "email": user.email,
                "joinDate": user.joinDate,
                "subscriptionStatus": user.subscriptionStatus
            ],
            "sessions": sessions.map { session in
                [
                    "exerciseId": session.exercise?.exerciseId ?? "",
                    "startTime": session.startTime,
                    "duration": session.actualDuration,
                    "completed": session.isCompleted,
                    "feedback": session.feedback
                ]
            },
            "statistics": [
                "totalSessions": statistics?.totalSessions ?? 0,
                "totalMinutes": statistics?.totalMinutes ?? 0.0,
                "currentStreak": statistics?.currentStreak ?? 0
            ]
        ]
    }
}

// MARK: - Convenience Extensions

extension CoreDataManager {
    
    /// Check if user has completed any sessions today
    var hasCompletedSessionToday: Bool {
        let todaySessions = fetchTodaySessions()
        return todaySessions.contains { $0.isCompleted }
    }
    
    /// Get today's total practice time in minutes
    var todaysPracticeTime: Double {
        let todaySessions = fetchTodaySessions()
        return todaySessions.reduce(0) { $0 + $1.durationInMinutes }
    }
    
    /// Get current streak
    var currentStreak: Int {
        return getUserStatistics()?.currentStreak ?? 0
    }
    
    /// Get total practice time
    var totalPracticeTime: Double {
        return getUserStatistics()?.totalMinutes ?? 0
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let coreDataDidSave = Notification.Name("CoreDataDidSave")
    static let userDataDidUpdate = Notification.Name("UserDataDidUpdate")
}
