//
//  SerenityDataModel.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData

/// Programmatic Core Data model definition
/// This file defines the Core Data model programmatically since we can't create .xcdatamodeld files
class SerenityDataModel {
    
    static func createModel() -> NSManagedObjectModel {
        let model = NSManagedObjectModel()
        
        // Create entities
        let userEntity = createUserEntity()
        let userPreferenceEntity = createUserPreferenceEntity()
        let breathingExerciseEntity = createBreathingExerciseEntity()
        let practiceSessionEntity = createPracticeSessionEntity()
        let personalPlanEntity = createPersonalPlanEntity()
        
        // Set up relationships
        setupRelationships(
            userEntity: userEntity,
            userPreferenceEntity: userPreferenceEntity,
            breathingExerciseEntity: breathingExerciseEntity,
            practiceSessionEntity: practiceSessionEntity,
            personalPlanEntity: personalPlanEntity
        )
        
        // Add entities to model
        model.entities = [
            userEntity,
            userPreferenceEntity,
            breathingExerciseEntity,
            practiceSessionEntity,
            personalPlanEntity
        ]
        
        return model
    }
    
    // MARK: - Entity Creation
    
    private static func createUserEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "User"
        entity.managedObjectClassName = "User"
        
        let attributes = [
            createAttribute(name: "userId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "name", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "email", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "avatarData", type: .binaryDataAttributeType, isOptional: true),
            createAttribute(name: "joinDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "subscriptionStatus", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "subscriptionExpiry", type: .dateAttributeType, isOptional: true),
            createAttribute(name: "isActive", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "lastLoginDate", type: .dateAttributeType, isOptional: true),
            createAttribute(name: "preferredLanguage", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "timeZone", type: .stringAttributeType, isOptional: false)
        ]
        
        entity.properties = attributes
        return entity
    }
    
    private static func createUserPreferenceEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "UserPreference"
        entity.managedObjectClassName = "UserPreference"
        
        let attributes = [
            createAttribute(name: "key", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "value", type: .stringAttributeType, isOptional: false)
        ]
        
        entity.properties = attributes
        return entity
    }
    
    private static func createBreathingExerciseEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "BreathingExercise"
        entity.managedObjectClassName = "BreathingExercise"
        
        let attributes = [
            createAttribute(name: "exerciseId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "name", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "localizedName", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "exerciseDescription", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "instructions", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "benefits", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "difficulty", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "category", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "durationOptions", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "breathingPattern", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "imageUrl", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "audioUrl", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "isActive", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "sortOrder", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "isPremium", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "createdDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "updatedDate", type: .dateAttributeType, isOptional: false)
        ]
        
        entity.properties = attributes
        return entity
    }
    
    private static func createPracticeSessionEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "PracticeSession"
        entity.managedObjectClassName = "PracticeSession"
        
        let attributes = [
            createAttribute(name: "sessionId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "startTime", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "endTime", type: .dateAttributeType, isOptional: true),
            createAttribute(name: "plannedDuration", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "actualDuration", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "completionStatus", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "feedback", type: .integer16AttributeType, isOptional: false),
            createAttribute(name: "notes", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "breathingSpeed", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "backgroundSound", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "soundVolume", type: .floatAttributeType, isOptional: false),
            createAttribute(name: "wasInterrupted", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "interruptionReason", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "heartRateBefore", type: .integer16AttributeType, isOptional: false),
            createAttribute(name: "heartRateAfter", type: .integer16AttributeType, isOptional: false),
            createAttribute(name: "moodBefore", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "moodAfter", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "createdDate", type: .dateAttributeType, isOptional: false)
        ]
        
        entity.properties = attributes
        return entity
    }
    
    private static func createPersonalPlanEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "PersonalPlan"
        entity.managedObjectClassName = "PersonalPlan"
        
        let attributes = [
            createAttribute(name: "planId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "goal", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "startDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "endDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "dailyExercises", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "completionRate", type: .floatAttributeType, isOptional: false),
            createAttribute(name: "isActive", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "createdDate", type: .dateAttributeType, isOptional: false)
        ]
        
        entity.properties = attributes
        return entity
    }
    
    // MARK: - Helper Methods
    
    private static func createAttribute(name: String, type: NSAttributeType, isOptional: Bool) -> NSAttributeDescription {
        let attribute = NSAttributeDescription()
        attribute.name = name
        attribute.attributeType = type
        attribute.isOptional = isOptional
        
        // Set default values for certain types
        switch type {
        case .booleanAttributeType:
            attribute.defaultValue = false
        case .integer16AttributeType, .integer32AttributeType:
            attribute.defaultValue = 0
        case .floatAttributeType, .doubleAttributeType:
            attribute.defaultValue = 0.0
        default:
            break
        }
        
        return attribute
    }
    
    private static func setupRelationships(
        userEntity: NSEntityDescription,
        userPreferenceEntity: NSEntityDescription,
        breathingExerciseEntity: NSEntityDescription,
        practiceSessionEntity: NSEntityDescription,
        personalPlanEntity: NSEntityDescription
    ) {
        // User -> UserPreferences (one-to-many)
        let userToPreferences = NSRelationshipDescription()
        userToPreferences.name = "preferences"
        userToPreferences.destinationEntity = userPreferenceEntity
        userToPreferences.minCount = 0
        userToPreferences.maxCount = 0 // 0 means unlimited
        userToPreferences.deleteRule = .cascadeDeleteRule
        
        let preferenceToUser = NSRelationshipDescription()
        preferenceToUser.name = "user"
        preferenceToUser.destinationEntity = userEntity
        preferenceToUser.minCount = 1
        preferenceToUser.maxCount = 1
        preferenceToUser.deleteRule = .nullifyDeleteRule
        
        userToPreferences.inverseRelationship = preferenceToUser
        preferenceToUser.inverseRelationship = userToPreferences
        
        // User -> PracticeSessions (one-to-many)
        let userToSessions = NSRelationshipDescription()
        userToSessions.name = "practiceSessions"
        userToSessions.destinationEntity = practiceSessionEntity
        userToSessions.minCount = 0
        userToSessions.maxCount = 0
        userToSessions.deleteRule = .cascadeDeleteRule
        
        let sessionToUser = NSRelationshipDescription()
        sessionToUser.name = "user"
        sessionToUser.destinationEntity = userEntity
        sessionToUser.minCount = 1
        sessionToUser.maxCount = 1
        sessionToUser.deleteRule = .nullifyDeleteRule
        
        userToSessions.inverseRelationship = sessionToUser
        sessionToUser.inverseRelationship = userToSessions
        
        // User -> PersonalPlans (one-to-many)
        let userToPlans = NSRelationshipDescription()
        userToPlans.name = "personalPlans"
        userToPlans.destinationEntity = personalPlanEntity
        userToPlans.minCount = 0
        userToPlans.maxCount = 0
        userToPlans.deleteRule = .cascadeDeleteRule
        
        let planToUser = NSRelationshipDescription()
        planToUser.name = "user"
        planToUser.destinationEntity = userEntity
        planToUser.minCount = 1
        planToUser.maxCount = 1
        planToUser.deleteRule = .nullifyDeleteRule
        
        userToPlans.inverseRelationship = planToUser
        planToUser.inverseRelationship = userToPlans
        
        // BreathingExercise -> PracticeSessions (one-to-many)
        let exerciseToSessions = NSRelationshipDescription()
        exerciseToSessions.name = "practiceSessions"
        exerciseToSessions.destinationEntity = practiceSessionEntity
        exerciseToSessions.minCount = 0
        exerciseToSessions.maxCount = 0
        exerciseToSessions.deleteRule = .nullifyDeleteRule
        
        let sessionToExercise = NSRelationshipDescription()
        sessionToExercise.name = "exercise"
        sessionToExercise.destinationEntity = breathingExerciseEntity
        sessionToExercise.minCount = 1
        sessionToExercise.maxCount = 1
        sessionToExercise.deleteRule = .nullifyDeleteRule
        
        exerciseToSessions.inverseRelationship = sessionToExercise
        sessionToExercise.inverseRelationship = exerciseToSessions
        
        // Add relationships to entities
        userEntity.properties.append(contentsOf: [userToPreferences, userToSessions, userToPlans])
        userPreferenceEntity.properties.append(preferenceToUser)
        breathingExerciseEntity.properties.append(exerciseToSessions)
        practiceSessionEntity.properties.append(contentsOf: [sessionToUser, sessionToExercise])
        personalPlanEntity.properties.append(planToUser)
    }
}
