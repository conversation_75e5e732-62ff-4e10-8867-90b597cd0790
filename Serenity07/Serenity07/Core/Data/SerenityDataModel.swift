//
//  SerenityDataModel.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import CoreData

/// Programmatic Core Data model definition
/// This file defines the Core Data model programmatically since we can't create .xcdatamodeld files
class SerenityDataModel {
    
    static func createModel() -> NSManagedObjectModel {
        let model = NSManagedObjectModel()
        
        // Create entities
        let userEntity = createUserEntity()
        let userPreferenceEntity = createUserPreferenceEntity()
        let breathingExerciseEntity = createBreathingExerciseEntity()
        let practiceSessionEntity = createPracticeSessionEntity()
        let personalPlanEntity = createPersonalPlanEntity()
        let meditationExerciseEntity = createMeditationExerciseEntity()
        let meditationSessionEntity = createMeditationSessionEntity()
        let sleepContentEntity = createSleepContentEntity()
        let bedtimeRoutineEntity = createBedtimeRoutineEntity()
        let bedtimeRoutineStepEntity = createBedtimeRoutineStepEntity()
        let bedtimeRoutineSessionEntity = createBedtimeRoutineSessionEntity()
        let achievementEntity = createAchievementEntity()
        let userAchievementEntity = createUserAchievementEntity()
        let userFavoriteEntity = createUserFavoriteEntity()
        let userStatisticsEntity = createUserStatisticsEntity()

        // Set up relationships
        setupRelationships(
            userEntity: userEntity,
            userPreferenceEntity: userPreferenceEntity,
            breathingExerciseEntity: breathingExerciseEntity,
            practiceSessionEntity: practiceSessionEntity,
            personalPlanEntity: personalPlanEntity,
            meditationExerciseEntity: meditationExerciseEntity,
            meditationSessionEntity: meditationSessionEntity,
            sleepContentEntity: sleepContentEntity,
            bedtimeRoutineEntity: bedtimeRoutineEntity,
            bedtimeRoutineStepEntity: bedtimeRoutineStepEntity,
            bedtimeRoutineSessionEntity: bedtimeRoutineSessionEntity,
            achievementEntity: achievementEntity,
            userAchievementEntity: userAchievementEntity,
            userFavoriteEntity: userFavoriteEntity,
            userStatisticsEntity: userStatisticsEntity
        )

        // Add entities to model
        model.entities = [
            userEntity,
            userPreferenceEntity,
            breathingExerciseEntity,
            practiceSessionEntity,
            personalPlanEntity,
            meditationExerciseEntity,
            meditationSessionEntity,
            sleepContentEntity,
            bedtimeRoutineEntity,
            bedtimeRoutineStepEntity,
            bedtimeRoutineSessionEntity,
            achievementEntity,
            userAchievementEntity,
            userFavoriteEntity,
            userStatisticsEntity
        ]
        
        return model
    }
    
    // MARK: - Entity Creation
    
    private static func createUserEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "User"
        entity.managedObjectClassName = "User"
        
        let attributes = [
            createAttribute(name: "userId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "name", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "email", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "avatarData", type: .binaryDataAttributeType, isOptional: true),
            createAttribute(name: "joinDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "subscriptionStatus", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "subscriptionExpiry", type: .dateAttributeType, isOptional: true),
            createAttribute(name: "isActive", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "lastLoginDate", type: .dateAttributeType, isOptional: true),
            createAttribute(name: "preferredLanguage", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "timeZone", type: .stringAttributeType, isOptional: false)
        ]
        
        entity.properties = attributes
        return entity
    }
    
    private static func createUserPreferenceEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "UserPreference"
        entity.managedObjectClassName = "UserPreference"
        
        let attributes = [
            createAttribute(name: "key", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "value", type: .stringAttributeType, isOptional: false)
        ]
        
        entity.properties = attributes
        return entity
    }
    
    private static func createBreathingExerciseEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "BreathingExercise"
        entity.managedObjectClassName = "BreathingExercise"
        
        let attributes = [
            createAttribute(name: "exerciseId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "name", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "localizedName", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "exerciseDescription", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "instructions", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "benefits", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "difficulty", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "category", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "durationOptions", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "breathingPattern", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "imageUrl", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "audioUrl", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "isActive", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "sortOrder", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "isPremium", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "createdDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "updatedDate", type: .dateAttributeType, isOptional: false)
        ]
        
        entity.properties = attributes
        return entity
    }
    
    private static func createPracticeSessionEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "PracticeSession"
        entity.managedObjectClassName = "PracticeSession"
        
        let attributes = [
            createAttribute(name: "sessionId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "startTime", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "endTime", type: .dateAttributeType, isOptional: true),
            createAttribute(name: "plannedDuration", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "actualDuration", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "completionStatus", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "feedback", type: .integer16AttributeType, isOptional: false),
            createAttribute(name: "notes", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "breathingSpeed", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "backgroundSound", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "soundVolume", type: .floatAttributeType, isOptional: false),
            createAttribute(name: "wasInterrupted", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "interruptionReason", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "heartRateBefore", type: .integer16AttributeType, isOptional: false),
            createAttribute(name: "heartRateAfter", type: .integer16AttributeType, isOptional: false),
            createAttribute(name: "moodBefore", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "moodAfter", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "createdDate", type: .dateAttributeType, isOptional: false)
        ]
        
        entity.properties = attributes
        return entity
    }
    
    private static func createPersonalPlanEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "PersonalPlan"
        entity.managedObjectClassName = "PersonalPlan"
        
        let attributes = [
            createAttribute(name: "planId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "goal", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "startDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "endDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "dailyExercises", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "completionRate", type: .floatAttributeType, isOptional: false),
            createAttribute(name: "isActive", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "createdDate", type: .dateAttributeType, isOptional: false)
        ]
        
        entity.properties = attributes
        return entity
    }
    
    // MARK: - Helper Methods
    
    private static func createAttribute(name: String, type: NSAttributeType, isOptional: Bool) -> NSAttributeDescription {
        let attribute = NSAttributeDescription()
        attribute.name = name
        attribute.attributeType = type
        attribute.isOptional = isOptional
        
        // Set default values for certain types
        switch type {
        case .booleanAttributeType:
            attribute.defaultValue = false
        case .integer16AttributeType, .integer32AttributeType:
            attribute.defaultValue = 0
        case .floatAttributeType, .doubleAttributeType:
            attribute.defaultValue = 0.0
        default:
            break
        }
        
        return attribute
    }
    
    private static func setupRelationships(
        userEntity: NSEntityDescription,
        userPreferenceEntity: NSEntityDescription,
        breathingExerciseEntity: NSEntityDescription,
        practiceSessionEntity: NSEntityDescription,
        personalPlanEntity: NSEntityDescription,
        meditationExerciseEntity: NSEntityDescription,
        meditationSessionEntity: NSEntityDescription,
        sleepContentEntity: NSEntityDescription,
        bedtimeRoutineEntity: NSEntityDescription,
        bedtimeRoutineStepEntity: NSEntityDescription,
        bedtimeRoutineSessionEntity: NSEntityDescription,
        achievementEntity: NSEntityDescription,
        userAchievementEntity: NSEntityDescription,
        userFavoriteEntity: NSEntityDescription,
        userStatisticsEntity: NSEntityDescription
    ) {
        // User -> UserPreferences (one-to-many)
        let userToPreferences = NSRelationshipDescription()
        userToPreferences.name = "preferences"
        userToPreferences.destinationEntity = userPreferenceEntity
        userToPreferences.minCount = 0
        userToPreferences.maxCount = 0 // 0 means unlimited
        userToPreferences.deleteRule = .cascadeDeleteRule
        
        let preferenceToUser = NSRelationshipDescription()
        preferenceToUser.name = "user"
        preferenceToUser.destinationEntity = userEntity
        preferenceToUser.minCount = 1
        preferenceToUser.maxCount = 1
        preferenceToUser.deleteRule = .nullifyDeleteRule
        
        userToPreferences.inverseRelationship = preferenceToUser
        preferenceToUser.inverseRelationship = userToPreferences
        
        // User -> PracticeSessions (one-to-many)
        let userToSessions = NSRelationshipDescription()
        userToSessions.name = "practiceSessions"
        userToSessions.destinationEntity = practiceSessionEntity
        userToSessions.minCount = 0
        userToSessions.maxCount = 0
        userToSessions.deleteRule = .cascadeDeleteRule
        
        let sessionToUser = NSRelationshipDescription()
        sessionToUser.name = "user"
        sessionToUser.destinationEntity = userEntity
        sessionToUser.minCount = 1
        sessionToUser.maxCount = 1
        sessionToUser.deleteRule = .nullifyDeleteRule
        
        userToSessions.inverseRelationship = sessionToUser
        sessionToUser.inverseRelationship = userToSessions
        
        // User -> PersonalPlans (one-to-many)
        let userToPlans = NSRelationshipDescription()
        userToPlans.name = "personalPlans"
        userToPlans.destinationEntity = personalPlanEntity
        userToPlans.minCount = 0
        userToPlans.maxCount = 0
        userToPlans.deleteRule = .cascadeDeleteRule
        
        let planToUser = NSRelationshipDescription()
        planToUser.name = "user"
        planToUser.destinationEntity = userEntity
        planToUser.minCount = 1
        planToUser.maxCount = 1
        planToUser.deleteRule = .nullifyDeleteRule
        
        userToPlans.inverseRelationship = planToUser
        planToUser.inverseRelationship = userToPlans
        
        // BreathingExercise -> PracticeSessions (one-to-many)
        let exerciseToSessions = NSRelationshipDescription()
        exerciseToSessions.name = "practiceSessions"
        exerciseToSessions.destinationEntity = practiceSessionEntity
        exerciseToSessions.minCount = 0
        exerciseToSessions.maxCount = 0
        exerciseToSessions.deleteRule = .nullifyDeleteRule
        
        let sessionToExercise = NSRelationshipDescription()
        sessionToExercise.name = "exercise"
        sessionToExercise.destinationEntity = breathingExerciseEntity
        sessionToExercise.minCount = 1
        sessionToExercise.maxCount = 1
        sessionToExercise.deleteRule = .nullifyDeleteRule
        
        exerciseToSessions.inverseRelationship = sessionToExercise
        sessionToExercise.inverseRelationship = exerciseToSessions
        
        // Add relationships to entities
        userEntity.properties.append(contentsOf: [userToPreferences, userToSessions, userToPlans])
        userPreferenceEntity.properties.append(preferenceToUser)
        breathingExerciseEntity.properties.append(exerciseToSessions)
        practiceSessionEntity.properties.append(contentsOf: [sessionToUser, sessionToExercise])
        personalPlanEntity.properties.append(planToUser)

        // TODO: Add relationships for new entities when needed
    }

    // MARK: - New Entity Creation Methods

    private static func createUserStatisticsEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "UserStatistics"
        entity.managedObjectClassName = "UserStatistics"

        entity.properties = [
            createAttribute(name: "userId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "totalSessions", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "totalMinutes", type: .doubleAttributeType, isOptional: false),
            createAttribute(name: "currentStreak", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "longestStreak", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "breathingSessions", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "breathingMinutes", type: .doubleAttributeType, isOptional: false),
            createAttribute(name: "meditationSessions", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "meditationMinutes", type: .doubleAttributeType, isOptional: false),
            createAttribute(name: "sleepSessions", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "sleepMinutes", type: .doubleAttributeType, isOptional: false),
            createAttribute(name: "averageSessionLength", type: .doubleAttributeType, isOptional: false),
            createAttribute(name: "favoriteExerciseType", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "preferredSessionTime", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "weeklyGoal", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "monthlyGoal", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "lastSessionDate", type: .dateAttributeType, isOptional: true),
            createAttribute(name: "firstSessionDate", type: .dateAttributeType, isOptional: true),
            createAttribute(name: "createdDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "updatedDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "weeklyStats", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "monthlyStats", type: .stringAttributeType, isOptional: true)
        ]

        return entity
    }

    private static func createMeditationExerciseEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "MeditationExercise"
        entity.managedObjectClassName = "MeditationExercise"

        entity.properties = [
            createAttribute(name: "exerciseId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "name", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "localizedName", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "exerciseDescription", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "instructions", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "benefits", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "difficulty", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "category", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "durationOptions", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "guidanceType", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "audioUrl", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "backgroundSoundUrl", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "imageUrl", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "isActive", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "sortOrder", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "isPremium", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "createdDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "updatedDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "tags", type: .stringAttributeType, isOptional: false)
        ]

        return entity
    }

    private static func createMeditationSessionEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "MeditationSession"
        entity.managedObjectClassName = "MeditationSession"

        entity.properties = [
            createAttribute(name: "sessionId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "startTime", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "endTime", type: .dateAttributeType, isOptional: true),
            createAttribute(name: "plannedDuration", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "actualDuration", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "completionStatus", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "feedback", type: .integer16AttributeType, isOptional: false),
            createAttribute(name: "notes", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "backgroundSound", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "soundVolume", type: .floatAttributeType, isOptional: false),
            createAttribute(name: "wasInterrupted", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "interruptionReason", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "moodBefore", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "moodAfter", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "focusLevel", type: .integer16AttributeType, isOptional: false),
            createAttribute(name: "calmLevel", type: .integer16AttributeType, isOptional: false),
            createAttribute(name: "insights", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "createdDate", type: .dateAttributeType, isOptional: false)
        ]

        return entity
    }

    private static func createSleepContentEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "SleepContent"
        entity.managedObjectClassName = "SleepContent"

        entity.properties = [
            createAttribute(name: "contentId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "title", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "localizedTitle", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "contentDescription", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "narrator", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "duration", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "contentType", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "category", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "audioUrl", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "imageUrl", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "thumbnailUrl", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "isActive", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "isPremium", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "sortOrder", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "playCount", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "averageRating", type: .floatAttributeType, isOptional: false),
            createAttribute(name: "tags", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "createdDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "updatedDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "metadata", type: .stringAttributeType, isOptional: true)
        ]

        return entity
    }

    private static func createBedtimeRoutineEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "BedtimeRoutine"
        entity.managedObjectClassName = "BedtimeRoutine"

        entity.properties = [
            createAttribute(name: "routineId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "name", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "routineDescription", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "isActive", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "isDefault", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "estimatedDuration", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "reminderTime", type: .dateAttributeType, isOptional: true),
            createAttribute(name: "isReminderEnabled", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "createdDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "updatedDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "lastUsedDate", type: .dateAttributeType, isOptional: true),
            createAttribute(name: "usageCount", type: .integer32AttributeType, isOptional: false)
        ]

        return entity
    }

    private static func createBedtimeRoutineStepEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "BedtimeRoutineStep"
        entity.managedObjectClassName = "BedtimeRoutineStep"

        entity.properties = [
            createAttribute(name: "stepId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "title", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "stepDescription", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "stepType", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "duration", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "isOptional", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "sortOrder", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "iconName", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "audioUrl", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "instructions", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "isCompleted", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "completedDate", type: .dateAttributeType, isOptional: true),
            createAttribute(name: "createdDate", type: .dateAttributeType, isOptional: false)
        ]

        return entity
    }

    private static func createBedtimeRoutineSessionEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "BedtimeRoutineSession"
        entity.managedObjectClassName = "BedtimeRoutineSession"

        entity.properties = [
            createAttribute(name: "sessionId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "startTime", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "endTime", type: .dateAttributeType, isOptional: true),
            createAttribute(name: "isCompleted", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "completedSteps", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "totalSteps", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "notes", type: .stringAttributeType, isOptional: true),
            createAttribute(name: "sleepQuality", type: .integer16AttributeType, isOptional: false),
            createAttribute(name: "createdDate", type: .dateAttributeType, isOptional: false)
        ]

        return entity
    }

    private static func createAchievementEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "Achievement"
        entity.managedObjectClassName = "Achievement"

        entity.properties = [
            createAttribute(name: "achievementId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "title", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "localizedTitle", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "achievementDescription", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "localizedDescription", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "category", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "type", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "iconName", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "badgeColor", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "targetValue", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "currentValue", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "isUnlocked", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "unlockedDate", type: .dateAttributeType, isOptional: true),
            createAttribute(name: "sortOrder", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "isHidden", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "points", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "rarity", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "createdDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "updatedDate", type: .dateAttributeType, isOptional: false)
        ]

        return entity
    }

    private static func createUserAchievementEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "UserAchievement"
        entity.managedObjectClassName = "UserAchievement"

        entity.properties = [
            createAttribute(name: "userId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "achievementId", type: .stringAttributeType, isOptional: false),
            createAttribute(name: "currentProgress", type: .integer32AttributeType, isOptional: false),
            createAttribute(name: "isUnlocked", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "unlockedDate", type: .dateAttributeType, isOptional: true),
            createAttribute(name: "notificationSent", type: .booleanAttributeType, isOptional: false),
            createAttribute(name: "createdDate", type: .dateAttributeType, isOptional: false),
            createAttribute(name: "updatedDate", type: .dateAttributeType, isOptional: false)
        ]

        return entity
    }

    private static func createUserFavoriteEntity() -> NSEntityDescription {
        let entity = NSEntityDescription()
        entity.name = "UserFavorite"
        entity.managedObjectClassName = "UserFavorite"

        entity.properties = [
            createAttribute(name: "createdDate", type: .dateAttributeType, isOptional: false)
        ]

        return entity
    }
}
