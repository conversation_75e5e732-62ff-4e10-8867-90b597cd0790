//
//  NavigationRouter.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI
import Combine

/// Navigation router for handling app-wide navigation
class NavigationRouter: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var currentTab: Tab = .home
    @Published var navigationPath = NavigationPath()
    @Published var presentedSheet: SheetDestination?
    @Published var presentedFullScreen: FullScreenDestination?
    @Published var showAlert = false
    @Published var alertConfig: AlertConfig?
    
    // MARK: - Navigation Methods
    
    /// Navigate to a specific tab
    func navigateToTab(_ tab: Tab) {
        currentTab = tab
    }
    
    /// Push a new view onto the navigation stack
    func push(_ destination: NavigationDestination) {
        navigationPath.append(destination)
    }
    
    /// Pop the current view from the navigation stack
    func pop() {
        if !navigationPath.isEmpty {
            navigationPath.removeLast()
        }
    }
    
    /// Pop to root view
    func popToRoot() {
        navigationPath = NavigationPath()
    }
    
    /// Present a sheet
    func presentSheet(_ destination: SheetDestination) {
        presentedSheet = destination
    }
    
    /// Dismiss the current sheet
    func dismissSheet() {
        presentedSheet = nil
    }
    
    /// Present a full screen cover
    func presentFullScreen(_ destination: FullScreenDestination) {
        presentedFullScreen = destination
    }
    
    /// Dismiss the current full screen cover
    func dismissFullScreen() {
        presentedFullScreen = nil
    }
    
    /// Show an alert
    func showAlert(_ config: AlertConfig) {
        alertConfig = config
        showAlert = true
    }
    
    /// Dismiss the current alert
    func dismissAlert() {
        showAlert = false
        alertConfig = nil
    }
}

// MARK: - Navigation Destinations

enum NavigationDestination: Hashable {
    case breathingExerciseDetail(BreathingExercise)
    case breathingSession(BreathingExercise, duration: Int)
    case meditationDetail(String) // meditation ID
    case sleepDetail(String) // sleep content ID
    case statistics
    case personalPlanDetail(PersonalPlan)
    case createPersonalPlan
    case settings
    case profile
    case subscription
    case exerciseLibrary(category: ExerciseCategory?)
    case sessionHistory
    case achievements
    case about
    case help
    case feedback
}

enum SheetDestination: Identifiable {
    case sessionComplete(PracticeSession)
    case sessionFeedback(PracticeSession)
    case exerciseOptions(BreathingExercise)
    case languageSelection
    case notificationSettings
    case soundSettings
    case subscriptionOffer
    case userProfile
    case createPlan
    case planOptions(PersonalPlan)
    
    var id: String {
        switch self {
        case .sessionComplete:
            return "sessionComplete"
        case .sessionFeedback:
            return "sessionFeedback"
        case .exerciseOptions:
            return "exerciseOptions"
        case .languageSelection:
            return "languageSelection"
        case .notificationSettings:
            return "notificationSettings"
        case .soundSettings:
            return "soundSettings"
        case .subscriptionOffer:
            return "subscriptionOffer"
        case .userProfile:
            return "userProfile"
        case .createPlan:
            return "createPlan"
        case .planOptions:
            return "planOptions"
        }
    }
}

enum FullScreenDestination: Identifiable {
    case breathingSession(BreathingExercise, duration: Int)
    case onboarding
    case login
    case subscription
    
    var id: String {
        switch self {
        case .breathingSession:
            return "breathingSession"
        case .onboarding:
            return "onboarding"
        case .login:
            return "login"
        case .subscription:
            return "subscription"
        }
    }
}

// MARK: - Alert Configuration

struct AlertConfig {
    let title: String
    let message: String?
    let primaryButton: AlertButton?
    let secondaryButton: AlertButton?
    
    struct AlertButton {
        let title: String
        let action: () -> Void
        let style: ButtonStyle
        
        enum ButtonStyle {
            case `default`
            case cancel
            case destructive
        }
    }
    
    init(
        title: String,
        message: String? = nil,
        primaryButton: AlertButton? = nil,
        secondaryButton: AlertButton? = nil
    ) {
        self.title = title
        self.message = message
        self.primaryButton = primaryButton
        self.secondaryButton = secondaryButton
    }
    
    // MARK: - Convenience Initializers
    
    static func error(
        title: String = "Error",
        message: String,
        onDismiss: (() -> Void)? = nil
    ) -> AlertConfig {
        AlertConfig(
            title: title,
            message: message,
            primaryButton: AlertButton(
                title: "OK",
                action: onDismiss ?? {},
                style: .default
            )
        )
    }
    
    static func confirmation(
        title: String,
        message: String,
        confirmTitle: String = "Confirm",
        cancelTitle: String = "Cancel",
        onConfirm: @escaping () -> Void,
        onCancel: (() -> Void)? = nil
    ) -> AlertConfig {
        AlertConfig(
            title: title,
            message: message,
            primaryButton: AlertButton(
                title: confirmTitle,
                action: onConfirm,
                style: .default
            ),
            secondaryButton: AlertButton(
                title: cancelTitle,
                action: onCancel ?? {},
                style: .cancel
            )
        )
    }
    
    static func destructive(
        title: String,
        message: String,
        destructiveTitle: String = "Delete",
        cancelTitle: String = "Cancel",
        onDestruct: @escaping () -> Void,
        onCancel: (() -> Void)? = nil
    ) -> AlertConfig {
        AlertConfig(
            title: title,
            message: message,
            primaryButton: AlertButton(
                title: destructiveTitle,
                action: onDestruct,
                style: .destructive
            ),
            secondaryButton: AlertButton(
                title: cancelTitle,
                action: onCancel ?? {},
                style: .cancel
            )
        )
    }
}

// MARK: - Navigation View Modifier

struct NavigationRouterModifier: ViewModifier {
    @ObservedObject var router: NavigationRouter
    
    func body(content: Content) -> some View {
        content
            .sheet(item: $router.presentedSheet) { destination in
                SheetView(destination: destination, router: router)
            }
            .fullScreenCover(item: $router.presentedFullScreen) { destination in
                FullScreenView(destination: destination, router: router)
            }
            .alert(
                router.alertConfig?.title ?? "",
                isPresented: $router.showAlert,
                presenting: router.alertConfig
            ) { config in
                if let primaryButton = config.primaryButton {
                    Button(primaryButton.title, action: primaryButton.action)
                }
                
                if let secondaryButton = config.secondaryButton {
                    Button(secondaryButton.title, action: secondaryButton.action)
                }
            } message: { config in
                if let message = config.message {
                    Text(message)
                }
            }
    }
}

extension View {
    func navigationRouter(_ router: NavigationRouter) -> some View {
        modifier(NavigationRouterModifier(router: router))
    }
}

// MARK: - Sheet View

struct SheetView: View {
    let destination: SheetDestination
    let router: NavigationRouter
    
    var body: some View {
        NavigationView {
            Group {
                switch destination {
                case .sessionComplete(let session):
                    SessionCompleteView(session: session, router: router)
                case .sessionFeedback(let session):
                    SessionFeedbackView(session: session, router: router)
                case .exerciseOptions(let exercise):
                    ExerciseOptionsView(exercise: exercise, router: router)
                case .languageSelection:
                    LanguageSelectionView(router: router)
                case .notificationSettings:
                    NotificationSettingsView(router: router)
                case .soundSettings:
                    SoundSettingsView(router: router)
                case .subscriptionOffer:
                    SubscriptionOfferView(router: router)
                case .userProfile:
                    UserProfileView(router: router)
                case .createPlan:
                    CreatePlanView(router: router)
                case .planOptions(let plan):
                    PlanOptionsView(plan: plan, router: router)
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        router.dismissSheet()
                    }
                }
            }
        }
    }
}

// MARK: - Full Screen View

struct FullScreenView: View {
    let destination: FullScreenDestination
    let router: NavigationRouter
    
    var body: some View {
        Group {
            switch destination {
            case .breathingSession(let exercise, let duration):
                BreathingSessionView(
                    exercise: exercise,
                    duration: TimeInterval(duration),
                    onComplete: {
                        router.dismissFullScreen()
                    },
                    onCancel: {
                        router.dismissFullScreen()
                    }
                )
            case .onboarding:
                OnboardingView {
                    router.dismissFullScreen()
                }
            case .login:
                LoginView(router: router)
            case .subscription:
                SubscriptionView(router: router)
            }
        }
    }
}

// MARK: - Placeholder Views for Sheet Destinations

struct SessionCompleteView: View {
    let session: PracticeSession
    let router: NavigationRouter
    
    var body: some View {
        VStack {
            Text("Session Complete!")
                .textStyle(.primaryHeading)
            Text("Great job completing your breathing session.")
                .textStyle(.secondaryBody)
        }
        .padding()
    }
}

struct SessionFeedbackView: View {
    let session: PracticeSession
    let router: NavigationRouter
    
    var body: some View {
        VStack {
            Text("How was your session?")
                .textStyle(.primaryHeading)
            Text("Your feedback helps us improve.")
                .textStyle(.secondaryBody)
        }
        .padding()
    }
}

struct ExerciseOptionsView: View {
    let exercise: BreathingExercise
    let router: NavigationRouter
    
    var body: some View {
        VStack {
            Text("Exercise Options")
                .textStyle(.primaryHeading)
            Text("Customize your \(exercise.localizedName) session.")
                .textStyle(.secondaryBody)
        }
        .padding()
    }
}

struct LanguageSelectionView: View {
    let router: NavigationRouter
    
    var body: some View {
        LanguagePickerView()
            .padding()
    }
}

struct NotificationSettingsView: View {
    let router: NavigationRouter
    
    var body: some View {
        VStack {
            Text("Notification Settings")
                .textStyle(.primaryHeading)
            Text("Manage your notification preferences.")
                .textStyle(.secondaryBody)
        }
        .padding()
    }
}

struct SoundSettingsView: View {
    let router: NavigationRouter
    
    var body: some View {
        VStack {
            Text("Sound Settings")
                .textStyle(.primaryHeading)
            Text("Adjust audio and sound preferences.")
                .textStyle(.secondaryBody)
        }
        .padding()
    }
}

struct SubscriptionOfferView: View {
    let router: NavigationRouter
    
    var body: some View {
        VStack {
            Text("Upgrade to Premium")
                .textStyle(.primaryHeading)
            Text("Unlock all features and content.")
                .textStyle(.secondaryBody)
        }
        .padding()
    }
}

struct UserProfileView: View {
    let router: NavigationRouter
    
    var body: some View {
        VStack {
            Text("User Profile")
                .textStyle(.primaryHeading)
            Text("Manage your account information.")
                .textStyle(.secondaryBody)
        }
        .padding()
    }
}

struct CreatePlanView: View {
    let router: NavigationRouter
    
    var body: some View {
        VStack {
            Text("Create Personal Plan")
                .textStyle(.primaryHeading)
            Text("Design a custom practice plan.")
                .textStyle(.secondaryBody)
        }
        .padding()
    }
}

struct PlanOptionsView: View {
    let plan: PersonalPlan
    let router: NavigationRouter
    
    var body: some View {
        VStack {
            Text("Plan Options")
                .textStyle(.primaryHeading)
            Text("Manage your \(plan.planGoal.displayName) plan.")
                .textStyle(.secondaryBody)
        }
        .padding()
    }
}

struct LoginView: View {
    let router: NavigationRouter
    
    var body: some View {
        VStack {
            Text("Login")
                .textStyle(.primaryHeading)
            Text("Sign in to your account.")
                .textStyle(.secondaryBody)
        }
        .padding()
    }
}

struct SubscriptionView: View {
    let router: NavigationRouter
    
    var body: some View {
        VStack {
            Text("Subscription")
                .textStyle(.primaryHeading)
            Text("Choose your subscription plan.")
                .textStyle(.secondaryBody)
        }
        .padding()
    }
}
