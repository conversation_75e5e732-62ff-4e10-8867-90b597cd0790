//
//  TabBarView.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

/// Main tab bar navigation for the app
struct TabBarView: View {

    @StateObject private var router = NavigationRouter()
    @StateObject private var coreDataManager = CoreDataManager.shared

    var body: some View {
        TabView(selection: $router.currentTab) {
            // Home Tab
            HomeView(router: router)
                .tabItem {
                    Image(systemName: router.currentTab == .home ? "house.fill" : "house")
                    Text("Home")
                }
                .tag(Tab.home)

            // Breathing Tab
            BreathingView(router: router)
                .tabItem {
                    Image(systemName: router.currentTab == .breathing ? "wind" : "wind")
                    Text("Breathing")
                }
                .tag(Tab.breathing)

            // Meditation Tab
            MeditationView(router: router)
                .tabItem {
                    Image(systemName: router.currentTab == .meditation ? "leaf.fill" : "leaf")
                    Text("Meditation")
                }
                .tag(Tab.meditation)

            // Sleep Tab
            SleepView(router: router)
                .tabItem {
                    Image(systemName: router.currentTab == .sleep ? "moon.fill" : "moon")
                    Text("Sleep")
                }
                .tag(Tab.sleep)

            // Profile Tab
            ProfileView(router: router)
                .tabItem {
                    Image(systemName: router.currentTab == .profile ? "person.fill" : "person")
                    Text("Profile")
                }
                .tag(Tab.profile)
        }
        .accentColor(.primaryTeal)
        .environmentObject(coreDataManager)
        .environmentObject(router)
        .navigationRouter(router)
        .onAppear {
            setupTabBarAppearance()
        }
    }
    
    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithTransparentBackground()
        appearance.backgroundColor = UIColor.primaryBlue.withAlphaComponent(0.95)
        
        // Normal state
        appearance.stackedLayoutAppearance.normal.iconColor = UIColor.mediumGray
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor.mediumGray,
            .font: UIFont.systemFont(ofSize: 10, weight: .medium)
        ]
        
        // Selected state
        appearance.stackedLayoutAppearance.selected.iconColor = UIColor.primaryTeal
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor.primaryTeal,
            .font: UIFont.systemFont(ofSize: 10, weight: .medium)
        ]
        
        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }
}

// MARK: - Tab Enum

enum Tab: String, CaseIterable {
    case home = "home"
    case breathing = "breathing"
    case meditation = "meditation"
    case sleep = "sleep"
    case profile = "profile"
    
    var title: String {
        switch self {
        case .home:
            return NSLocalizedString("Home", comment: "Home tab title")
        case .breathing:
            return NSLocalizedString("Breathing", comment: "Breathing tab title")
        case .meditation:
            return NSLocalizedString("Meditation", comment: "Meditation tab title")
        case .sleep:
            return NSLocalizedString("Sleep", comment: "Sleep tab title")
        case .profile:
            return NSLocalizedString("Profile", comment: "Profile tab title")
        }
    }
    
    var iconName: String {
        switch self {
        case .home:
            return "house"
        case .breathing:
            return "wind"
        case .meditation:
            return "leaf"
        case .sleep:
            return "moon"
        case .profile:
            return "person"
        }
    }
    
    var selectedIconName: String {
        switch self {
        case .home:
            return "house.fill"
        case .breathing:
            return "wind"
        case .meditation:
            return "leaf.fill"
        case .sleep:
            return "moon.fill"
        case .profile:
            return "person.fill"
        }
    }
}

// MARK: - Placeholder Views

/// Home view placeholder
struct HomeView: View {
    let router: NavigationRouter
    @EnvironmentObject var coreDataManager: CoreDataManager
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.appBackground.ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: Spacing.lg) {
                        // Welcome section
                        VStack(alignment: .leading, spacing: Spacing.md) {
                            Text("Welcome back!")
                                .textStyle(.primaryHeading)
                            
                            Text("Ready for your daily practice?")
                                .textStyle(.secondaryBody)
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .containerPadding()
                        
                        // Quick stats
                        HStack(spacing: Spacing.md) {
                            StatisticsCard(
                                title: "Streak",
                                value: "\(coreDataManager.currentStreak)",
                                subtitle: "days",
                                icon: "flame.fill",
                                color: .warning
                            )
                            
                            StatisticsCard(
                                title: "Today",
                                value: "\(Int(coreDataManager.todaysPracticeTime))",
                                subtitle: "minutes",
                                icon: "clock.fill"
                            )
                        }
                        .containerPadding()
                        
                        // Quick actions
                        VStack(spacing: Spacing.md) {
                            QuickActionCard(
                                title: "Quick Breathing",
                                subtitle: "5-minute stress relief session",
                                icon: "wind",
                                color: .primaryTeal
                            ) {
                                // Navigate to breathing session
                            }
                            
                            QuickActionCard(
                                title: "Sleep Preparation",
                                subtitle: "Relax and prepare for sleep",
                                icon: "moon",
                                color: .primaryPurple
                            ) {
                                // Navigate to sleep session
                            }
                        }
                        .containerPadding()
                        
                        Spacer(minLength: Spacing.xl)
                    }
                }
            }
            .navigationBarHidden(true)
        }
    }
}

/// Breathing view placeholder
struct BreathingView: View {
    let router: NavigationRouter

    var body: some View {
        BreathingPracticeScreen(router: router)
    }
}

/// Meditation view placeholder
struct MeditationView: View {
    let router: NavigationRouter

    var body: some View {
        NavigationView {
            ZStack {
                Color.appBackground.ignoresSafeArea()
                
                VStack {
                    Text("Meditation")
                        .textStyle(.primaryHeading)
                    
                    Text("Guided meditation sessions")
                        .textStyle(.secondaryBody)
                    
                    Spacer()
                }
                .containerPadding()
            }
            .navigationBarHidden(true)
        }
    }
}

/// Sleep view placeholder
struct SleepView: View {
    let router: NavigationRouter

    var body: some View {
        NavigationView {
            ZStack {
                Color.appBackground.ignoresSafeArea()
                
                VStack {
                    Text("Sleep")
                        .textStyle(.primaryHeading)
                    
                    Text("Sleep assistance and relaxation")
                        .textStyle(.secondaryBody)
                    
                    Spacer()
                }
                .containerPadding()
            }
            .navigationBarHidden(true)
        }
    }
}

/// Profile view placeholder
struct ProfileView: View {
    let router: NavigationRouter

    var body: some View {
        NavigationView {
            ZStack {
                Color.appBackground.ignoresSafeArea()
                
                VStack {
                    Text("Profile")
                        .textStyle(.primaryHeading)
                    
                    Text("Your account and settings")
                        .textStyle(.secondaryBody)
                    
                    Spacer()
                }
                .containerPadding()
            }
            .navigationBarHidden(true)
        }
    }
}

// MARK: - Preview

#Preview {
    TabBarView()
        .environmentObject(CoreDataManager.shared)
}
