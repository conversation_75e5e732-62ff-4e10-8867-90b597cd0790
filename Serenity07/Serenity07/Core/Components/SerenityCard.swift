//
//  SerenityCard.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

/// Custom card component following Serenity design system
struct SerenityCard<Content: View>: View {
    
    // MARK: - Properties
    
    let content: Content
    let style: CardStyle
    let padding: EdgeInsets
    let cornerRadius: CGFloat
    let shadow: ShadowStyle
    let onTap: (() -> Void)?
    
    // MARK: - Initialization
    
    init(
        style: CardStyle = .standard,
        padding: EdgeInsets? = nil,
        cornerRadius: CGFloat? = nil,
        shadow: ShadowStyle = .subtle,
        onTap: (() -> Void)? = nil,
        @ViewBuilder content: () -> Content
    ) {
        self.content = content()
        self.style = style
        self.padding = padding ?? style.defaultPadding
        self.cornerRadius = cornerRadius ?? style.defaultCornerRadius
        self.shadow = shadow
        self.onTap = onTap
    }
    
    // MARK: - Body
    
    var body: some View {
        Group {
            if let onTap = onTap {
                Button(action: onTap) {
                    cardContent
                }
                .buttonStyle(CardButtonStyle())
            } else {
                cardContent
            }
        }
    }
    
    private var cardContent: some View {
        content
            .padding(padding)
            .background(style.backgroundColor)
            .cornerRadius(cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(style.borderColor, lineWidth: style.borderWidth)
            )
            .shadow(
                color: shadow.color,
                radius: shadow.radius,
                x: shadow.offset.width,
                y: shadow.offset.height
            )
    }
}

// MARK: - Card Styles

extension SerenityCard {
    
    enum CardStyle {
        case standard
        case elevated
        case outlined
        case glass
        case gradient
        
        var backgroundColor: Color {
            switch self {
            case .standard, .elevated:
                return .cardBackground
            case .outlined:
                return .clear
            case .glass:
                return .white.opacity(0.05)
            case .gradient:
                return .clear
            }
        }
        
        var borderColor: Color {
            switch self {
            case .outlined:
                return .mediumGray.opacity(0.3)
            case .glass:
                return .white.opacity(0.1)
            default:
                return .clear
            }
        }
        
        var borderWidth: CGFloat {
            switch self {
            case .outlined, .glass:
                return 1
            default:
                return 0
            }
        }
        
        var defaultPadding: EdgeInsets {
            return EdgeInsets(
                top: Spacing.md,
                leading: Spacing.md,
                bottom: Spacing.md,
                trailing: Spacing.md
            )
        }
        
        var defaultCornerRadius: CGFloat {
            return CornerRadius.card
        }
    }
    
    enum ShadowStyle {
        case none
        case subtle
        case medium
        case strong
        
        var color: Color {
            switch self {
            case .none:
                return .clear
            default:
                return .black.opacity(0.1)
            }
        }
        
        var radius: CGFloat {
            switch self {
            case .none:
                return 0
            case .subtle:
                return 2
            case .medium:
                return 4
            case .strong:
                return 8
            }
        }
        
        var offset: CGSize {
            switch self {
            case .none:
                return .zero
            case .subtle:
                return CGSize(width: 0, height: 1)
            case .medium:
                return CGSize(width: 0, height: 2)
            case .strong:
                return CGSize(width: 0, height: 4)
            }
        }
    }
}

// MARK: - Card Button Style

struct CardButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .opacity(configuration.isPressed ? 0.9 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Specialized Card Components

/// Exercise card for displaying breathing exercises
struct ExerciseCard: View {
    let exercise: BreathingExercise
    let onTap: () -> Void
    
    var body: some View {
        SerenityCard(style: .standard, shadow: .medium, onTap: onTap) {
            VStack(alignment: .leading, spacing: Spacing.sm) {
                HStack {
                    VStack(alignment: .leading, spacing: Spacing.xs) {
                        Text(exercise.localizedName)
                            .textStyle(.cardTitle)
                        
                        Text(exercise.exerciseCategory.displayName)
                            .textStyle(.caption)
                            .foregroundColor(.primaryTeal)
                    }
                    
                    Spacer()
                    
                    DifficultyBadge(difficulty: exercise.difficultyLevel)
                }
                
                Text(exercise.exerciseDescription)
                    .textStyle(.secondaryBody)
                    .lineLimit(2)
                
                HStack {
                    Label("\(exercise.availableDurations.first ?? 300 / 60) min", 
                          systemImage: "clock")
                        .textStyle(.caption)
                    
                    Spacer()
                    
                    if exercise.isPremium {
                        Label("Premium", systemImage: "crown.fill")
                            .textStyle(.caption)
                            .foregroundColor(.warning)
                    }
                }
            }
        }
    }
}

/// Statistics card for displaying user stats
struct StatisticsCard: View {
    let title: String
    let value: String
    let subtitle: String?
    let icon: String
    let color: Color
    
    init(title: String, value: String, subtitle: String? = nil, icon: String, color: Color = .primaryTeal) {
        self.title = title
        self.value = value
        self.subtitle = subtitle
        self.icon = icon
        self.color = color
    }
    
    var body: some View {
        SerenityCard(style: .glass, shadow: .subtle) {
            VStack(spacing: Spacing.xs) {
                HStack {
                    Image(systemName: icon)
                        .foregroundColor(color)
                        .font(.system(size: ComponentSize.buttonIconSize))
                    
                    Spacer()
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(value)
                        .textStyle(.statistics)
                        .foregroundColor(color)
                    
                    Text(title)
                        .textStyle(.caption)
                    
                    if let subtitle = subtitle {
                        Text(subtitle)
                            .textStyle(.caption)
                            .foregroundColor(.mediumGray)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
        }
        .frame(height: ComponentSize.statisticsCardHeight)
    }
}

/// Quick action card
struct QuickActionCard: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let onTap: () -> Void
    
    var body: some View {
        SerenityCard(style: .gradient, shadow: .medium, onTap: onTap) {
            HStack(spacing: Spacing.md) {
                Image(systemName: icon)
                    .font(.system(size: 32, weight: .medium))
                    .foregroundColor(color)
                    .frame(width: 48, height: 48)
                    .background(color.opacity(0.1))
                    .cornerRadius(CornerRadius.medium)
                
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text(title)
                        .textStyle(.cardTitle)
                    
                    Text(subtitle)
                        .textStyle(.secondaryBody)
                        .lineLimit(2)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.mediumGray)
                    .font(.system(size: ComponentSize.smallIconSize, weight: .medium))
            }
        }
        .background(
            LinearGradient(
                colors: [color.opacity(0.1), color.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .cornerRadius(CornerRadius.card)
        )
    }
}

/// Difficulty badge component
struct DifficultyBadge: View {
    let difficulty: ExerciseDifficulty
    
    var body: some View {
        Text(difficulty.displayName)
            .font(.caption)
            .fontWeight(.medium)
            .padding(.horizontal, Spacing.xs)
            .padding(.vertical, 4)
            .background(badgeColor.opacity(0.2))
            .foregroundColor(badgeColor)
            .cornerRadius(CornerRadius.small)
    }
    
    private var badgeColor: Color {
        switch difficulty {
        case .beginner:
            return .success
        case .intermediate:
            return .warning
        case .advanced:
            return .error
        }
    }
}

// MARK: - Preview

#Preview {
    ScrollView {
        VStack(spacing: Spacing.md) {
            // Standard card
            SerenityCard {
                VStack(alignment: .leading, spacing: Spacing.sm) {
                    Text("Standard Card")
                        .textStyle(.cardTitle)
                    Text("This is a standard card with default styling.")
                        .textStyle(.secondaryBody)
                }
            }
            
            // Statistics cards
            HStack(spacing: Spacing.md) {
                StatisticsCard(
                    title: "Current Streak",
                    value: "7",
                    subtitle: "days",
                    icon: "flame.fill",
                    color: .warning
                )
                
                StatisticsCard(
                    title: "Total Time",
                    value: "45",
                    subtitle: "minutes",
                    icon: "clock.fill"
                )
            }
            
            // Quick action card
            QuickActionCard(
                title: "Quick Breathing",
                subtitle: "Start a 5-minute breathing session",
                icon: "wind",
                color: .primaryTeal
            ) {
                print("Quick action tapped")
            }
        }
        .padding()
    }
    .background(Color.appBackground)
}
