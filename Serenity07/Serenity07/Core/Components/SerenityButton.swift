//
//  SerenityButton.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

/// Custom button component following Serenity design system
struct SerenityButton: View {
    
    // MARK: - Properties
    
    let title: String
    let action: () -> Void
    let style: ButtonStyle
    let size: ButtonSize
    let isEnabled: Bool
    let isLoading: Bool
    let icon: String?
    
    // MARK: - Initialization
    
    init(
        _ title: String,
        style: ButtonStyle = .primary,
        size: ButtonSize = .medium,
        isEnabled: Bool = true,
        isLoading: Bool = false,
        icon: String? = nil,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.style = style
        self.size = size
        self.isEnabled = isEnabled
        self.isLoading = isLoading
        self.icon = icon
        self.action = action
    }
    
    // MARK: - Body
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: Spacing.xs) {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: textColor))
                        .scaleEffect(0.8)
                } else if let icon = icon {
                    Image(systemName: icon)
                        .font(.system(size: iconSize, weight: .medium))
                }
                
                if !title.isEmpty {
                    Text(title)
                        .font(textFont)
                        .fontWeight(.medium)
                }
            }
            .foregroundColor(textColor)
            .frame(minWidth: minWidth, minHeight: height)
            .padding(.horizontal, horizontalPadding)
            .background(backgroundColor)
            .cornerRadius(cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
        }
        .disabled(!isEnabled || isLoading)
        .scaleEffect(isEnabled ? 1.0 : 0.95)
        .opacity(isEnabled ? 1.0 : 0.6)
        .animation(.easeInOut(duration: 0.15), value: isEnabled)
    }
}

// MARK: - Button Styles

extension SerenityButton {
    
    enum ButtonStyle {
        case primary
        case secondary
        case tertiary
        case destructive
        case ghost
        
        var backgroundColor: Color {
            switch self {
            case .primary:
                return .primaryTeal
            case .secondary:
                return .primaryPurple
            case .tertiary:
                return .cardBackground
            case .destructive:
                return .error
            case .ghost:
                return .clear
            }
        }
        
        var textColor: Color {
            switch self {
            case .primary, .secondary, .destructive:
                return .white
            case .tertiary, .ghost:
                return .primaryText
            }
        }
        
        var borderColor: Color {
            switch self {
            case .ghost:
                return .primaryTeal
            case .tertiary:
                return .mediumGray.opacity(0.3)
            default:
                return .clear
            }
        }
        
        var borderWidth: CGFloat {
            switch self {
            case .ghost, .tertiary:
                return 1
            default:
                return 0
            }
        }
    }
    
    enum ButtonSize {
        case small
        case medium
        case large
        
        var height: CGFloat {
            switch self {
            case .small:
                return ComponentSize.secondaryButtonHeight
            case .medium:
                return ComponentSize.primaryButtonHeight
            case .large:
                return 56
            }
        }
        
        var horizontalPadding: CGFloat {
            switch self {
            case .small:
                return Spacing.sm
            case .medium:
                return Spacing.md
            case .large:
                return Spacing.lg
            }
        }
        
        var minWidth: CGFloat {
            switch self {
            case .small:
                return 80
            case .medium:
                return ComponentSize.minimumButtonWidth
            case .large:
                return 160
            }
        }
        
        var cornerRadius: CGFloat {
            return height / 2
        }
        
        var textFont: Font {
            switch self {
            case .small:
                return .bodyMedium
            case .medium:
                return .button
            case .large:
                return .h4
            }
        }
        
        var iconSize: CGFloat {
            switch self {
            case .small:
                return ComponentSize.smallIconSize
            case .medium:
                return ComponentSize.buttonIconSize
            case .large:
                return ComponentSize.navigationIconSize
            }
        }
    }
}

// MARK: - Computed Properties

private extension SerenityButton {
    
    var backgroundColor: Color {
        return style.backgroundColor
    }
    
    var textColor: Color {
        return style.textColor
    }
    
    var borderColor: Color {
        return style.borderColor
    }
    
    var borderWidth: CGFloat {
        return style.borderWidth
    }
    
    var height: CGFloat {
        return size.height
    }
    
    var horizontalPadding: CGFloat {
        return size.horizontalPadding
    }
    
    var minWidth: CGFloat {
        return size.minWidth
    }
    
    var cornerRadius: CGFloat {
        return size.cornerRadius
    }
    
    var textFont: Font {
        return size.textFont
    }
    
    var iconSize: CGFloat {
        return size.iconSize
    }
}

// MARK: - Convenience Initializers

extension SerenityButton {
    
    /// Primary button
    static func primary(
        _ title: String,
        icon: String? = nil,
        isEnabled: Bool = true,
        isLoading: Bool = false,
        action: @escaping () -> Void
    ) -> SerenityButton {
        SerenityButton(
            title,
            style: .primary,
            size: .medium,
            isEnabled: isEnabled,
            isLoading: isLoading,
            icon: icon,
            action: action
        )
    }
    
    /// Secondary button
    static func secondary(
        _ title: String,
        icon: String? = nil,
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) -> SerenityButton {
        SerenityButton(
            title,
            style: .secondary,
            size: .medium,
            isEnabled: isEnabled,
            icon: icon,
            action: action
        )
    }
    
    /// Ghost button
    static func ghost(
        _ title: String,
        icon: String? = nil,
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) -> SerenityButton {
        SerenityButton(
            title,
            style: .ghost,
            size: .medium,
            isEnabled: isEnabled,
            icon: icon,
            action: action
        )
    }
    
    /// Icon-only button
    static func icon(
        _ iconName: String,
        style: ButtonStyle = .tertiary,
        size: ButtonSize = .medium,
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) -> SerenityButton {
        SerenityButton(
            "",
            style: style,
            size: size,
            isEnabled: isEnabled,
            icon: iconName,
            action: action
        )
    }
    
    /// Destructive button
    static func destructive(
        _ title: String,
        icon: String? = nil,
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) -> SerenityButton {
        SerenityButton(
            title,
            style: .destructive,
            size: .medium,
            isEnabled: isEnabled,
            icon: icon,
            action: action
        )
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: Spacing.md) {
        SerenityButton.primary("Start Practice", icon: "play.fill") {
            print("Primary button tapped")
        }
        
        SerenityButton.secondary("Continue", icon: "arrow.right") {
            print("Secondary button tapped")
        }
        
        SerenityButton.ghost("Skip") {
            print("Ghost button tapped")
        }
        
        SerenityButton.icon("heart.fill", style: .primary) {
            print("Icon button tapped")
        }
        
        SerenityButton.primary("Loading...", isLoading: true) {
            print("Loading button tapped")
        }
        
        SerenityButton.destructive("Delete", icon: "trash") {
            print("Destructive button tapped")
        }
    }
    .padding()
    .background(Color.appBackground)
}
