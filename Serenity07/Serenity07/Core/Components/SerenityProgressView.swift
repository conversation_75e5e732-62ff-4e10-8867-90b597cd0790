//
//  SerenityProgressView.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

/// Custom progress view components following Serenity design system
struct SerenityProgressView: View {
    
    // MARK: - Properties
    
    let value: Double
    let total: Double
    let style: ProgressStyle
    let showPercentage: Bool
    let showLabels: Bool
    let color: Color
    let backgroundColor: Color
    let height: CGFloat
    
    // MARK: - Initialization
    
    init(
        value: Double,
        total: Double = 100,
        style: ProgressStyle = .linear,
        showPercentage: Bool = false,
        showLabels: Bool = false,
        color: Color = .primaryTeal,
        backgroundColor: Color = .mediumGray.opacity(0.3),
        height: CGFloat = 8
    ) {
        self.value = value
        self.total = total
        self.style = style
        self.showPercentage = showPercentage
        self.showLabels = showLabels
        self.color = color
        self.backgroundColor = backgroundColor
        self.height = height
    }
    
    // MARK: - Body
    
    var body: some View {
        Group {
            switch style {
            case .linear:
                linearProgressView
            case .circular:
                circularProgressView
            case .ring:
                ringProgressView
            case .segmented(let segments):
                segmentedProgressView(segments: segments)
            }
        }
    }
}

// MARK: - Progress Styles

extension SerenityProgressView {
    
    enum ProgressStyle {
        case linear
        case circular
        case ring
        case segmented(Int)
    }
}

// MARK: - Progress View Implementations

private extension SerenityProgressView {
    
    var progressPercentage: Double {
        guard total > 0 else { return 0 }
        return min(max(value / total, 0), 1)
    }
    
    var percentageText: String {
        return "\(Int(progressPercentage * 100))%"
    }
    
    // MARK: - Linear Progress View
    
    var linearProgressView: some View {
        VStack(alignment: .leading, spacing: Spacing.xs) {
            if showLabels {
                HStack {
                    Text("\(Int(value))")
                        .textStyle(.caption)
                    
                    Spacer()
                    
                    Text("\(Int(total))")
                        .textStyle(.caption)
                }
            }
            
            ZStack(alignment: .leading) {
                // Background
                RoundedRectangle(cornerRadius: height / 2)
                    .fill(backgroundColor)
                    .frame(height: height)
                
                // Progress
                RoundedRectangle(cornerRadius: height / 2)
                    .fill(color)
                    .frame(width: progressWidth, height: height)
                    .animation(.easeInOut(duration: 0.3), value: progressPercentage)
            }
            
            if showPercentage {
                HStack {
                    Spacer()
                    Text(percentageText)
                        .textStyle(.caption)
                        .foregroundColor(color)
                }
            }
        }
    }
    
    private var progressWidth: CGFloat {
        // This will be calculated based on the available width
        // In a real implementation, you'd use GeometryReader
        return 200 * progressPercentage
    }
    
    // MARK: - Circular Progress View
    
    var circularProgressView: some View {
        ZStack {
            // Background circle
            Circle()
                .stroke(backgroundColor, lineWidth: height)
            
            // Progress circle
            Circle()
                .trim(from: 0, to: progressPercentage)
                .stroke(
                    color,
                    style: StrokeStyle(lineWidth: height, lineCap: .round)
                )
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 0.5), value: progressPercentage)
            
            // Center content
            if showPercentage {
                Text(percentageText)
                    .textStyle(.statistics)
                    .foregroundColor(color)
            }
        }
        .frame(width: 120, height: 120)
    }
    
    // MARK: - Ring Progress View
    
    var ringProgressView: some View {
        ZStack {
            // Background ring
            Circle()
                .stroke(backgroundColor, lineWidth: height)
            
            // Progress ring with gradient
            Circle()
                .trim(from: 0, to: progressPercentage)
                .stroke(
                    AngularGradient(
                        colors: [color.opacity(0.3), color],
                        center: .center,
                        startAngle: .degrees(0),
                        endAngle: .degrees(360 * progressPercentage)
                    ),
                    style: StrokeStyle(lineWidth: height, lineCap: .round)
                )
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 0.5), value: progressPercentage)
            
            // Center content
            VStack(spacing: 2) {
                if showPercentage {
                    Text(percentageText)
                        .textStyle(.cardTitle)
                        .foregroundColor(color)
                }
                
                if showLabels {
                    Text("\(Int(value))/\(Int(total))")
                        .textStyle(.caption)
                        .foregroundColor(.secondaryText)
                }
            }
        }
        .frame(width: 100, height: 100)
    }
    
    // MARK: - Segmented Progress View
    
    func segmentedProgressView(segments: Int) -> some View {
        HStack(spacing: 4) {
            ForEach(0..<segments, id: \.self) { index in
                let segmentValue = Double(index + 1) / Double(segments)
                let isCompleted = progressPercentage >= segmentValue
                let isPartial = progressPercentage > Double(index) / Double(segments) && 
                               progressPercentage < segmentValue
                
                RoundedRectangle(cornerRadius: height / 2)
                    .fill(segmentColor(isCompleted: isCompleted, isPartial: isPartial))
                    .frame(height: height)
                    .animation(.easeInOut(duration: 0.2), value: progressPercentage)
            }
        }
    }
    
    private func segmentColor(isCompleted: Bool, isPartial: Bool) -> Color {
        if isCompleted {
            return color
        } else if isPartial {
            return color.opacity(0.5)
        } else {
            return backgroundColor
        }
    }
}

// MARK: - Specialized Progress Components

/// Streak progress view for daily goals
struct StreakProgressView: View {
    let currentStreak: Int
    let targetStreak: Int
    
    var body: some View {
        VStack(spacing: Spacing.sm) {
            HStack {
                Image(systemName: "flame.fill")
                    .foregroundColor(.warning)
                
                Text("Current Streak")
                    .textStyle(.secondaryBody)
                
                Spacer()
                
                Text("\(currentStreak) days")
                    .textStyle(.cardTitle)
                    .foregroundColor(.warning)
            }
            
            SerenityProgressView(
                value: Double(currentStreak),
                total: Double(targetStreak),
                style: .segmented(7),
                color: .warning,
                height: 6
            )
        }
        .cardPadding()
        .background(Color.cardBackground)
        .cornerRadius(CornerRadius.card)
    }
}

/// Session progress view for breathing sessions
struct SessionProgressView: View {
    let currentTime: TimeInterval
    let totalTime: TimeInterval
    let isActive: Bool
    
    var body: some View {
        VStack(spacing: Spacing.sm) {
            HStack {
                Text(formatTime(currentTime))
                    .textStyle(.timer)
                    .foregroundColor(.primaryTeal)
                
                Spacer()
                
                Text(formatTime(totalTime))
                    .textStyle(.caption)
                    .foregroundColor(.secondaryText)
            }
            
            SerenityProgressView(
                value: currentTime,
                total: totalTime,
                style: .linear,
                color: .primaryTeal,
                height: 4
            )
        }
    }
    
    private func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

/// Goal progress view for personal plans
struct GoalProgressView: View {
    let title: String
    let current: Int
    let target: Int
    let unit: String
    let color: Color
    
    var body: some View {
        VStack(spacing: Spacing.sm) {
            HStack {
                Text(title)
                    .textStyle(.secondaryBody)
                
                Spacer()
                
                Text("\(current)/\(target) \(unit)")
                    .textStyle(.caption)
                    .foregroundColor(color)
            }
            
            SerenityProgressView(
                value: Double(current),
                total: Double(target),
                style: .ring,
                showPercentage: true,
                color: color,
                height: 8
            )
        }
    }
}

/// Loading progress view
struct LoadingProgressView: View {
    @State private var isAnimating = false
    
    var body: some View {
        VStack(spacing: Spacing.md) {
            ZStack {
                Circle()
                    .stroke(Color.mediumGray.opacity(0.3), lineWidth: 4)
                    .frame(width: 40, height: 40)
                
                Circle()
                    .trim(from: 0, to: 0.3)
                    .stroke(
                        Color.primaryTeal,
                        style: StrokeStyle(lineWidth: 4, lineCap: .round)
                    )
                    .frame(width: 40, height: 40)
                    .rotationEffect(.degrees(isAnimating ? 360 : 0))
                    .animation(
                        .linear(duration: 1.0).repeatForever(autoreverses: false),
                        value: isAnimating
                    )
            }
            
            Text("Loading...")
                .textStyle(.caption)
                .foregroundColor(.secondaryText)
        }
        .onAppear {
            isAnimating = true
        }
    }
}

// MARK: - Preview

#Preview {
    ScrollView {
        VStack(spacing: Spacing.lg) {
            // Linear progress
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text("Linear Progress")
                    .textStyle(.cardTitle)
                
                SerenityProgressView(
                    value: 65,
                    total: 100,
                    style: .linear,
                    showPercentage: true,
                    showLabels: true
                )
            }
            
            // Circular progress
            VStack(spacing: Spacing.sm) {
                Text("Circular Progress")
                    .textStyle(.cardTitle)
                
                SerenityProgressView(
                    value: 75,
                    total: 100,
                    style: .circular,
                    showPercentage: true
                )
            }
            
            // Ring progress
            VStack(spacing: Spacing.sm) {
                Text("Ring Progress")
                    .textStyle(.cardTitle)
                
                SerenityProgressView(
                    value: 8,
                    total: 10,
                    style: .ring,
                    showPercentage: true,
                    showLabels: true
                )
            }
            
            // Segmented progress
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text("Segmented Progress")
                    .textStyle(.cardTitle)
                
                SerenityProgressView(
                    value: 5,
                    total: 7,
                    style: .segmented(7),
                    color: .warning
                )
            }
            
            // Specialized components
            StreakProgressView(currentStreak: 5, targetStreak: 7)
            
            SessionProgressView(
                currentTime: 180,
                totalTime: 300,
                isActive: true
            )
            
            GoalProgressView(
                title: "Daily Goal",
                current: 15,
                target: 20,
                unit: "minutes",
                color: .success
            )
            
            LoadingProgressView()
        }
        .padding()
    }
    .background(Color.appBackground)
}
