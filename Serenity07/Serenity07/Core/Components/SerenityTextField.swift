//
//  SerenityTextField.swift
//  Serenity07
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

/// Custom text field component following Serenity design system
struct SerenityTextField: View {
    
    // MARK: - Properties
    
    let title: String
    let placeholder: String
    @Binding var text: String
    let style: TextFieldStyle
    let isSecure: Bool
    let keyboardType: UIKeyboardType
    let isEnabled: Bool
    let errorMessage: String?
    let maxLength: Int?
    let onEditingChanged: ((Bool) -> Void)?
    let onCommit: (() -> Void)?
    
    @State private var isEditing = false
    @State private var showPassword = false
    
    // MARK: - Initialization
    
    init(
        title: String = "",
        placeholder: String,
        text: Binding<String>,
        style: TextFieldStyle = .standard,
        isSecure: Bool = false,
        keyboardType: UIKeyboardType = .default,
        isEnabled: Bool = true,
        errorMessage: String? = nil,
        maxLength: Int? = nil,
        onEditingChanged: ((Bool) -> Void)? = nil,
        onCommit: (() -> Void)? = nil
    ) {
        self.title = title
        self.placeholder = placeholder
        self._text = text
        self.style = style
        self.isSecure = isSecure
        self.keyboardType = keyboardType
        self.isEnabled = isEnabled
        self.errorMessage = errorMessage
        self.maxLength = maxLength
        self.onEditingChanged = onEditingChanged
        self.onCommit = onCommit
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.xs) {
            // Title
            if !title.isEmpty {
                Text(title)
                    .textStyle(.secondaryBody)
                    .foregroundColor(titleColor)
            }
            
            // Text field container
            HStack(spacing: Spacing.sm) {
                // Text field
                Group {
                    if isSecure && !showPassword {
                        SecureField(placeholder, text: $text, onCommit: {
                            onCommit?()
                        })
                    } else {
                        TextField(placeholder, text: $text, onEditingChanged: { editing in
                            isEditing = editing
                            onEditingChanged?(editing)
                        }, onCommit: {
                            onCommit?()
                        })
                    }
                }
                .textStyle(.primaryBody)
                .keyboardType(keyboardType)
                .disabled(!isEnabled)
                .onChange(of: text) { newValue in
                    if let maxLength = maxLength, newValue.count > maxLength {
                        text = String(newValue.prefix(maxLength))
                    }
                }
                
                // Secure field toggle
                if isSecure {
                    Button(action: {
                        showPassword.toggle()
                    }) {
                        Image(systemName: showPassword ? "eye.slash" : "eye")
                            .foregroundColor(.mediumGray)
                            .font(.system(size: ComponentSize.buttonIconSize))
                    }
                }
                
                // Clear button
                if !text.isEmpty && isEditing && style.showsClearButton {
                    Button(action: {
                        text = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.mediumGray)
                            .font(.system(size: ComponentSize.buttonIconSize))
                    }
                }
            }
            .padding(.horizontal, style.horizontalPadding)
            .padding(.vertical, style.verticalPadding)
            .background(backgroundColor)
            .cornerRadius(style.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: style.cornerRadius)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
            
            // Error message
            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .textStyle(.caption)
                    .foregroundColor(.error)
            }
            
            // Character count
            if let maxLength = maxLength, style.showsCharacterCount {
                HStack {
                    Spacer()
                    Text("\(text.count)/\(maxLength)")
                        .textStyle(.caption)
                        .foregroundColor(text.count > maxLength ? .error : .mediumGray)
                }
            }
        }
    }
}

// MARK: - Text Field Styles

extension SerenityTextField {
    
    enum TextFieldStyle {
        case standard
        case outlined
        case minimal
        case search
        
        var horizontalPadding: CGFloat {
            switch self {
            case .standard, .outlined:
                return Spacing.md
            case .minimal:
                return Spacing.sm
            case .search:
                return Spacing.md
            }
        }
        
        var verticalPadding: CGFloat {
            switch self {
            case .standard, .outlined:
                return Spacing.sm
            case .minimal:
                return Spacing.xs
            case .search:
                return Spacing.sm
            }
        }
        
        var cornerRadius: CGFloat {
            switch self {
            case .standard:
                return CornerRadius.inputField
            case .outlined:
                return CornerRadius.inputField
            case .minimal:
                return CornerRadius.small
            case .search:
                return CornerRadius.searchField
            }
        }
        
        var backgroundColor: Color {
            switch self {
            case .standard:
                return .cardBackground
            case .outlined:
                return .clear
            case .minimal:
                return .clear
            case .search:
                return .darkGray.opacity(0.3)
            }
        }
        
        var borderColor: Color {
            switch self {
            case .outlined:
                return .mediumGray.opacity(0.5)
            case .minimal:
                return .clear
            default:
                return .clear
            }
        }
        
        var borderWidth: CGFloat {
            switch self {
            case .outlined:
                return 1
            default:
                return 0
            }
        }
        
        var showsClearButton: Bool {
            switch self {
            case .search:
                return true
            default:
                return false
            }
        }
        
        var showsCharacterCount: Bool {
            return true
        }
    }
}

// MARK: - Computed Properties

private extension SerenityTextField {
    
    var titleColor: Color {
        if let _ = errorMessage {
            return .error
        }
        return isEditing ? .primaryTeal : .secondaryText
    }
    
    var backgroundColor: Color {
        if !isEnabled {
            return .mediumGray.opacity(0.1)
        }
        return style.backgroundColor
    }
    
    var borderColor: Color {
        if let _ = errorMessage {
            return .error
        }
        if isEditing {
            return .primaryTeal
        }
        return style.borderColor
    }
    
    var borderWidth: CGFloat {
        if errorMessage != nil || isEditing {
            return 1
        }
        return style.borderWidth
    }
}

// MARK: - Convenience Initializers

extension SerenityTextField {
    
    /// Standard text field
    static func standard(
        title: String = "",
        placeholder: String,
        text: Binding<String>,
        errorMessage: String? = nil
    ) -> SerenityTextField {
        SerenityTextField(
            title: title,
            placeholder: placeholder,
            text: text,
            style: .standard,
            errorMessage: errorMessage
        )
    }
    
    /// Email text field
    static func email(
        title: String = "Email",
        placeholder: String = "Enter your email",
        text: Binding<String>,
        errorMessage: String? = nil
    ) -> SerenityTextField {
        SerenityTextField(
            title: title,
            placeholder: placeholder,
            text: text,
            style: .outlined,
            keyboardType: .emailAddress,
            errorMessage: errorMessage
        )
    }
    
    /// Password text field
    static func password(
        title: String = "Password",
        placeholder: String = "Enter your password",
        text: Binding<String>,
        errorMessage: String? = nil
    ) -> SerenityTextField {
        SerenityTextField(
            title: title,
            placeholder: placeholder,
            text: text,
            style: .outlined,
            isSecure: true,
            errorMessage: errorMessage
        )
    }
    
    /// Search text field
    static func search(
        placeholder: String = "Search...",
        text: Binding<String>
    ) -> SerenityTextField {
        SerenityTextField(
            placeholder: placeholder,
            text: text,
            style: .search,
            keyboardType: .default
        )
    }
    
    /// Number text field
    static func number(
        title: String = "",
        placeholder: String,
        text: Binding<String>,
        maxLength: Int? = nil,
        errorMessage: String? = nil
    ) -> SerenityTextField {
        SerenityTextField(
            title: title,
            placeholder: placeholder,
            text: text,
            style: .standard,
            keyboardType: .numberPad,
            maxLength: maxLength,
            errorMessage: errorMessage
        )
    }
}

// MARK: - Form Field Component

struct FormField<Content: View>: View {
    let title: String
    let isRequired: Bool
    let errorMessage: String?
    let content: Content
    
    init(
        title: String,
        isRequired: Bool = false,
        errorMessage: String? = nil,
        @ViewBuilder content: () -> Content
    ) {
        self.title = title
        self.isRequired = isRequired
        self.errorMessage = errorMessage
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.xs) {
            HStack {
                Text(title)
                    .textStyle(.secondaryBody)
                    .foregroundColor(errorMessage != nil ? .error : .secondaryText)
                
                if isRequired {
                    Text("*")
                        .textStyle(.secondaryBody)
                        .foregroundColor(.error)
                }
                
                Spacer()
            }
            
            content
            
            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .textStyle(.caption)
                    .foregroundColor(.error)
            }
        }
    }
}

// MARK: - Preview

#Preview {
    ScrollView {
        VStack(spacing: Spacing.lg) {
            SerenityTextField.standard(
                title: "Name",
                placeholder: "Enter your name",
                text: .constant("")
            )
            
            SerenityTextField.email(
                text: .constant(""),
                errorMessage: "Please enter a valid email"
            )
            
            SerenityTextField.password(
                text: .constant("")
            )
            
            SerenityTextField.search(
                text: .constant("")
            )
            
            SerenityTextField.number(
                title: "Age",
                placeholder: "Enter your age",
                text: .constant(""),
                maxLength: 3
            )
            
            FormField(title: "Custom Field", isRequired: true) {
                SerenityTextField.standard(
                    placeholder: "Custom input",
                    text: .constant("")
                )
            }
        }
        .padding()
    }
    .background(Color.appBackground)
}
