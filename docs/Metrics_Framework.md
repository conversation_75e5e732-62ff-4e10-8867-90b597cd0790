# 产品评估指标框架 - 静息(Serenity)

## 1. 指标框架概述

静息(Serenity)应用的指标框架是一套系统化的评估体系，用于衡量产品性能、用户体验和业务目标达成情况。本框架基于行业最佳实践，结合呼吸健康应用的特殊性，建立了多维度、可量化的指标体系，确保产品决策有数据支撑，持续优化有明确方向。

**框架设计原则**：
- **以用户为中心**：优先关注反映用户体验和价值的指标
- **可操作性**：指标需可收集、可量化、可分析
- **关联性**：指标间相互关联，共同反映产品整体健康状况
- **驱动行动**：指标变化能直接指导产品迭代和优化
- **平衡全面**：兼顾短期指标与长期健康，用户指标与业务指标

**指标层级结构**：
1. **北极星指标**：单一核心指标，反映产品核心价值实现情况
2. **一级指标**：围绕北极星指标的关键维度（HEART/AARRR模型）
3. **二级指标**：各维度下的具体衡量指标
4. **功能级指标**：特定功能的使用和效果指标
5. **健康指标**：产品技术性能和稳定性指标

## 2. 北极星指标定义

**北极星指标**：**月活跃用户的周均呼吸练习完成次数**

**定义**：每月活跃用户在一周内平均完成的呼吸练习次数

**计算公式**：总月练习完成次数 ÷ 月活跃用户数 ÷ 4.3（平均每月周数）

**选择依据**：
- **反映核心价值**：直接关联产品核心功能（呼吸练习）的使用情况
- **用户价值导向**：练习完成次数与用户获得的健康益处正相关
- **可操作性**：易于理解、测量和影响
- **长期导向**：鼓励可持续的用户习惯养成，而非短期行为
- **业务驱动**：与用户留存率、付费转化率等业务指标强相关

**目标值**：
- 上线3个月：1.5次/周/用户
- 上线6个月：2.0次/周/用户
- 上线12个月：2.5次/周/用户

**监测频率**：每日计算，周度汇总，月度回顾

## 3. HEART / AARRR等指标体系详述

### 3.1 HEART模型（用户体验指标）

| 维度 | 核心指标 | 定义 | 目标值 | 监测频率 |
|------|---------|------|--------|----------|
| **Happiness（满意度）** | 应用商店评分 | 用户在App Store的评分 | ≥4.5星 | 周度 |
|  | NPS得分 | 净推荐值（愿意推荐的用户比例） | ≥40 | 季度 |
|  | 练习满意度评分 | 用户完成练习后的满意度（5分制） | ≥4.2分 | 月度 |
| **Engagement（参与度）** | 日均使用时长 | 活跃用户平均每日使用时长 | ≥8分钟 | 日度 |
|  | 周活跃天数 | 用户每周活跃的平均天数 | ≥3天 | 周度 |
|  | 练习深度 | 单次练习平均完成率 | ≥85% | 日度 |
| **Adoption（采纳率）** | 新用户次日留存 | 首次使用后次日再次使用的用户比例 | ≥40% | 日度 |
|  | 核心功能发现率 | 发现并使用过至少3个核心功能的新用户比例 | ≥70% | 周度 |
|  | 引导完成率 | 完成首次使用引导流程的用户比例 | ≥90% | 日度 |
| **Retention（留存率）** | 7日留存率 | 首次使用后7天内再次使用的用户比例 | ≥30% | 周度 |
|  | 30日留存率 | 首次使用后30天内再次使用的用户比例 | ≥20% | 月度 |
|  | 90日留存率 | 首次使用后90天内再次使用的用户比例 | ≥15% | 季度 |
| **Task Success（任务成功率）** | 练习开始成功率 | 成功开始练习的用户比例 | ≥98% | 日度 |
|  | 练习完成成功率 | 成功完成所选练习的用户比例 | ≥95% | 日度 |
|  | 目标设置完成率 | 成功设置个人健康目标的用户比例 | ≥80% | 周度 |

### 3.2 AARRR模型（增长指标）

| 阶段 | 核心指标 | 定义 | 目标值 | 监测频率 |
|------|---------|------|--------|----------|
| **Acquisition（获取）** | 总下载量 | 应用累计下载次数 | 首年10万+ | 日度 |
|  | 新增用户数 | 每日新增用户数量 | 上线6个月后500+/日 | 日度 |
|  | 获客成本(CAC) | 获取一个新用户的平均成本 | ≤50元 | 月度 |
|  | 渠道转化率 | 各渠道访问到下载的转化率 | ≥20% | 周度 |
| **Activation（激活）** | 激活用户比例 | 完成首次练习的新用户比例 | ≥60% | 日度 |
|  | 首次体验满意度 | 首次使用后给予4-5星评价的用户比例 | ≥70% | 周度 |
|  | 核心功能触达率 | 首次使用中接触核心功能的用户比例 | ≥90% | 周度 |
| **Retention（留存）** | 月活跃用户数(MAU) | 每月至少使用一次的用户总数 | 上线12个月后5万+ | 月度 |
|  | 用户留存曲线 | 不同时期注册用户的留存衰减曲线 | 30日留存≥20% | 周度 |
|  | 流失预警用户比例 | 即将流失的高风险用户占比 | ≤10% | 周度 |
| **Revenue（收入）** | 付费转化率 | 免费用户转化为付费用户的比例 | ≥5% | 月度 |
|  | 每用户平均收入(ARPU) | 总收入÷总用户数 | ≥15元 | 月度 |
|  | 付费用户生命周期价值(LTV) | 付费用户在生命周期内贡献的总收入 | ≥300元 | 季度 |
|  | 续订率 | 订阅到期后续订的用户比例 | ≥70% | 月度 |
| **Referral（推荐）** | 病毒系数(K因子) | 每个用户平均推荐的新用户数 | ≥0.3 | 月度 |
|  | 分享率 | 完成练习后分享到社交平台的用户比例 | ≥8% | 日度 |
|  | 邀请转化率 | 被邀请者中完成注册的比例 | ≥15% | 周度 |
|  | 社区活跃度 | 参与社区互动的用户比例 | ≥20% | 周度 |

### 3.3 健康与性能指标

| 类别 | 指标 | 定义 | 目标值 | 监测频率 |
|------|------|------|--------|----------|
| **技术性能** | 应用启动时间 | 从点击图标到可交互的时间 | ≤2秒 | 日度 |
|  | 页面加载时间 | 页面从请求到完全渲染的时间 | ≤1秒 | 日度 |
|  | 动画流畅度 | 呼吸引导动画的帧率 | ≥59fps | 周度 |
| **稳定性** | 崩溃率 | 应用崩溃次数÷启动次数 | ≤0.5% | 日度 |
|  | ANR率 | 应用无响应次数÷启动次数 | ≤0.3% | 日度 |
|  | 网络错误率 | 网络请求失败次数÷总请求次数 | ≤2% | 日度 |
| **安全合规** | 隐私合规率 | 符合隐私政策的用户数据处理比例 | 100% | 月度 |
|  | 安全漏洞数 | 发现的高危安全漏洞数量 | 0 | 季度 |
|  | 数据加密率 | 加密存储的敏感数据比例 | 100% | 季度 |

## 4. 功能级评估指标

### 4.1 核心功能指标

| 功能模块 | 关键指标 | 定义 | 目标值 | 监测频率 |
|---------|---------|------|--------|----------|
| **呼吸练习** | 练习完成率 | 开始练习后成功完成的比例 | ≥90% | 日度 |
|  | 平均练习时长 | 单次练习的平均持续时间 | ≥5分钟 | 日度 |
|  | 练习类型分布 | 不同类型练习的选择比例 | 均衡分布 | 周度 |
|  | 难度级别分布 | 不同难度练习的选择比例 | 初级:中级:高级=5:3:2 | 周度 |
| **个性化计划** | 计划完成率 | 开始个性化计划后完成的比例 | ≥60% | 周度 |
|  | 计划调整率 | 修改默认计划的用户比例 | ≤30% | 周度 |
|  | 计划满意度 | 对个性化计划的满意度评分 | ≥4.0分 | 月度 |
| **睡眠模块** | 睡眠练习使用率 | 使用睡眠练习的活跃用户比例 | ≥40% | 周度 |
|  | 睡眠质量自评提升 | 使用后报告睡眠质量提升的用户比例 | ≥60% | 月度 |
|  | 睡眠声音偏好 | 不同睡眠声音的使用频率 | - | 月度 |
| **冥想课程** | 课程完成率 | 开始冥想课程后完成的比例 | ≥75% | 周度 |
|  | 课程评分 | 用户对冥想课程的平均评分 | ≥4.3分 | 周度 |
|  | 课程分享率 | 完成后分享冥想课程的用户比例 | ≥5% | 日度 |

### 4.2 辅助功能指标

| 功能模块 | 关键指标 | 定义 | 目标值 | 监测频率 |
|---------|---------|------|--------|----------|
| **统计与成就** | 统计页面访问率 | 查看统计页面的活跃用户比例 | ≥30% | 周度 |
|  | 成就解锁率 | 解锁至少一个成就的用户比例 | ≥50% | 周度 |
|  | 成就分享率 | 解锁成就后分享的用户比例 | ≥10% | 周度 |
| **提醒功能** | 提醒开启率 | 开启练习提醒的用户比例 | ≥60% | 周度 |
|  | 提醒响应率 | 收到提醒后24小时内使用应用的比例 | ≥25% | 日度 |
|  | 提醒时间调整率 | 修改默认提醒时间的用户比例 | ≥40% | 月度 |
| **社交分享** | 分享功能使用率 | 使用过分享功能的用户比例 | ≥15% | 月度 |
|  | 分享转化率 | 分享后带来的新用户转化率 | ≥3% | 周度 |
|  | 分享内容偏好 | 不同类型分享内容的比例 | - | 月度 |
| **设置与个性化** | 设置页面访问率 | 访问设置页面的活跃用户比例 | ≥40% | 周度 |
|  | 主题切换率 | 更改默认主题的用户比例 | ≥20% | 月度 |
|  | 音频设置调整率 | 调整音频参数的用户比例 | ≥30% | 月度 |

## 5. 指标监测计划

### 5.1 数据收集方法

| 数据类型 | 收集工具 | 收集范围 | 隐私保护措施 |
|---------|---------|---------|------------|
| 用户行为数据 | 自定义事件跟踪 | 页面访问、按钮点击、功能使用 | 数据匿名化，用户可选择退出 |
| 应用性能数据 | Firebase Performance | 启动时间、页面加载、崩溃报告 | 仅收集设备和应用信息 |
| 用户反馈数据 | 应用内反馈表单、App Store评论 | 评分、文字评论、功能建议 | 用户自愿提供，可关联用户ID |
| 生理数据 | HealthKit集成(可选) | 心率、睡眠数据(需用户授权) | 本地存储，加密传输，明确授权 |
| 商业数据 | 应用内购买API、第三方分析 | 付费转化、订阅续订、收入 | 数据脱敏，聚合分析 |

### 5.2 数据处理流程

1. **数据采集**：
   - 前端埋点：在关键用户交互点植入事件跟踪代码
   - 后端日志：服务器API请求和响应日志
   - 第三方工具：集成Firebase、AppsFlyer等专业分析工具
   - 定期导出：App Store、支付平台数据定期导出

2. **数据存储与处理**：
   - 实时数据：存储在Redis等内存数据库，用于实时监控
   - 历史数据：存储在PostgreSQL，用于趋势分析
   - 数据清洗：每日凌晨进行数据去重、补全和标准化
   - 数据聚合：按日、周、月聚合指标数据

3. **数据分析与可视化**：
   - 实时监控面板：展示核心指标实时状态
   - 定期报告：自动生成日/周/月指标报告
   - 异常检测：设置指标阈值，异常时自动告警
   - 深度分析：针对关键指标变化进行归因分析

### 5.3 报告与沟通机制

| 报告类型 | 频率 | 受众 | 内容重点 | 交付方式 |
|---------|------|------|---------|----------|
| 实时监控 | 持续 | 技术团队 | 性能指标、崩溃率、活跃用户 | 监控大屏、即时告警 |
| 日报 | 每日 | 产品、运营团队 | DAU、新增用户、关键转化指标 | 邮件、Slack |
| 周报 | 每周 | 全体团队 | 周环比变化、用户行为分析、功能使用数据 | 会议、文档 |
| 月报 | 每月 | 管理层、全体团队 | 月度目标达成情况、关键趋势、战略调整建议 | 正式报告、演示 |
| 季度回顾 | 每季度 | 管理层、产品团队 | 季度目标评估、用户增长分析、竞品对比 | 战略会议、PPT |

### 5.4 指标应用与迭代机制

1. **目标设定与跟踪**：
   - 基于北极星指标分解季度和月度目标
   - 建立目标达成追踪表，每周更新进度
   - 当指标偏离目标20%以上时启动分析流程

2. **A/B测试框架**：
   - 新功能上线前进行小规模A/B测试
   - 每个测试明确主要评估指标和样本量
   - 测试结果需达到统计显著性才能推广

3. **指标驱动迭代**：
   - 产品迭代优先级部分基于指标表现
   - 低使用率功能进行优化或下架评估
   - 高价值用户行为路径强化和推广

4. **框架定期评审**：
   - 每季度评审指标框架适用性
   - 根据产品阶段调整指标权重
   - 新增重要功能对应的评估指标

通过本指标框架，静息(Serenity)团队将能够全面、系统地评估产品表现，及时发现问题并优化，确保产品持续为用户创造价值的同时实现业务目标。