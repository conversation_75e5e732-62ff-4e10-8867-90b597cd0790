# 用户故事地图 - 静息(Serenity)

## 1. 用户故事地图概述

静息(Serenity)应用的用户故事地图是对用户与产品交互过程的可视化表示，通过横向的用户活动流和纵向的任务分解，清晰展示用户如何通过应用完成目标。本故事地图基于目标用户画像和核心使用场景创建，旨在帮助团队理解用户需求优先级和功能实现顺序。

**地图用途**：
- 对齐团队对用户旅程的理解
- 规划功能开发优先级
- 识别功能间的依赖关系
- 确保产品完整性和用户体验连贯性
- 支持敏捷开发中的迭代规划

**创建依据**：
- 目标用户画像(主要针对25-40岁有压力和睡眠问题的都市专业人士)
- 核心使用场景(减压、改善睡眠、提升专注力)
- 产品功能需求(来自PRD文档)
- 市场竞品分析结果

## 2. 用户活动流(横向)

用户在静息应用中的主要活动流如下，按用户旅程顺序排列：

```mermaid
graph LR
    A[发现与安装] --> B[首次使用引导]
    B --> C[注册/登录]
    C --> D[设置个人资料与目标]
    D --> E[浏览与选择内容]
    E --> F[进行呼吸练习]
    F --> G[查看练习结果与统计]
    G --> H[调整与个性化]
    H --> I[持续使用与分享]
```

### 2.1 活动流详细说明

| 活动阶段 | 描述 | 用户目标 | 关键触点 |
|---------|------|---------|---------|
| **发现与安装** | 用户通过App Store或推荐了解并下载应用 | 找到解决压力/睡眠问题的工具 | App Store页面、推荐信息 |
| **首次使用引导** | 应用引导用户了解核心功能和使用方法 | 快速掌握应用基本操作 | 欢迎界面、功能引导、权限请求 |
| **注册/登录** | 用户创建账户或使用第三方账号登录 | 保存个人数据和练习进度 | 注册表单、第三方登录选项 |
| **设置个人资料与目标** | 用户填写基本信息并设定健康目标 | 获得个性化体验 | 资料表单、目标选择器、兴趣标签 |
| **浏览与选择内容** | 用户浏览呼吸练习和相关内容 | 找到适合当前需求的练习 | 首页推荐、分类浏览、搜索功能 |
| **进行呼吸练习** | 用户跟随引导完成所选呼吸练习 | 通过练习缓解压力/改善睡眠 | 练习界面、视觉引导、音频指导 |
| **查看练习结果与统计** | 用户查看练习数据和进步情况 | 了解练习效果和坚持情况 | 统计页面、进度图表、成就展示 |
| **调整与个性化** | 用户根据偏好调整应用设置 | 获得符合个人习惯的体验 | 设置页面、偏好选项、通知设置 |
| **持续使用与分享** | 用户定期使用并分享练习成果 | 维持健康习惯并获得社交认同 | 提醒通知、分享功能、社区互动 |

## 3. 用户任务分解(纵向)

针对每个用户活动阶段，分解为具体任务和子任务，形成完整的用户故事层级结构：

### 3.1 发现与安装
- 查看应用介绍和评价
- 了解应用功能和价值
- 下载并安装应用
- 打开应用并开始使用

### 3.2 首次使用引导
- 浏览欢迎界面
- 了解核心功能介绍
- 授予必要权限(通知、健康数据)
- 完成初始设置向导

### 3.3 注册/登录
- 选择注册方式(邮箱/社交账号)
- 填写注册信息
- 验证身份(如需要)
- 设置密码
- 登录已有账户
- 找回密码(如需要)

### 3.4 设置个人资料与目标
- 上传个人头像
- 填写基本信息(年龄、性别等)
- 选择健康目标(减压/睡眠/专注)
- 设置每日练习时长目标
- 选择偏好的练习类型
- 设置提醒时间

### 3.5 浏览与选择内容
- 查看首页推荐内容
- 浏览分类内容(按场景/难度/时长)
- 搜索特定练习
- 查看练习详情
- 预览练习内容
- 将练习添加到收藏

### 3.6 进行呼吸练习
- 开始所选练习
- 跟随视觉引导动画
- 聆听音频指导
- 调整练习参数(时长/节奏)
- 暂停/继续练习
- 完成练习并记录感受
- 跳过或提前结束练习

### 3.7 查看练习结果与统计
- 查看单次练习数据
- 浏览历史练习记录
- 查看练习趋势图表
- 查看达成的成就
- 查看睡眠质量分析(如适用)
- 导出健康数据(如适用)

### 3.8 调整与个性化
- 修改个人资料
- 更新健康目标
- 调整通知设置
- 更改应用主题和外观
- 管理订阅和付费选项
- 调整音频和视觉设置
- 清除缓存或数据

### 3.9 持续使用与分享
- 接收练习提醒
- 参与挑战活动
- 分享练习成果到社交平台
- 邀请好友使用应用
- 加入用户社区
- 提供应用反馈
- 参与用户调研

## 4. 故事优先级与版本映射

将用户故事按优先级和依赖关系分配到不同产品版本，与Roadmap中的版本规划保持一致：

### 4.1 故事优先级定义

- **Must Have (M)**: 核心必要功能，没有则产品无法使用
- **Should Have (S)**: 重要功能，显著提升用户体验
- **Could Have (C)**: 增强功能，锦上添花但非必需
- **Won't Have (W)**: 暂不考虑，未来可能加入

### 4.2 MVP版本(V1.0)故事映射

| 活动流 | 关键用户故事 | 优先级 | 验收标准 |
|-------|------------|--------|---------|
| **首次使用引导** | 作为新用户，我希望获得清晰的功能引导，以便快速了解如何使用应用 | M | 用户完成引导后能描述出至少3个核心功能 |
| **注册/登录** | 作为用户，我希望能用Apple账号快速登录，以便无需记忆额外密码 | M | 90%的用户能在3步内完成登录 |
| **设置目标** | 作为用户，我希望设置健康目标，以便应用提供个性化推荐 | M | 用户可成功选择至少一个健康目标并保存 |
| **浏览内容** | 作为用户，我希望按场景浏览练习，以便找到适合当前需求的内容 | M | 用户能在3次点击内找到并选择一个练习 |
| **进行练习** | 作为用户，我希望有视觉引导动画，以便跟随正确的呼吸节奏 | M | 用户能顺利完成一次5分钟的完整练习 |
| **基础统计** | 作为用户，我希望查看练习次数统计，以便了解自己的坚持情况 | S | 用户可在个人中心查看总练习次数和时长 |
| **设置提醒** | 作为用户，我希望设置每日提醒，以便养成练习习惯 | S | 用户可成功设置提醒并在指定时间收到通知 |

### 4.3 V2.0版本故事映射

| 活动流 | 关键用户故事 | 优先级 | 关联MVP故事 |
|-------|------------|--------|------------|
| **个性化计划** | 作为用户，我希望获得个性化练习计划，以便系统地达成健康目标 | M | 设置目标 |
| **睡眠模块** | 作为用户，我希望有专门的睡眠练习，以便改善睡眠质量 | M | 进行练习 |
| **详细统计** | 作为用户，我希望查看详细的练习数据和趋势，以便了解进步情况 | S | 基础统计 |
| **成就系统** | 作为用户，我希望获得练习成就，以便增加练习动力 | S | 基础统计 |
| **冥想课程** | 作为用户，我希望有简短冥想引导，以便结合呼吸练习放松 | C | 进行练习 |
| **社交分享** | 作为用户，我希望分享练习成果，以便获得社交认同 | C | 详细统计 |

### 4.4 V3.0版本故事映射

| 活动流 | 关键用户故事 | 优先级 | 关联前期故事 |
|-------|------------|--------|------------|
| **专家指导** | 作为用户，我希望获得专家指导内容，以便确保练习的科学性 | M | 进行练习 |
| **自定义练习** | 作为用户，我希望创建自定义呼吸模式，以便满足个人特殊需求 | S | 进行练习 |
| **睡眠分析** | 作为用户，我希望追踪和分析睡眠质量，以便了解练习效果 | M | 睡眠模块 |
| **Watch集成** | 作为用户，我希望在Apple Watch上使用应用，以便便捷地进行练习 | S | 进行练习 |
| **订阅系统** | 作为用户，我希望通过订阅解锁高级功能，以便获得更专业的体验 | M | 所有核心功能 |

### 4.5 V4.0版本故事映射

| 活动流 | 关键用户故事 | 优先级 | 关联前期故事 |
|-------|------------|--------|------------|
| **社区功能** | 作为用户，我希望加入练习社区，以便与他人分享经验和获得支持 | M | 社交分享 |
| **AI教练** | 作为用户，我希望获得AI个性化指导，以便根据我的情况调整练习建议 | M | 个性化计划 |
| **跨平台支持** | 作为用户，我希望在iPad上使用应用，以便获得更好的视觉体验 | S | Watch集成 |
| **家庭共享** | 作为用户，我希望与家人共享订阅，以便节省费用 | C | 订阅系统 |
| **企业版功能** | 作为企业用户，我希望为团队部署应用，以便提升员工心理健康 | C | 所有核心功能 |

### 4.6 用户故事依赖关系图

```mermaid
graph TD
    A[注册/登录] --> B[设置个人资料与目标]
    B --> C[浏览与选择内容]
    C --> D[进行呼吸练习]
    D --> E[查看练习结果与统计]
    E --> F[调整与个性化]
    F --> G[持续使用与分享]
    D --> H[睡眠模块]
    H --> I[睡眠分析]
    B --> J[个性化计划]
    E --> K[成就系统]
    K --> G
    C --> L[搜索功能]
    L --> M[收藏功能]
```

## 5. 故事地图使用指南

### 5.1 开发团队使用建议

- **冲刺规划**：每个冲刺从当前优先级最高的故事列中选取任务
- **依赖管理**：优先解决前置依赖故事，避免阻塞后续开发
- **估算依据**：基于故事复杂度和工作量进行开发时间估算
- **验收标准**：每个故事的验收标准应在开发前明确并达成共识
- **用户反馈**：定期收集用户反馈，调整故事优先级

### 5.2 优先级调整机制

- **每两周评审**：在迭代计划会议上重新评估故事优先级
- **数据驱动**：基于用户行为数据和转化率调整功能优先级
- **用户反馈**：高价值用户反馈可提升相关故事优先级
- **业务目标**：根据当前业务重点调整故事优先级
- **技术债务**：定期安排技术债务清理故事，保持产品健康度

### 5.3 故事拆分原则

当故事过大或过于复杂时，应拆分为更小的可执行故事：
- 遵循INVEST原则：Independent, Negotiable, Valuable, Estimable, Small, Testable
- 每个故事应能在一个冲刺内完成
- 确保拆分后的故事仍保持独立价值
- 保持一致的用户视角
- 拆分后重新评估优先级

通过本用户故事地图，产品团队可以清晰了解用户需求和功能实现顺序，确保开发工作始终围绕用户价值展开，同时支持灵活的迭代规划和优先级调整。