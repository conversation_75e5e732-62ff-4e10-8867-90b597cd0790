# 产品需求文档 (PRD) - 静息(Serenity)

## 1. 文档信息

### 1.1 版本历史
| 版本 | 日期 | 作者 | 变更说明 |
|------|------|------|----------|
| 1.0 | 2023-11-15 | 产品团队 | 初始版本 |

### 1.2 文档目的
本文档详细描述了"静息(Serenity)"iOS应用的产品需求，包括产品定位、目标用户、功能需求、交互流程等，为设计、开发和测试团队提供明确指导。

### 1.3 相关文档引用
- 产品原型图
- 竞品分析报告：Calm、Headspace等冥想类应用

## 2. 产品概述

### 2.1 产品名称与定位
- **中文名称**：静息
- **英文名称**：Serenity
- **产品定位**：一款帮助用户通过科学呼吸练习缓解压力、改善睡眠和提升专注力的健康类iOS应用。

### 2.2 产品愿景与使命
- **愿景**：成为用户日常心理健康管理的首选工具，通过简单有效的呼吸练习，帮助每个人实现身心平衡。
- **使命**：让科学的呼吸方法变得简单易行，随时随地为用户提供平静与放松。

### 2.3 价值主张与独特卖点(USP)
- **核心价值**：通过引导式呼吸练习，帮助用户快速缓解压力、改善睡眠质量、提升专注力。
- **独特卖点**：
  1. 基于科学研究的呼吸练习方案
  2. 简洁直观的视觉引导界面
  3. 个性化练习计划与进度追踪
  4. 结合冥想与睡眠场景的扩展功能

### 2.4 目标平台列表
- iOS (iPhone)：支持iOS 15.0及以上版本
- 未来可扩展至iPad和Apple Watch

### 2.5 产品核心假设
1. 用户愿意每天花5-15分钟进行呼吸练习
2. 视觉引导式呼吸练习比传统音频引导更有效
3. 持续使用可显著改善用户的心理健康状态
4. 用户愿意为高级功能订阅付费

### 2.6 商业模式概述
- **基础版**：免费提供基础呼吸练习和有限功能
- **高级版**：订阅制(月付/年付)提供全部练习内容、个性化计划、高级统计分析等功能
- **定价策略**：月付12元，年付98元，首月免费试用

## 3. 用户研究

### 3.1 目标用户画像

#### 3.1.1 人口统计特征
- **主要用户群**：25-40岁的都市白领
- **次要用户群**：高压学习的学生(18-24岁)、关注健康的中老年人(40-60岁)
- **性别比例**：男女比例约为4:6
- **收入水平**：中等及以上收入
- **教育程度**：大专及以上学历

#### 3.1.2 行为习惯与偏好
- 工作/学习压力大，经常感到焦虑
- 睡眠质量不佳
- 有自我提升意识，关注身心健康
- 习惯使用手机应用解决实际问题
- 碎片化时间使用APP(通勤、午休、睡前)

#### 3.1.3 核心需求与痛点
- **核心需求**：缓解压力、改善睡眠、提升专注力
- **痛点**：
  1. 工作/学习压力大，缺乏有效的放松方法
  2. 入睡困难，睡眠质量差
  3. 注意力不集中，效率低下
  4. 现有解决方案要么太复杂，要么效果不明显
  5. 没有足够时间进行长时间放松练习

#### 3.1.4 动机与目标
- **短期目标**：快速缓解当下的紧张和焦虑情绪
- **中期目标**：改善睡眠质量和日常专注力
- **长期目标**：建立健康的生活习惯，提升整体身心健康水平

### 3.2 用户场景分析

#### 3.2.1 核心使用场景详述

**场景一：工作间隙放松**
- **场景描述**：下午3点，用户在办公室感到疲劳和注意力不集中，打开Serenity进行5分钟的快速呼吸练习。
- **用户目标**：快速恢复精力，提升工作效率
- **使用流程**：打开APP → 选择"快速练习" → 完成4-7-8呼吸法 → 返回工作

**场景二：睡前助眠**
- **场景描述**：晚上10点，用户躺在床上难以入睡，打开Serenity选择睡眠呼吸练习。
- **用户目标**：放松身心，快速入睡
- **使用流程**：打开APP → 进入"睡眠"标签 → 选择"睡前呼吸" → 跟随引导完成练习

**场景三：考前减压**
- **场景描述**：考试前15分钟，用户感到紧张焦虑，使用Serenity进行简短的减压呼吸练习。
- **用户目标**：缓解焦虑，保持冷静
- **使用流程**：打开APP → 选择"减压"分类 → 进行"交替鼻孔呼吸"练习

#### 3.2.2 边缘使用场景考量
- **通勤场景**：地铁/公交上，用户利用通勤时间进行简短呼吸练习
- **会议前准备**：重要会议前，用户通过APP快速调整状态
- **运动后恢复**：运动后，用户使用呼吸练习帮助身体恢复
- **亲子场景**：家长引导孩子使用简单呼吸练习缓解学习压力

### 3.3 用户调研洞察
- 85%的受访者表示在感到压力时没有有效的即时缓解方法
- 72%的受访者愿意尝试通过手机APP改善睡眠质量
- 68%的受访者认为视觉引导比纯音频引导更有助于集中注意力
- 90%的受访者希望练习时长可以灵活选择(5分钟-30分钟)
- 主要竞争对手的用户反馈显示，简洁的界面和明确的效果是用户最看重的因素

## 4. 市场与竞品分析

### 4.1 市场规模与增长预测
- 全球心理健康APP市场规模2023年约为100亿美元，预计年增长率15-20%
- 中国心理健康市场规模约为500亿元，年增长率超过25%
- 呼吸冥想类细分市场增速高于整体心理健康市场，年增长率约30%
- 疫情后，用户对心理健康的关注度显著提升，市场需求持续增长

### 4.2 行业趋势分析
- **个性化**：基于用户数据和偏好提供个性化内容推荐
- **科学化**：结合神经科学和心理学研究成果开发练习方案
- **场景化**：针对特定场景(睡眠、工作、运动)设计专用功能
- **社区化**：引入社交元素，增强用户粘性和参与度
- **多模态交互**：结合视觉、音频、触觉等多种引导方式
- **可穿戴设备集成**：与智能手表等设备同步，提供更全面的健康数据

### 4.3 竞争格局分析

#### 4.3.1 直接竞争对手详析

**Calm**
- **优势**：品牌知名度高，内容丰富，名人合作多，有儿童版
- **劣势**：功能复杂，部分用户反馈引导过于冗长，本地化不足
- **定价**：月付28元，年付198元
- **核心特性**：睡眠故事，冥想课程，呼吸练习，正念指导

**Headspace**
- **优势**：科学背书强，教学体系完善，动画设计优秀
- **劣势**：内容相对单一，价格较高
- **定价**：月付30元，年付218元
- **核心特性**：冥想课程，睡眠内容，专注力训练，运动恢复

**潮汐(Tide)**
- **优势**：本地化做得好，自然声音丰富，界面简洁
- **劣势**：功能相对简单，缺乏个性化推荐
- **定价**：免费+内购，高级版月付12元，年付98元
- **核心特性**：专注计时，自然声音，简单呼吸引导

#### 4.3.2 间接竞争对手概述
- **Keep等健身APP**：包含部分冥想放松内容
- **SleepTown等睡眠APP**：专注睡眠追踪和改善
- **Forest等专注APP**：帮助用户专注工作学习
- **喜马拉雅等音频平台**：提供冥想放松类音频内容

### 4.4 竞品功能对比
| 功能 | 静息(Serenity) | Calm | Headspace | 潮汐 |
|------|---------------|------|-----------|------|
| 呼吸练习引导 | ✓ (核心功能) | ✓ | ✓ | ✓ |
| 视觉呼吸动画 | ✓ (特色) | ○ | ○ | ✓ |
| 冥想课程 | ✓ | ✓ (丰富) | ✓ (丰富) | ○ |
| 睡眠故事 | ✓ (基础) | ✓ (丰富) | ✓ | ✓ |
| 自然声音 | ✓ | ✓ | ✓ | ✓ (丰富) |
| 个性化计划 | ✓ | ✓ | ✓ | ○ |
| 进度追踪 | ✓ | ✓ | ✓ | ○ |
| 社区功能 | ○ (未来) | ✓ | ✓ | ○ |
| Apple Watch支持 | ○ (未来) | ✓ | ✓ | ○ |
| 价格 | 中 | 高 | 高 | 中 |

### 4.5 市场差异化策略
- **专注呼吸**：相比竞品的全而杂，专注于呼吸练习这一核心功能
- **视觉引导**：更直观的动态视觉引导，提升练习体验和效果
- **简洁设计**：极简界面设计，降低使用门槛，让用户专注于练习本身
- **场景细分**：针对不同场景(工作、睡眠、学习)设计专用呼吸方案
- **科学背书**：与心理学和神经科学专家合作，确保练习方法的科学性
- **合理定价**：提供更具竞争力的价格，扩大用户群体

## 5. 产品功能需求

### 5.1 功能架构与模块划分

```mermaid
graph TD
    A[静息(Serenity)] --> B[首页]
    A --> C[呼吸练习]
    A --> D[冥想]
    A --> E[睡眠]
    A --> F[个人中心]
    
    B --> B1[推荐练习]
    B --> B2[快速开始]
    B --> B3[最近练习]
    B --> B4[练习统计]
    
    C --> C1[基础呼吸法]
    C --> C2[减压呼吸法]
    C --> C3[专注呼吸法]
    C --> C4[睡眠呼吸法]
    C --> C5[自定义呼吸]
    
    D --> D1[引导冥想]
    D --> D2[呼吸冥想]
    D --> D3[正念练习]
    D --> D4[冥想音乐]
    
    E --> E1[睡前放松]
    E --> E2[睡眠故事]
    E --> E3[自然声音]
    E --> E4[睡眠统计]
    
    F --> F1[个人资料]
    F --> F2[练习记录]
    F --> F3[设置]
    F --> F4[订阅管理]
    F --> F5[帮助与反馈]
```

### 5.2 核心功能详述

#### 5.2.1 呼吸练习模块

**功能描述**：作为用户，我想要多种呼吸练习方案，以便根据不同需求选择合适的呼吸方法。

**用户价值**：提供科学有效的呼吸练习，帮助用户缓解压力、提升专注力、改善睡眠。

**功能逻辑与规则**：
1. 提供至少8种不同的呼吸练习方法，包括：
   - 腹式呼吸法(Diaphragmatic Breathing)
   - 等长呼吸法(Equal Breathing)
   - 交替鼻孔呼吸法(Alternate Nostril Breathing)
   - 4-7-8呼吸法(4-7-8 Breathing)
   - 方盒呼吸法(Box Breathing)
   - 蜂鸣呼吸法(Bhramari/bee Breathing)
   - 叹气呼吸法(Sigh Breathing)
   - 自定义呼吸法

2. 每种呼吸法包含：
   - 详细说明和科学依据
   - 适用场景和益处
   - 分步指导
   - 视觉动画引导
   - 可调节的练习时长(1-30分钟)
   - 背景声音选择

3. 练习流程：
   - 选择呼吸法 → 设置时长和背景音 → 开始练习 → 跟随动画引导 → 完成后显示统计和反馈

**交互要求**：
- 主界面使用圆形动画作为呼吸引导，直观展示吸气、屏息和呼气阶段
- 支持触摸屏幕跟随引导或自动播放模式
- 提供大字体模式，方便视力不佳用户使用
- 练习过程中可随时暂停、调整音量或提前结束

**数据需求**：
- 记录每次练习的类型、时长、完成情况
- 存储用户偏好设置(常用呼吸法、背景音、时长等)
- 统计练习频率和累计时长

**技术依赖**：
- 动画渲染系统
- 音频播放系统
- 本地数据存储

**验收标准**：
1. 用户可在3步内开始任意呼吸练习
2. 动画引导与呼吸节奏同步精度误差不超过0.2秒
3. 支持后台音频播放，允许锁屏状态下继续练习
4. 应用在练习过程中保持稳定，无崩溃或卡顿现象
5. 所有呼吸法内容准确，符合科学原理

#### 5.2.2 个性化练习计划

**功能描述**：作为用户，我想要个性化的练习计划，以便系统地培养呼吸习惯并达到特定健康目标。

**用户价值**：提供结构化的练习路径，提高用户坚持度和练习效果。

**功能逻辑与规则**：
1. 目标设定：
   - 用户可选择预设目标(缓解压力、改善睡眠、提升专注力等)
   - 或自定义目标和期望达成时间

2. 计划生成：
   - 基于用户目标、经验水平和可用时间生成个性化计划
   - 计划周期为7天、14天或30天
   - 每天推荐1-2个适合的呼吸练习
   - 难度和时长随计划进展逐渐增加

3. 进度追踪：
   - 显示每日完成情况
   - 提供周/月进度概览
   - 完成阶段性目标给予成就奖励
   - 连续练习天数统计和鼓励

**交互要求**：
- 计划首页清晰展示当日任务和总体进度
- 完成每日练习后提供直观的完成反馈
- 支持调整计划难度或跳过不适合的练习
- 提供计划提醒功能

**数据需求**：
- 存储用户目标和偏好设置
- 记录每日计划完成情况
- 计算并存储连续练习天数
- 保存用户成就和里程碑

**技术依赖**：
- 本地数据库
- 推送通知系统
- 数据统计与分析

**验收标准**：
1. 用户可在5分钟内完成目标设定并生成个性化计划
2. 系统能根据用户完成情况动态调整后续推荐
3. 计划提醒准确率达100%
4. 数据统计准确，连续练习天数计算无误
5. 80%的用户能坚持完成7天以上的练习计划

#### 5.2.3 睡眠辅助模块

**功能描述**：作为用户，我想要专门的睡眠呼吸练习和辅助功能，以便改善睡眠质量和解决入睡困难问题。

**用户价值**：提供针对性的睡眠解决方案，帮助用户快速入睡并提高睡眠质量。

**功能逻辑与规则**：
1. 睡眠呼吸练习：
   - 专门设计的睡前呼吸法(4-7-8呼吸法、延长呼气呼吸法等)
   - 渐进式放松引导
   - 练习时长建议(5-20分钟)
   - 自动结束功能

2. 睡眠声音：
   - 自然声音(雨声、海浪、森林等)
   - 白噪音(白噪音、粉噪音、棕色噪音)
   - 环境声音(咖啡馆、壁炉、火车等)
   - 冥想音乐(轻柔钢琴曲、冥想钵等)
   - 声音混合功能，允许用户组合多种声音
   - 定时关闭功能

3. 睡眠故事：
   - 专业录制的睡前故事
   - 语速缓慢、语调柔和
   - 内容积极平和
   - 支持后台播放

4. 睡眠统计：
   - 记录入睡时间和睡眠时长
   - 统计使用频率和效果反馈
   - 生成睡眠改善报告

**交互要求**：
- 睡眠模式界面采用更暗的色调和更大的按钮
- 支持一键进入"快速助眠"模式
- 练习过程中屏幕亮度逐渐降低
- 支持设置睡眠定时器
- 提供夜间模式，减少蓝光

**数据需求**：
- 记录睡眠练习使用情况
- 存储用户偏好的睡眠声音和设置
- 收集用户睡眠质量反馈
- 统计睡眠改善趋势

**技术依赖**：
- 音频混合与播放系统
- 屏幕亮度控制
- 定时器功能
- 健康数据集成(可选，与HealthKit集成)

**验收标准**：
1. 睡眠声音播放稳定，无中断或卡顿
2. 所有睡眠功能可在后台运行
3. 定时关闭功能准确率100%
4. 睡眠统计数据准确，趋势分析合理
5. 用户反馈入睡时间平均缩短10分钟以上

### 5.3 次要功能描述

#### 5.3.1 冥想引导
提供基础的冥想引导课程，结合呼吸练习，帮助用户进入更深层次的放松状态。包括专注冥想、慈悲冥想、身体扫描等基础类型，时长5-20分钟不等。

#### 5.3.2 练习统计与报告
记录用户的练习历史，生成周/月/年统计报告，展示练习频率、时长、偏好的练习类型等数据，并提供个性化的改进建议。

#### 5.3.3 提醒功能
允许用户设置练习提醒，支持每日固定时间提醒、智能提醒(根据用户习惯推荐最佳练习时间)和场景提醒(如睡前、工作间隙等)。

#### 5.3.4 成就系统
设置不同类型的成就徽章，鼓励用户坚持练习。包括连续练习成就、总时长成就、探索成就等，增强用户粘性。

#### 5.3.5 分享功能
允许用户分享自己的练习成就、统计数据或喜欢的练习方法到社交媒体，或与好友互相鼓励监督。

### 5.4 未来功能储备(backlog)
1. **社区功能**：用户可以加入兴趣小组，分享经验，互相鼓励
2. **专家指导**：邀请专业冥想导师和心理学家提供付费指导课程
3. **可穿戴设备集成**：与Apple Watch等设备集成，监测心率等生理指标，优化练习体验
4. **AI个性化**：基于AI算法分析用户数据，提供更精准的个性化推荐
5. **儿童模式**：专门为儿童设计的简单有趣的呼吸练习，帮助儿童缓解压力和改善睡眠
6. **企业版**：为企业提供团队心理健康解决方案，包含团队数据和管理功能

## 6. 用户流程与交互设计指导

### 6.1 核心用户旅程地图

**首次使用旅程**
```mermaid
graph LR
    A[下载并打开APP] --> B[欢迎界面]
    B --> C[创建账户/登录]
    C --> D[目标选择问卷]
    D --> E[个性化推荐]
    E --> F[首页]
    F --> G[开始首次练习]
    G --> H[完成练习]
    H --> I[反馈与鼓励]
    I --> F
```

**日常使用旅程**
```mermaid
graph LR
    A[打开APP] --> B[首页/今日推荐]
    B --> C{选择操作}
    C --> D[继续个性化计划]
    C --> E[选择特定练习]
    C --> F[查看统计报告]
    D --> G[完成练习]
    E --> G
    G --> H[记录反馈]
    H --> B
```

### 6.2 关键流程详述与状态转换图

**呼吸练习流程**
```mermaid
graph TD
    A[选择呼吸练习] --> B[设置练习参数]
    B --> C[开始练习]
    C --> D{练习中}
    D --> E[吸气阶段]
    D --> F[屏息阶段]
    D --> G[呼气阶段]
    E --> F
    F --> G
    G --> D
    D --> H{完成/中断}
    H --> I[显示练习结果]
    I --> J[记录反馈]
    J --> K[返回首页]
```

**状态转换说明**：
- 吸气阶段：动画圆扩大，提示"吸气"
- 屏息阶段：动画圆保持最大状态，提示"屏息"
- 呼气阶段：动画圆缩小，提示"呼气"
- 各阶段时长根据所选呼吸法自动调整
- 支持暂停/继续/放弃操作

### 6.3 对设计师(UI/UX Agent)的界面原型参考说明和要求

**整体设计风格**：
- 简洁、现代、平静的设计语言
- 主色调：深蓝色(#1A2151)、青绿色(#2EC4B6)、深紫色(#4D4C7D)
- 辅助色：柔和的绿色、蓝色渐变
- 中性色：深灰(#2E2E3A)、中灰(#6B7280)、浅灰(#F3F4F6)、白色(#FFFFFF)
- 圆角设计：所有卡片和按钮使用16-24px的圆角
- 阴影效果：轻微阴影，增强层次感但不过度

**关键界面设计要求**：

1. **首页**：
   - 顶部显示用户信息和今日状态
   - 中部为"快速开始"按钮，突出显示
   - 下方为个性化推荐练习和继续上次练习
   - 底部为练习统计概览(连续天数、本周时长等)
   - 整体布局清晰，信息层级分明

2. **呼吸练习界面**：
   - 中央为大型呼吸引导动画圆，占据主要视觉空间
   - 动画圆颜色使用主色调渐变，吸气时变亮，呼气时变暗
   - 动画圆下方显示当前阶段(吸气/屏息/呼气)和倒计时
   - 顶部显示练习名称和剩余时间
   - 底部为控制按钮(暂停/调整音量/放弃)
   - 背景使用渐变或微妙的纹理，避免视觉疲劳

3. **导航设计**：
   - 使用底部标签栏导航，包含4个主要标签：首页、呼吸、冥想、睡眠、个人
   - 标签图标使用简洁的线性图标，选中状态使用主色调
   - 支持左右滑动切换主要标签
   - 练习过程中提供简化导航，避免干扰

4. **字体要求**：
   - 标题：SF Pro Display, 粗体
   - 正文：SF Pro Text, 常规
   - 按钮文本：SF Pro Text, 中等
   - 辅助文字：SF Pro Text, 轻量
   - 字号层级清晰，确保可读性

### 6.4 交互设计规范与原则建议

**核心交互原则**：
1. **直观性**：所有功能操作符合用户直觉，减少学习成本
2. **一致性**：交互模式在整个应用中保持一致
3. **反馈性**：所有用户操作都有明确的视觉或听觉反馈
4. **可访问性**：支持VoiceOver，提供足够的颜色对比度
5. **简约性**：每个界面只包含必要元素，避免干扰
6. **容错性**：允许用户犯错并轻松恢复

**具体交互规范**：
- 按钮点击反馈：轻微缩放(0.95倍)和颜色变化
- 页面切换：使用淡入淡出或滑动过渡动画
- 加载状态：使用柔和的骨架屏或进度指示器
- 错误提示：友好的错误信息和解决方案建议
- 空状态：提供引导性内容和操作建议
- 下拉刷新：用于更新内容列表
- 长按操作：用于显示更多选项

## 7. 非功能需求

### 7.1 性能需求
- **响应时间**：所有界面切换和操作响应时间<0.5秒
- **启动时间**：冷启动时间<2秒，热启动时间<1秒
- **动画流畅度**：呼吸引导动画保持60fps稳定帧率
- **后台播放**：支持音频在后台稳定播放，无中断
- **内存占用**：正常使用时内存占用<100MB
- **电池消耗**：连续使用1小时电池消耗<15%
- **网络要求**：基础功能支持离线使用，仅内容更新需要网络

### 7.2 安全需求
- **数据加密**：用户数据本地加密存储
- **隐私保护**：明确的隐私政策，不收集不必要的用户信息
- **认证授权**：支持Touch ID/Face ID登录选项
- **儿童保护**：如添加儿童模式，需符合相关儿童隐私保护法规
- **内容安全**：所有引导内容需经过专业审核，确保科学性和安全性
- **防沉迷**：设置使用时长提醒功能

### 7.3 数据统计与分析需求
- **核心事件跟踪**：
  - 应用启动次数和时长
  - 各功能模块使用频率
  - 练习完成率和放弃率
  - 用户留存率(日/周/月)
  - 转化率(免费至付费)
- **用户行为分析**：
  - 用户路径分析
  - 功能使用偏好
  - 练习时长和频率分布
  - A/B测试支持
- **数据展示**：
  - 用户端：个人练习统计和趋势
  - 管理端：综合数据分析仪表盘
- **数据隐私**：
  - 所有统计数据匿名化处理
  - 明确告知用户数据收集目的
  - 提供数据导出和删除功能

## 8. 技术架构考量

### 8.1 技术栈建议
- **开发语言**：Swift 5
- **UI框架**：SwiftUI为主，部分复杂界面可使用UIKit
- **状态管理**：Combine框架
- **本地存储**：Core Data/Realm
- **网络请求**：Alamofire
- **动画**：SwiftUI动画+部分自定义动画
- **音频处理**：AVFoundation
- **分析工具**：Firebase Analytics/AppsFlyer
- **崩溃监控**：Crashlytics
- **CI/CD**：Fastlane, GitHub Actions

### 8.2 系统集成需求
- **HealthKit集成**：可选，允许用户将练习数据同步到健康应用
- **通知中心**：本地通知和远程通知
- **后台任务**：支持后台音频播放和定时提醒
- **iCloud同步**：可选，支持多设备数据同步
- **App Tracking Transparency**：符合iOS 14+隐私要求
- **深度链接**：支持从外部链接直接打开特定练习

### 8.3 技术依赖与约束
- **最低系统版本**：iOS 15.0
- **设备支持**：iPhone 8及以上机型
- **屏幕适配**：支持所有iPhone屏幕尺寸，包括刘海屏
- **横屏支持**：主要功能需支持横屏模式
- **国际化**：支持中文(简/繁)和英文
- **本地化**：支持中国地区特有的功能和内容
- **性能约束**：动画渲染需优化以保证低性能设备流畅运行
- **内容更新**：支持远程内容更新，无需应用版本更新

### 8.4 数据模型建议

**核心数据实体**：

1. **User(用户)**
   - userId: String (主键)
   - name: String
   - email: String
   - avatar: Data/URL
   - joinDate: Date
   - subscriptionStatus: Enum
   - subscriptionExpiry: Date
   - preferences: [Preference]

2. **BreathingExercise(呼吸练习)**
   - exerciseId: String (主键)
   - name: String
   - description: String
   - instructions: [String]
   - benefits: [String]
   - difficulty: Enum
   - durationOptions: [Int]
   - breathingPattern: [BreathingPhase]
   - category: [Category]
   - imageUrl: String
   - audioUrl: String?

3. **BreathingPhase(呼吸阶段)**
   - phaseType: Enum (吸气/屏息/呼气)
   - duration: Int (秒)
   - instructions: String

4. **PracticeSession(练习会话)**
   - sessionId: String (主键)
   - userId: String (外键)
   - exerciseId: String (外键)
   - startTime: Date
   - endTime: Date
   - duration: Int
   - completionStatus: Enum
   - feedback: Int? (1-5星)
   - notes: String?

5. **PersonalPlan(个性化计划)**
   - planId: String (主键)
   - userId: String (外键)
   - goal: String
   - startDate: Date
   - endDate: Date
   - dailyExercises: [DailyExercise]
   - completionRate: Float

## 9. 验收标准汇总

### 9.1 功能验收标准矩阵
| 功能模块 | 验收标准 | 优先级 |
|---------|---------|--------|
| 呼吸练习 | 提供至少8种呼吸练习方法，动画引导准确，支持自定义时长 | P0 |
| 个性化计划 | 可生成7/14/30天计划，支持目标设定和进度追踪 | P0 |
| 睡眠辅助 | 包含至少5种睡眠呼吸练习，10种背景声音，支持定时关闭 | P0 |
| 冥想引导 | 提供至少5种基础冥想课程，时长5-20分钟 | P1 |
| 统计报告 | 准确记录练习数据，生成周/月报告，显示趋势 | P1 |
| 提醒功能 | 支持多种提醒类型，准确率100% | P1 |
| 成就系统 | 包含至少10种成就，触发条件合理 | P2 |
| 分享功能 | 支持分享到主流社交平台，内容格式美观 | P2 |

### 9.2 性能验收标准
- 应用启动时间：冷启动<2秒，热启动<1秒
- 界面响应时间：所有操作<0.5秒
- 动画帧率：呼吸引导动画稳定60fps
- 内存占用：正常使用<100MB，峰值<150MB
- 电池消耗：连续使用1小时<15%
- 稳定性：连续使用24小时无崩溃
- 兼容性：在支持的所有iOS版本和设备上正常运行

### 9.3 质量验收标准
- **Bug密度**：每千行代码<0.5个严重Bug
- **测试覆盖率**：单元测试覆盖率>70%，UI测试覆盖率>50%
- **用户体验**：完成核心任务的步骤<3步
- **可访问性**：通过iOS可访问性测试
- **本地化**：所有文本准确翻译，符合目标语言习惯
- **安全审计**：通过第三方安全审计，无高危漏洞
- **用户满意度**：内测用户满意度>4.5星(5星制)

## 10. 产品成功指标

### 10.1 关键绩效指标(KPIs)定义与目标
| KPI | 定义 | 3个月目标 | 6个月目标 | 12个月目标 |
|-----|------|----------|----------|-----------|
| 下载量 | 应用总下载次数 | 10,000 | 50,000 | 200,000 |
| 日活跃用户 | 每日打开应用的独立用户数 | 2,000 | 10,000 | 40,000 |
| 月活跃用户 | 每月打开应用的独立用户数 | 5,000 | 25,000 | 100,000 |
| 用户留存率 | 7天留存率 | 30% | 40% | 50% |
| | 30天留存率 | 20% | 30% | 40% |
| | 90天留存率 | 10% | 20% | 30% |
| 平均使用时长 | 用户每次使用的平均时间 | 5分钟 | 8分钟 | 10分钟 |
| 月均使用次数 | 用户每月平均使用次数 | 8次 | 12次 | 15次 |
| 付费转化率 | 免费用户转为付费用户的比例 | 2% | 4% | 6% |
| 续订率 | 订阅到期后选择续订的比例 | 60% | 70% | 80% |
| 用户评分 | App Store平均评分 | 4.0星 | 4.5星 | 4.7星 |

### 10.2 北极星指标定义与选择依据

**北极星指标**：月活跃用户的月均练习完成次数

**定义**：每月活跃用户平均完成的呼吸练习次数

**当前基准**：N/A(新产品)

**3个月目标**：8次/月/用户
**6个月目标**：12次/月/用户
**12个月目标**：15次/月/用户

**选择依据**：
1. 直接反映用户对核心价值的获取程度
2. 与用户留存率和付费意愿高度相关
3. 可通过产品设计和运营活动直接影响
4. 能够全面反映产品整体健康状况
5. 易于理解和沟通，团队全员可对齐

### 10.3 指标监测计划

**数据收集频率**：
- 日级指标：每日收集，次日更新仪表盘
- 周级指标：每周日汇总，次周一更新
- 月级指标：每月最后一天汇总，次月2日前更新

**报告机制**：
- 每日快报：核心日指标(DAU、新增用户、崩溃率)
- 周度报告：周指标回顾、环比变化、异常分析
- 月度报告：全面指标回顾、用户画像分析、留存分析、转化分析
- 季度战略回顾：与业务目标对齐，调整产品策略

**数据可视化**：
- 开发内部数据仪表盘，实时展示核心指标
- 为不同角色定制数据视图(产品、运营、开发)
- 设置关键指标预警机制

**用户反馈收集**：
- 应用内反馈入口
- 应用商店评论监控
- 定期用户调研(季度)
- 核心用户访谈(月度)

通过以上指标监测计划，确保产品团队能够及时了解产品表现，发现问题并调整策略，持续优化产品体验。